page 50061 "Posted Transfer Receipt Line"
{
    Caption = 'Posted Transfer Receipt Lines Veiw';
    Editable = false;
    PageType = List;
    SourceTable = "Transfer Receipt Line";
    UsageCategory = Lists;
    ApplicationArea = all;
    layout
    {
        area(content)
        {
            repeater(Control1)
            {
                ShowCaption = false;
                field("Document No."; "Document No.")
                {
                    ApplicationArea = Location;
                    HideValue = DocumentNoHideValue;
                    StyleExpr = 'Strong';
                    ToolTip = 'Specifies the document number associated with this transfer line.';
                }
                field("Item No."; "Item No.")
                {
                    ApplicationArea = Location;
                    ToolTip = 'Specifies the number of the item that you want to transfer.';
                }
                field(Description; Description)
                {
                    ApplicationArea = Location;
                    ToolTip = 'Specifies the description of the item being transferred.';
                }
                //>>> 190325 G2S CAS-01410-P8Z9W6
                field("Production Batch No."; "Production Batch No.")
                {
                    ApplicationArea = Location;
                    ToolTip = 'Specifies the Production Batch No..';
                } //>>> 190325 G2S CAS-01410-P8Z9W6
                field(Quantity; Quantity)
                {
                    ApplicationArea = Location;
                    ToolTip = 'Specifies the quantity of the item specified on the line.';
                }
                field("Unit of Measure"; "Unit of Measure")
                {
                    ApplicationArea = Location;
                    ToolTip = 'Specifies the name of the item or resource''s unit of measure, such as piece or hour.';
                }
                field("Receipt Date"; "Receipt Date")
                {
                    ApplicationArea = Location;
                    ToolTip = 'Specifies the receipt date of the transfer receipt line.';
                }
                field("Transfer-from Code"; "Transfer-from Code")
                {
                    ApplicationArea = Location;
                    ToolTip = 'Specifies the receipt date of the transfer receipt line.';
                }
                field("Transfer-to Code"; "Transfer-to Code")
                {
                    ApplicationArea = Location;
                    ToolTip = 'Specifies the receipt date of the transfer receipt line.';
                }
                field("Transfer Order No."; "Transfer Order No.")
                {
                    ApplicationArea = Location;
                    ToolTip = 'Specifies the receipt date of the transfer receipt line.';
                }
            }
        }
        area(factboxes)
        {
            systempart(Control1900383207; Links)
            {
                ApplicationArea = RecordLinks;
                Visible = false;
            }
            systempart(Control1905767507; Notes)
            {
                ApplicationArea = Notes;
                Visible = false;
            }
        }
    }

    actions
    {
        area(navigation)
        {
            group("&Line")
            {
                Caption = '&Line';
                Image = Line;
                action("Show Document")
                {
                    ApplicationArea = Location;
                    Caption = 'Show Document';
                    Image = View;
                    ShortCutKey = 'Shift+F7';
                    ToolTip = 'Open the document that the selected line exists on.';

                    trigger OnAction()
                    var
                        TransRcptHeader: Record "Transfer Receipt Header";
                    begin
                        TransRcptHeader.Get("Document No.");
                        PAGE.Run(PAGE::"Posted Transfer Receipt", TransRcptHeader);
                    end;
                }
                action(Dimensions)
                {
                    AccessByPermission = TableData Dimension = R;
                    ApplicationArea = Dimensions;
                    Caption = 'Dimensions';
                    Image = Dimensions;
                    ShortCutKey = 'Alt+D';
                    ToolTip = 'View or edit dimensions, such as area, project, or department, that you can assign to sales and purchase documents to distribute costs and analyze transaction history.';

                    trigger OnAction()
                    begin
                        ShowDimensions;
                    end;
                }
            }
        }
    }

    trigger OnAfterGetRecord()
    begin
        DocumentNoHideValue := false;
        DocumentNoOnFormat;
    end;

    trigger OnQueryClosePage(CloseAction: Action): Boolean
    begin
        if CloseAction = ACTION::LookupOK then
            LookupOKOnPush;
    end;

    var
        FromTransRcptLine: Record "Transfer Receipt Line";
        TempTransRcptLine: Record "Transfer Receipt Line" temporary;
        ItemChargeAssgntPurch: Record "Item Charge Assignment (Purch)";
        AssignItemChargePurch: Codeunit "Item Charge Assgnt. (Purch.)";
        UnitCost: Decimal;
        CreateCostDistrib: Boolean;
        [InDataSet]
        DocumentNoHideValue: Boolean;

    procedure Initialize(NewItemChargeAssgntPurch: Record "Item Charge Assignment (Purch)"; NewUnitCost: Decimal)
    begin
        ItemChargeAssgntPurch := NewItemChargeAssgntPurch;
        UnitCost := NewUnitCost;
        CreateCostDistrib := true;
    end;

    local procedure IsFirstLine(DocNo: Code[20]; LineNo: Integer): Boolean
    var
        TransRcptLine: Record "Transfer Receipt Line";
    begin
        TempTransRcptLine.Reset();
        TempTransRcptLine.CopyFilters(Rec);
        TempTransRcptLine.SetRange("Document No.", DocNo);
        if not TempTransRcptLine.FindFirst then begin
            TransRcptLine.CopyFilters(Rec);
            TransRcptLine.SetRange("Document No.", DocNo);
            TransRcptLine.FindFirst;
            TempTransRcptLine := TransRcptLine;
            TempTransRcptLine.Insert();
        end;
        if TempTransRcptLine."Line No." = LineNo then
            exit(true);
    end;

    local procedure LookupOKOnPush()
    begin
        if CreateCostDistrib then begin
            FromTransRcptLine.Copy(Rec);
            CurrPage.SetSelectionFilter(FromTransRcptLine);
            if FromTransRcptLine.FindFirst then begin
                ItemChargeAssgntPurch."Unit Cost" := UnitCost;
                AssignItemChargePurch.CreateTransferRcptChargeAssgnt(FromTransRcptLine, ItemChargeAssgntPurch);
            end;
        end;
    end;

    local procedure DocumentNoOnFormat()
    begin
        if not IsFirstLine("Document No.", "Line No.") then
            DocumentNoHideValue := true;
    end;
}

