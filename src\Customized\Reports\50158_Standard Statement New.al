report 50158 "Standard Statement New"
{
    RDLCLayout = './CHIReports\Reports\Layout\StandardStatementN.rdl';
    //WordLayout = './CHIReports\Reports\Layout\StandardStatementn.docx';
    UsageCategory = ReportsAndAnalysis;
    ApplicationArea = all;
    Caption = 'Standard Statement New_50158';
    //DefaultLayout = Word;
    Permissions = tabledata Customer = rm;//B2B.P.K on 14.05.2021

    dataset
    {
        dataitem(Customer; Customer)
        {
            DataItemTableView = SORTING("No.");
            PrintOnlyIfDetail = true;
            RequestFilterFields = "No.", "Search Name", "Print Statements", "Currency Filter";
            column(No_Cust; "No.")
            {
            }
            dataitem("Integer"; "Integer")
            {
                DataItemTableView = SORTING(Number) WHERE(Number = CONST(1));
                PrintOnlyIfDetail = true;
                column(CompanyPicture; CompanyInfo.Picture)
                {
                }
                column(CompanyInfo1Picture; CompanyInfo1.Picture)
                {
                }
                column(CompanyInfo2Picture; CompanyInfo2.Picture)
                {
                }
                column(CompanyInfo3Picture; CompanyInfo3.Picture)
                {
                }
                column(CustAddr1; CustAddr[1])
                {
                }
                column(CompanyAddr1; CompanyAddr[1])
                {
                }
                column(CustAddr2; CustAddr[2])
                {
                }
                column(CompanyAddr2; CompanyAddr[2])
                {
                }
                column(CustAddr3; CustAddr[3])
                {
                }
                column(CompanyAddr3; CompanyAddr[3])
                {
                }
                column(CustAddr4; CustAddr[4])
                {
                }
                column(CompanyAddr4; CompanyAddr[4])
                {
                }
                column(CustAddr5; CustAddr[5])
                {
                }
                column(CompanyAddr5; CompanyAddr[5])
                {
                }
                column(PhoneNo_CompanyInfo; CompanyInfo."Phone No.")
                {
                }
                column(CustAddr6; CustAddr[6])
                {
                }
                column(CompanyAddr6; CompanyAddr[6])
                {
                }
                column(CompanyInfoEmail; CompanyInfo."E-Mail")
                {
                }
                column(CompanyInfoHomePage; CompanyInfo."Home Page")
                {
                }
                column(VATRegNo_CompanyInfo; CompanyInfo."VAT Registration No.")
                {
                }
                column(GiroNo_CompanyInfo; CompanyInfo."Giro No.")
                {
                }
                column(BankName_CompanyInfo; CompanyInfo."Bank Name")
                {
                }
                column(BankAccNo_CompanyInfo; CompanyInfo."Bank Account No.")
                {
                }
                column(No1_Cust; Customer."No.")
                {
                }
                column(TodayFormatted; Format(Today))
                {
                }
                column(StartDate; Format(StartDate))
                {
                }
                column(EndDate; Format(EndDate))
                {
                }
                column(LastStatmntNo_Cust; Format(Customer."Last Statement No."))
                {
                }
                column(CustAddr7; CustAddr[7])
                {
                }
                column(CustAddr8; CustAddr[8])
                {
                }
                column(CompanyAddr7; CompanyAddr[7])
                {
                }
                column(CompanyAddr8; CompanyAddr[8])
                {
                }
                column(StatementCaption; StatementCaptionLbl)
                {
                }
                column(PhoneNo_CompanyInfoCaption; PhoneNo_CompanyInfoCaptionLbl)
                {
                }
                column(VATRegNo_CompanyInfoCaption; VATRegNo_CompanyInfoCaptionLbl)
                {
                }
                column(GiroNo_CompanyInfoCaption; GiroNo_CompanyInfoCaptionLbl)
                {
                }
                column(BankName_CompanyInfoCaption; BankName_CompanyInfoCaptionLbl)
                {
                }
                column(BankAccNo_CompanyInfoCaption; BankAccNo_CompanyInfoCaptionLbl)
                {
                }
                column(No1_CustCaption; No1_CustCaptionLbl)
                {
                }
                column(StartDateCaption; StartDateCaptionLbl)
                {
                }
                column(EndDateCaption; EndDateCaptionLbl)
                {
                }
                column(LastStatmntNo_CustCaption; LastStatmntNo_CustCaptionLbl)
                {
                }
                column(PostDate_DtldCustLedgEntriesCaption; PostDate_DtldCustLedgEntriesCaptionLbl)
                {
                }
                column(DocNo_DtldCustLedgEntriesCaption; DtldCustLedgEntries.FieldCaption("Document No."))
                {
                }
                column(Desc_CustLedgEntry2Caption; CustLedgEntry2.FieldCaption(Description))
                {
                }
                column(DueDate_CustLedgEntry2Caption; DueDate_CustLedgEntry2CaptionLbl)
                {
                }
                column(RemainAmtCustLedgEntry2Caption; CustLedgEntry2.FieldCaption("Remaining Amount"))
                {
                }
                column(CustBalanceCaption; CustBalanceCaptionLbl)
                {
                }
                column(OriginalAmt_CustLedgEntry2Caption; CustLedgEntry2.FieldCaption("Original Amount"))
                {
                }
                column(CompanyInfoHomepageCaption; CompanyInfoHomepageCaptionLbl)
                {
                }
                column(CompanyInfoEmailCaption; CompanyInfoEmailCaptionLbl)
                {
                }
                column(DocDateCaption; DocDateCaptionLbl)
                {
                }
                column(CurrReportPageNoCaption; CurrReportPageNoCaptionLbl)
                {
                }
                column(CompanyLegalOffice; CompanyInfo.GetLegalOffice)
                {
                }
                column(CompanyLegalOffice_Lbl; CompanyInfo.GetLegalOfficeLbl)
                {
                }
                dataitem(CurrencyLoop; "Integer")
                {
                    DataItemTableView = SORTING(Number) WHERE(Number = FILTER(1 ..));
                    PrintOnlyIfDetail = true;
                    column(Total_Caption2; Total_CaptionLbl)
                    {
                    }
                    dataitem(CustLedgEntryHdr; "Integer")
                    {
                        DataItemTableView = SORTING(Number) WHERE(Number = CONST(1));
                        column(Currency2Code_CustLedgEntryHdr; StrSubstNo(EntriesLbl, CurrencyCode3))
                        {
                        }
                        column(StartBalance; StartBalance)
                        {
                            AutoFormatExpression = TempCurrency2.Code;
                            AutoFormatType = 1;
                        }
                        column(CurrencyCode3; CurrencyCode3)
                        {
                        }
                        column(CustBalance_CustLedgEntryHdr; CustBalance)
                        {
                        }
                        column(PrintLine; PrintLine)
                        {
                        }
                        column(DtldCustLedgEntryType; Format(DtldCustLedgEntries."Entry Type", 0, 2))
                        {
                        }
                        column(EntriesExists; EntriesExists)
                        {
                        }
                        column(IsNewCustCurrencyGroup; IsNewCustCurrencyGroup)
                        {
                        }
                        column(GrossAmount; GrossAmount)
                        {
                        }
                        column(FixedDiscAmt2; FixedDiscAmt2)
                        {
                        }
                        column(VariableDiscAmt2; VariableDiscAmt2)
                        {
                        }
                        dataitem(DtldCustLedgEntries; "Detailed Cust. Ledg. Entry")
                        {
                            DataItemTableView = SORTING("Customer No.", "Posting Date", "Entry Type", "Currency Code");
                            column(PostDate_DtldCustLedgEntries; Format("Posting Date"))
                            {
                            }
                            column(DocNo_DtldCustLedgEntries; "Document No.")
                            {
                            }
                            column(Description; Description)
                            {
                            }
                            column(DueDate_DtldCustLedgEntries; Format(DueDate))
                            {
                            }
                            column(CurrCode_DtldCustLedgEntries; "Currency Code")
                            {
                            }
                            column(Amt_DtldCustLedgEntries; Amount)
                            {
                                AutoFormatExpression = "Currency Code";
                                AutoFormatType = 1;
                            }
                            column(RemainAmt_DtldCustLedgEntries; RemainingAmount)
                            {
                                AutoFormatExpression = "Currency Code";
                                AutoFormatType = 1;
                            }
                            column(CustBalance; CustBalance)
                            {
                                AutoFormatExpression = "Currency Code";
                                AutoFormatType = 1;
                            }
                            column(Currency2Code; TempCurrency2.Code)
                            {
                            }
                            column(Description1; Description)
                            {
                            }

                            trigger OnAfterGetRecord()
                            begin
                                if SkipReversedUnapplied(DtldCustLedgEntries) or (Amount = 0) then
                                    CurrReport.Skip();
                                RemainingAmount := 0;
                                PrintLine := true;
                                case "Entry Type" of
                                    "Entry Type"::"Initial Entry":
                                        begin
                                            CustLedgerEntry.Get("Cust. Ledger Entry No.");
                                            //Description := CustLedgerEntry.Description; commented for RFC #26
                                            //added RFC #26
                                            Description := CustLedgerEntry."Description 2";

                                            if Description = '' then
                                                Description := CustLedgerEntry.Narration; // Add here

                                            // TEST case  NBJSI137584 NBJSI137568
                                            //if not (CustLedgerEntry."Document No." in ['NBJSI137584', 'NBJSI137568']) then CurrReport.Skip();
                                            UpdateVariables(CustLedgerEntry); // Fix  1

                                            if Description = '' then
                                                Description := CustLedgerEntry.Description;
                                            //ended RFC #26

                                            DueDate := CustLedgerEntry."Due Date";
                                            CustLedgerEntry.SetRange("Date Filter", 0D, EndDate);
                                            CustLedgerEntry.CalcFields("Remaining Amount");
                                            RemainingAmount := CustLedgerEntry."Remaining Amount";
                                            CustLedgerEntry.SetRange("Date Filter");
                                        end;
                                    "Entry Type"::Application:
                                        begin
                                            DetailedCustLedgEntry2.SetCurrentKey("Customer No.", "Posting Date", "Entry Type");
                                            DetailedCustLedgEntry2.SetRange("Customer No.", "Customer No.");
                                            DetailedCustLedgEntry2.SetRange("Posting Date", "Posting Date");
                                            DetailedCustLedgEntry2.SetRange("Entry Type", "Entry Type"::Application);
                                            DetailedCustLedgEntry2.SetRange("Transaction No.", "Transaction No.");
                                            DetailedCustLedgEntry2.SetFilter("Currency Code", '<>%1', "Currency Code");
                                            if not DetailedCustLedgEntry2.IsEmpty then begin
                                                Description := MulticurrencyAppLbl;
                                                DueDate := 0D;

                                                // TEST case  NBJSI137584 NBJSI137568
                                                //if not (CustLedgerEntry."Document No." in ['NBJSI137584', 'NBJSI137568']) then CurrReport.Skip();
                                                UpdateVariables(CustLedgerEntry); // 2
                                            end else
                                                CurrReport.Skip();
                                        end;
                                    "Entry Type"::"Payment Discount",
                                    "Entry Type"::"Payment Discount (VAT Excl.)",
                                    "Entry Type"::"Payment Discount (VAT Adjustment)",
                                    "Entry Type"::"Payment Discount Tolerance",
                                    "Entry Type"::"Payment Discount Tolerance (VAT Excl.)",
                                    "Entry Type"::"Payment Discount Tolerance (VAT Adjustment)":
                                        begin
                                            Description := PaymentDiscountLbl;
                                            DueDate := 0D;
                                        end;
                                    "Entry Type"::"Payment Tolerance",
                                    "Entry Type"::"Payment Tolerance (VAT Excl.)",
                                    "Entry Type"::"Payment Tolerance (VAT Adjustment)":
                                        begin
                                            Description := WriteoffsLbl;
                                            DueDate := 0D;
                                        end;
                                    "Entry Type"::"Appln. Rounding",
                                    "Entry Type"::"Correction of Remaining Amount":
                                        begin
                                            Description := RoundingLbl;
                                            DueDate := 0D;
                                        end;
                                end;

                                if PrintLine then begin
                                    CustBalance := CustBalance + Amount;
                                    IsNewCustCurrencyGroup := IsFirstPrintLine;
                                    IsFirstPrintLine := false;
                                end;
                            end;

                            trigger OnPreDataItem()
                            begin
                                SetRange("Customer No.", Customer."No.");
                                SetRange("Posting Date", StartDate, EndDate);
                                SetRange("Currency Code", TempCurrency2.Code);

                                if TempCurrency2.Code = '' then begin
                                    GLSetup.TestField("LCY Code");
                                    CurrencyCode3 := GLSetup."LCY Code"
                                end else
                                    CurrencyCode3 := TempCurrency2.Code;

                                IsFirstPrintLine := true;
                            end;
                        }
                    }
                    dataitem(CustLedgEntryFooter; "Integer")
                    {
                        DataItemTableView = SORTING(Number) WHERE(Number = CONST(1));
                        column(CurrencyCode3_CustLedgEntryFooter; CurrencyCode3)
                        {
                        }
                        column(Total_Caption; Total_CaptionLbl)
                        {
                        }
                        column(CustBalance_CustLedgEntryHdrFooter; CustBalance)
                        {
                            AutoFormatExpression = TempCurrency2.Code;
                            AutoFormatType = 1;
                        }
                        column(EntriesExistsl_CustLedgEntryFooterCaption; EntriesExists)
                        {
                        }
                    }
                    dataitem(OverdueVisible; "Integer")
                    {
                        DataItemTableView = SORTING(Number) WHERE(Number = CONST(1));
                        column(Total_Caption3; Total_CaptionLbl)
                        {
                        }
                        column(PostDate_DtldCustLedgEntriesCaption2; PostDate_DtldCustLedgEntriesCaptionLbl)
                        {
                        }
                        column(DocNo_DtldCustLedgEntriesCaption2; DtldCustLedgEntries.FieldCaption("Document No."))
                        {
                        }
                        column(Desc_CustLedgEntry2Caption2; CustLedgEntry2.FieldCaption(Description))
                        {
                        }
                        column(DueDate_CustLedgEntry2Caption2; DueDate_CustLedgEntry2CaptionLbl)
                        {
                        }
                        column(RemainAmtCustLedgEntry2Caption2; CustLedgEntry2.FieldCaption("Remaining Amount"))
                        {
                        }
                        column(OriginalAmt_CustLedgEntry2Caption2; CustLedgEntry2.FieldCaption("Original Amount"))
                        {
                        }
                        dataitem(CustLedgEntry2; "Cust. Ledger Entry")
                        {
                            DataItemLink = "Customer No." = FIELD("No.");
                            DataItemLinkReference = Customer;
                            DataItemTableView = SORTING("Customer No.", Open, Positive, "Due Date") where(amount = filter(> 0));//PKONJU16;
                            column(OverDueEntries; StrSubstNo(OverdueEntriesLbl, TempCurrency2.Code))
                            {
                            }
                            column(RemainAmt_CustLedgEntry2; "Remaining Amount")
                            {
                                AutoFormatExpression = "Currency Code";
                                AutoFormatType = 1;
                            }
                            column(PostDate_CustLedgEntry2; Format("Posting Date"))
                            {
                            }
                            column(DocNo_CustLedgEntry2; "Document No.")
                            {
                            }
                            column(Desc_CustLedgEntry2; Description)
                            {
                            }
                            column(DueDate_CustLedgEntry2; Format("Due Date"))
                            {
                            }
                            column(OriginalAmt_CustLedgEntry2; "Original Amount")
                            {
                                AutoFormatExpression = "Currency Code";
                            }
                            column(CurrCode_CustLedgEntry2; "Currency Code")
                            {
                            }
                            column(PrintEntriesDue; PrintEntriesDue)
                            {
                            }
                            column(Currency2Code_CustLedgEntry2; TempCurrency2.Code)
                            {
                            }
                            column(CurrencyCode3_CustLedgEntry2; CurrencyCode3)
                            {
                            }
                            column(CustNo_CustLedgEntry2; "Customer No.")
                            {
                            }

                            trigger OnAfterGetRecord()
                            var
                                CustLedgEntry: Record "Cust. Ledger Entry";
                            begin
                                if IncludeAgingBand then
                                    if ("Posting Date" > EndDate) and ("Due Date" >= EndDate) then
                                        CurrReport.Skip();
                                CustLedgEntry := CustLedgEntry2;
                                CustLedgEntry.SetRange("Date Filter", 0D, EndDate);
                                CustLedgEntry.CalcFields("Remaining Amount");
                                "Remaining Amount" := CustLedgEntry."Remaining Amount";
                                if CustLedgEntry."Remaining Amount" = 0 then
                                    CurrReport.Skip();

                                if "Due Date" >= EndDate then
                                    CurrReport.Skip();

                                CustBalance2 := CustBalance2 + CustLedgEntry."Remaining Amount";

                                // TEST case  NBJSI137584 NBJSI137568
                                //if not (CustLedgerEntry."Document No." in ['NBJSI137584', 'NBJSI137568']) then CurrReport.Skip();
                                UpdateVariables(CustLedgerEntry); // Fix 3
                            end;

                            trigger OnPreDataItem()
                            begin
                                if not IncludeAgingBand then
                                    SetRange("Due Date", 0D, EndDate - 1);
                                SetRange("Currency Code", TempCurrency2.Code);
                                if (not PrintEntriesDue) and (not IncludeAgingBand) then
                                    CurrReport.Break();
                            end;
                        }
                        dataitem(OverdueEntryFooder; "Integer")
                        {
                            DataItemTableView = SORTING(Number) WHERE(Number = CONST(1));
                            column(OverdueBalance; CustBalance2)
                            {
                            }
                        }

                        trigger OnPreDataItem()
                        begin
                            if not PrintEntriesDue then
                                CurrReport.Break();
                        end;
                    }

                    trigger OnAfterGetRecord()
                    begin
                        if Number = 1 then
                            TempCurrency2.Find('-');

                        repeat
                            if not IsFirstLoop then
                                IsFirstLoop := true
                            else
                                if TempCurrency2.Next = 0 then
                                    CurrReport.Break();
                            CustLedgerEntry.SetCurrentKey("Customer No.", "Posting Date", "Currency Code");
                            CustLedgerEntry.SetRange("Customer No.", Customer."No.");
                            CustLedgerEntry.SetRange("Posting Date", 0D, EndDate);
                            CustLedgerEntry.SetRange("Currency Code", TempCurrency2.Code);
                            EntriesExists := not CustLedgerEntry.IsEmpty;
                        until EntriesExists;
                        Cust2 := Customer;
                        Cust2.SetRange("Date Filter", 0D, StartDate - 1);
                        Cust2.SetRange("Currency Filter", TempCurrency2.Code);
                        Cust2.CalcFields("Net Change");
                        StartBalance := Cust2."Net Change";
                        CustBalance := Cust2."Net Change";
                        CustBalance2 := 0;
                    end;

                    trigger OnPreDataItem()
                    begin
                        Customer.CopyFilter("Currency Filter", TempCurrency2.Code);
                    end;
                }
                dataitem(AgingBandVisible; "Integer")
                {
                    DataItemTableView = SORTING(Number) WHERE(Number = CONST(1));
                    dataitem(AgingCustLedgEntry; "Cust. Ledger Entry")
                    {
                        DataItemLink = "Customer No." = FIELD("No.");
                        DataItemLinkReference = Customer;
                        DataItemTableView = SORTING("Customer No.", Open, Positive, "Due Date");

                        trigger OnAfterGetRecord()
                        var
                            CustLedgEntry: Record "Cust. Ledger Entry";
                        begin
                            if ("Posting Date" > EndDate) and ("Due Date" >= EndDate) then
                                CurrReport.Skip();
                            if DateChoice = DateChoice::"Due Date" then
                                if "Due Date" >= EndDate then
                                    CurrReport.Skip();
                            CustLedgEntry := AgingCustLedgEntry;
                            CustLedgEntry.SetRange("Date Filter", 0D, EndDate);
                            CustLedgEntry.CalcFields("Remaining Amount");
                            "Remaining Amount" := CustLedgEntry."Remaining Amount";
                            if CustLedgEntry."Remaining Amount" = 0 then
                                CurrReport.Skip();

                            if "Posting Date" <= EndDate then
                                UpdateBuffer("Currency Code", GetDate("Posting Date", "Due Date"), "Remaining Amount");


                            // TEST case  NBJSI137584 NBJSI137568
                            //if not (CustLedgerEntry."Document No." in ['NBJSI137584', 'NBJSI137568']) then CurrReport.Skip();
                            UpdateVariables(CustLedgerEntry); // Fix 4
                        end;

                        trigger OnPreDataItem()
                        begin
                            Customer.CopyFilter("Currency Filter", "Currency Code");
                            SetCurrentKey("Customer No.", "Posting Date", "Currency Code");
                            SetRange("Customer No.", Customer."No.");
                            SetRange("Posting Date", 0D, EndDate);
                        end;
                    }
                    dataitem(AgingBandLoop; "Integer")
                    {
                        DataItemTableView = SORTING(Number) WHERE(Number = FILTER(1 ..));
                        column(AgingDate1; Format(AgingDate[1] + 1))
                        {
                        }
                        column(AgingDate2; Format(AgingDate[2]))
                        {
                        }
                        column(AgingDate21; Format(AgingDate[2] + 1))
                        {
                        }
                        column(AgingDate3; Format(AgingDate[3]))
                        {
                        }
                        column(AgingDate31; Format(AgingDate[3] + 1))
                        {
                        }
                        column(AgingDate4; Format(AgingDate[4]))
                        {
                        }
                        column(AgingBandEndingDate; StrSubstNo(AgedSummaryLbl, AgingBandEndingDate, PeriodLength, SelectStr(DateChoice + 1, DuePostingDateLbl)))
                        {
                        }
                        column(AgingDate41; Format(AgingDate[4] + 1))
                        {
                        }
                        column(AgingDate5; Format(AgingDate[5]))
                        {
                        }
                        column(AgingBandBufCol1Amt; TempAgingBandBuf."Column 1 Amt.")
                        {
                            AutoFormatExpression = TempAgingBandBuf."Currency Code";
                            AutoFormatType = 1;
                        }
                        column(AgingBandBufCol2Amt; TempAgingBandBuf."Column 2 Amt.")
                        {
                            AutoFormatExpression = TempAgingBandBuf."Currency Code";
                            AutoFormatType = 1;
                        }
                        column(AgingBandBufCol3Amt; TempAgingBandBuf."Column 3 Amt.")
                        {
                            AutoFormatExpression = TempAgingBandBuf."Currency Code";
                            AutoFormatType = 1;
                        }
                        column(AgingBandBufCol4Amt; TempAgingBandBuf."Column 4 Amt.")
                        {
                            AutoFormatExpression = TempAgingBandBuf."Currency Code";
                            AutoFormatType = 1;
                        }
                        column(AgingBandBufCol5Amt; TempAgingBandBuf."Column 5 Amt.")
                        {
                            AutoFormatExpression = TempAgingBandBuf."Currency Code";
                            AutoFormatType = 1;
                        }
                        column(AgingBandCurrencyCode; AgingBandCurrencyCode)
                        {
                        }
                        column(beforeCaption; beforeCaptionLbl)
                        {
                        }
                        column(AgingDateHeader1; AgingDateHeader1)
                        {
                        }
                        column(AgingDateHeader2; AgingDateHeader2)
                        {
                        }
                        column(AgingDateHeader3; AgingDateHeader3)
                        {
                        }
                        column(AgingDateHeader4; AgingDateHeader4)
                        {
                        }

                        trigger OnAfterGetRecord()
                        begin
                            if Number = 1 then begin
                                if not TempAgingBandBuf.Find('-') then
                                    CurrReport.Break();
                            end else
                                if TempAgingBandBuf.Next = 0 then
                                    CurrReport.Break();
                            AgingBandCurrencyCode := TempAgingBandBuf."Currency Code";
                            if AgingBandCurrencyCode = '' then
                                AgingBandCurrencyCode := GLSetup."LCY Code";
                        end;
                    }

                    trigger OnPreDataItem()
                    begin
                        if not IncludeAgingBand then
                            CurrReport.Break
                    end;
                }
            }
            dataitem(LetterText; "Integer")
            {
                DataItemTableView = SORTING(Number) WHERE(Number = CONST(1));
                column(GreetingText; GreetingLbl)
                {
                }
                column(BodyText; BodyLbl)
                {
                }
                column(ClosingText; ClosingLbl)
                {
                }
            }

            trigger OnAfterGetRecord()
            begin
                TempAgingBandBuf.DeleteAll();
                CurrReport.Language := Language.GetLanguageIdOrDefault("Language Code");
                PrintLine := false;
                if PrintAllHavingBal and (not PrintAllHavingEntry) then
                    PrintLine := true;

                if (not PrintLine) and PrintAllHavingEntry then begin
                    CustLedgerEntry.Reset();
                    CustLedgerEntry.SetCurrentKey("Customer No.", "Posting Date");
                    CustLedgerEntry.SetRange("Customer No.", "No.");
                    CustLedgerEntry.SetRange("Posting Date", StartDate, EndDate);
                    CopyFilter("Currency Filter", CustLedgerEntry."Currency Code");
                    PrintLine := not CustLedgerEntry.IsEmpty();
                end;
                if (not PrintLine) and PrintAllHavingBal then begin
                    Cust2 := Customer;
                    Cust2.SetRange("Date Filter", 0D, EndDate);
                    Cust2.CalcFields("Net Change (LCY)");
                    PrintLine := Cust2."Net Change (LCY)" <> 0;
                end;
                if not PrintLine then
                    CurrReport.Skip();

                FormatAddr.Customer(CustAddr, Customer);
                PrintedCustomersList.Add("No.");

                IsFirstLoop := false;
            end;

            trigger OnPreDataItem()
            begin
                VerifyDates;
                AgingBandEndingDate := EndDate;
                CalcAgingBandDates;

                CompanyInfo.Get();
                FormatAddr.Company(CompanyAddr, CompanyInfo);
                CompanyInfo.CalcFields(Picture);

                PopulateTempCurrencies;

                if PrintAllHavingBal and not PrintAllHavingEntry then begin
                    SetRange("Date Filter", 0D, EndDate);
                    SetAutoCalcFields("Net Change (LCY)");
                    SetFilter("Net Change (LCY)", '<>0');
                end;
            end;
        }
    }

    requestpage
    {
        SaveValues = true;
        layout
        {
            area(content)
            {
                group(Options)
                {
                    Caption = 'Options';
                    field("Start Date"; StartDate)
                    {
                        ApplicationArea = Basic, Suite;
                        Caption = 'Start Date';
                        ToolTip = 'Specifies the date from which the report or batch job processes information.';
                    }
                    field("End Date"; EndDate)
                    {
                        ApplicationArea = Basic, Suite;
                        Caption = 'End Date';
                        ToolTip = 'Specifies the date to which the report or batch job processes information.';
                    }
                    field(ShowOverdueEntries; PrintEntriesDue)
                    {
                        ApplicationArea = Basic, Suite;
                        Caption = 'Show Overdue Entries';
                        ToolTip = 'Specifies if you want overdue entries to be shown separately for each currency.';
                    }
                    group(Include)
                    {
                        Caption = 'Include';
                        field(IncludeAllCustomerswithLE; PrintAllHavingEntry)
                        {
                            ApplicationArea = Basic, Suite;
                            Caption = 'Include All Customers with Ledger Entries';
                            MultiLine = true;
                            ToolTip = 'Specifies if you want entries displayed for customers that have ledger entries at the end of the selected period.';

                            trigger OnValidate()
                            begin
                                if not PrintAllHavingEntry then
                                    PrintAllHavingBal := true;
                            end;
                        }
                        field(IncludeAllCustomerswithBalance; PrintAllHavingBal)
                        {
                            ApplicationArea = Basic, Suite;
                            Caption = 'Include All Customers with a Balance';
                            MultiLine = true;
                            ToolTip = 'Specifies if you want entries displayed for customers that have a balance at the end of the selected period.';

                            trigger OnValidate()
                            begin
                                if not PrintAllHavingBal then
                                    PrintAllHavingEntry := true;
                            end;
                        }
                        field(IncludeReversedEntries; PrintReversedEntries)
                        {
                            ApplicationArea = Basic, Suite;
                            Caption = 'Include Reversed Entries';
                            ToolTip = 'Specifies if you want to include reversed entries in the report.';
                        }
                        field(IncludeUnappliedEntries; PrintUnappliedEntries)
                        {
                            ApplicationArea = Basic, Suite;
                            Caption = 'Include Unapplied Entries';
                            ToolTip = 'Specifies if you want to include unapplied entries in the report.';
                        }
                    }
                    group("Aging Band")
                    {
                        Caption = 'Aging Band';
                        field(IncludeAgingBand; IncludeAgingBand)
                        {
                            ApplicationArea = Basic, Suite;
                            Caption = 'Include Aging Band';
                            ToolTip = 'Specifies if you want an aging band to be included in the document. If you place a check mark here, you must also fill in the Aging Band Period Length and Aging Band by fields.';
                        }
                        field(AgingBandPeriodLengt; PeriodLength)
                        {
                            ApplicationArea = Basic, Suite;
                            Caption = 'Aging Band Period Length';
                            ToolTip = 'Specifies the length of each of the four periods in the aging band, for example, enter "1M" for one month. The most recent period will end on the last day of the period in the Date Filter field.';
                        }
                        field(AgingBandby; DateChoice)
                        {
                            ApplicationArea = Basic, Suite;
                            Caption = 'Aging Band by';
                            OptionCaption = 'Due Date,Posting Date';
                            ToolTip = 'Specifies if the aging band will be calculated from the due date or from the posting date.';
                        }
                    }
                    field(LogInteraction; LogInteraction)
                    {
                        ApplicationArea = Basic, Suite;
                        Caption = 'Log Interaction';
                        Enabled = LogInteractionEnable;
                        ToolTip = 'Specifies if you want the program to log this interaction.';
                    }
                }
                group("Output Options")
                {
                    Caption = 'Output Options';
                    field(ReportOutput; SupportedOutputMethod)
                    {
                        ApplicationArea = Basic, Suite;
                        Caption = 'Report Output';
                        OptionCaption = 'Print,Preview,Word,PDF,Email,XML - RDLC layouts only', Comment = 'Each item is a verb/action - to print, to preview, to export to Word, export to PDF, send email, export to XML for RDLC layouts only';
                        ToolTip = 'Specifies the output of the scheduled report, such as PDF or Word.';

                        trigger OnValidate()
                        var
                            CustomLayoutReporting: Codeunit "Custom Layout Reporting";
                        begin
                            ShowPrintIfEmailIsMissing := (SupportedOutputMethod = SupportedOutputMethod::Email);

                            case SupportedOutputMethod of
                                SupportedOutputMethod::Print:
                                    ChosenOutputMethod := CustomLayoutReporting.GetPrintOption;
                                SupportedOutputMethod::Preview:
                                    ChosenOutputMethod := CustomLayoutReporting.GetPreviewOption;
                                SupportedOutputMethod::Word:
                                    ChosenOutputMethod := CustomLayoutReporting.GetWordOption;
                                SupportedOutputMethod::PDF:
                                    ChosenOutputMethod := CustomLayoutReporting.GetPDFOption;
                                SupportedOutputMethod::Email:
                                    ChosenOutputMethod := CustomLayoutReporting.GetEmailOption;
                                SupportedOutputMethod::XML:
                                    ChosenOutputMethod := CustomLayoutReporting.GetXMLOption;
                            end;
                        end;
                    }
                    field(ChosenOutput; ChosenOutputMethod)
                    {
                        ApplicationArea = Basic, Suite;
                        Caption = 'Chosen Output';
                        ToolTip = 'Specifies how to output the report, such as Print or Excel.';
                        Visible = false;
                    }
                    group(EmailOptions)
                    {
                        Caption = 'Email Options';
                        Visible = ShowPrintIfEmailIsMissing;
                        field(PrintMissingAddresses; PrintIfEmailIsMissing)
                        {
                            ApplicationArea = Basic, Suite;
                            Caption = 'Print Although Email is Missing';
                            ToolTip = 'Specifies if you want to print also the statements for customers that have not been set up with a send-to email address.';
                        }
                    }
                }
            }
        }

        actions
        {
        }

        trigger OnOpenPage()
        begin
            InitRequestPageDataInternal;
        end;
    }

    labels
    {
    }

    trigger OnInitReport()
    begin
        GLSetup.Get();
        SalesSetup.Get();

        case SalesSetup."Logo Position on Documents" of
            SalesSetup."Logo Position on Documents"::"No Logo":
                ;
            SalesSetup."Logo Position on Documents"::Left:
                begin
                    CompanyInfo1.Get();
                    CompanyInfo1.CalcFields(Picture);
                end;
            SalesSetup."Logo Position on Documents"::Center:
                begin
                    CompanyInfo2.Get();
                    CompanyInfo2.CalcFields(Picture);
                end;
            SalesSetup."Logo Position on Documents"::Right:
                begin
                    CompanyInfo3.Get();
                    CompanyInfo3.CalcFields(Picture);
                end;
        end;

        LogInteractionEnable := true;
    end;

    trigger OnPostReport()
    var
        CusNo: Code[20];
    begin
        if not IsReportInPreviewMode then
            foreach CusNo in PrintedCustomersList do
                if Customer.Get(CusNo) then begin
                    Customer."Last Statement No." := Customer."Last Statement No." + 1;
                    Customer.Modify();
                    if LogInteraction then
                        SegManagement.LogDocument(
                          7, Format(Customer."Last Statement No."), 0, 0, DATABASE::Customer, Customer."No.", Customer."Salesperson Code", '',
                          StatementLbl + Format(Customer."Last Statement No."), '');
                end;
    end;

    trigger OnPreReport()
    begin
        InitRequestPageDataInternal;
    end;

    var
        EntriesLbl: Label 'Entries %1', Comment = '%1 = Currency code';
        OverdueEntriesLbl: Label 'Overdue Entries %1', Comment = '%1=Currency code';
        StatementLbl: Label 'Statement ';
        GLSetup: Record "General Ledger Setup";
        SalesSetup: Record "Sales & Receivables Setup";
        CompanyInfo: Record "Company Information";
        CompanyInfo1: Record "Company Information";
        CompanyInfo2: Record "Company Information";
        CompanyInfo3: Record "Company Information";
        Cust2: Record Customer;
        Currency: Record Currency;
        TempCurrency2: Record Currency temporary;
        CustLedgerEntry: Record "Cust. Ledger Entry";
        DetailedCustLedgEntry2: Record "Detailed Cust. Ledg. Entry";
        TempAgingBandBuf: Record "Aging Band Buffer" temporary;
        Language: Codeunit Language;
        FormatAddr: Codeunit "Format Address";
        SegManagement: Codeunit SegManagement;
        PrintedCustomersList: List of [Code[20]];
        PrintAllHavingEntry: Boolean;
        PrintAllHavingBal: Boolean;
        PrintEntriesDue: Boolean;
        PrintUnappliedEntries: Boolean;
        PrintReversedEntries: Boolean;
        PrintLine: Boolean;
        LogInteraction: Boolean;
        EntriesExists: Boolean;
        StartDate: Date;
        EndDate: Date;
        DueDate: Date;
        CustAddr: array[8] of Text[100];
        CompanyAddr: array[8] of Text[100];
        Description: Text[100];
        StartBalance: Decimal;
        CustBalance: Decimal;
        RemainingAmount: Decimal;
        CurrencyCode3: Code[10];
        MulticurrencyAppLbl: Label 'Multicurrency Application';
        PaymentDiscountLbl: Label 'Payment Discount';
        RoundingLbl: Label 'Rounding';
        PeriodLength: DateFormula;
        PeriodLength2: DateFormula;
        DateChoice: Option "Due Date","Posting Date";
        AgingDate: array[5] of Date;
        AgingBandPeriodErr: Label 'You must specify the Aging Band Period Length.';
        AgingBandEndingDate: Date;
        AgingBandEndErr: Label 'You must specify Aging Band Ending Date.';
        AgedSummaryLbl: Label 'Aged Summary by %1 (%2 by %3)', Comment = '%1=Report aging band end date, %2=Aging band period, %3=Type of deadline (''due date'', ''posting date'') as given in DuePostingDateLbl';
        IncludeAgingBand: Boolean;
        PeriodLengthErr: Label 'Period Length is out of range.';
        AgingBandCurrencyCode: Code[20];
        DuePostingDateLbl: Label 'Due Date,Posting Date';
        WriteoffsLbl: Label 'Application Writeoffs';
        [InDataSet]
        LogInteractionEnable: Boolean;
        PeriodSeparatorLbl: Label '-%1', Comment = 'Negating the period length: %1 is the period length';
        StatementCaptionLbl: Label 'Statement';
        PhoneNo_CompanyInfoCaptionLbl: Label 'Phone No.';
        VATRegNo_CompanyInfoCaptionLbl: Label 'VAT Registration No.';
        GiroNo_CompanyInfoCaptionLbl: Label 'Giro No.';
        BankName_CompanyInfoCaptionLbl: Label 'Bank';
        BankAccNo_CompanyInfoCaptionLbl: Label 'Account No.';
        No1_CustCaptionLbl: Label 'Customer No.';
        StartDateCaptionLbl: Label 'Starting Date';
        EndDateCaptionLbl: Label 'Ending Date';
        LastStatmntNo_CustCaptionLbl: Label 'Statement No.';
        PostDate_DtldCustLedgEntriesCaptionLbl: Label 'Posting Date';
        DueDate_CustLedgEntry2CaptionLbl: Label 'Due Date';
        CustBalanceCaptionLbl: Label 'Running Total';
        beforeCaptionLbl: Label '..before';
        isInitialized: Boolean;
        CompanyInfoHomepageCaptionLbl: Label 'Home Page';
        CompanyInfoEmailCaptionLbl: Label 'Email';
        DocDateCaptionLbl: Label 'Document Date';
        Total_CaptionLbl: Label 'Total';
        BlankStartDateErr: Label 'Start Date must have a value.';
        BlankEndDateErr: Label 'End Date must have a value.';
        StartDateLaterTheEndDateErr: Label 'Start date must be earlier than End date.';
        IsFirstLoop: Boolean;
        CurrReportPageNoCaptionLbl: Label 'Page';
        IsFirstPrintLine: Boolean;
        IsNewCustCurrencyGroup: Boolean;
        AgingDateHeader1: Text;
        AgingDateHeader2: Text;
        AgingDateHeader3: Text;
        AgingDateHeader4: Text;
        SupportedOutputMethod: Option Print,Preview,Word,PDF,Email,XML;
        ChosenOutputMethod: Integer;
        PrintIfEmailIsMissing: Boolean;
        [InDataSet]
        ShowPrintIfEmailIsMissing: Boolean;
        CustBalance2: Decimal;
        GreetingLbl: Label 'Hello';
        ClosingLbl: Label 'Sincerely';
        BodyLbl: Label 'Thank you for your business. Your statement is attached to this message.';

    local procedure GetDate(PostingDate: Date; DueDate: Date): Date
    begin
        if DateChoice = DateChoice::"Posting Date" then
            exit(PostingDate);

        exit(DueDate);
    end;

    local procedure CalcAgingBandDates()
    begin
        if not IncludeAgingBand then
            exit;
        if AgingBandEndingDate = 0D then
            Error(AgingBandEndErr);
        if Format(PeriodLength) = '' then
            Error(AgingBandPeriodErr);
        Evaluate(PeriodLength2, StrSubstNo(PeriodSeparatorLbl, PeriodLength));
        AgingDate[5] := AgingBandEndingDate;
        AgingDate[4] := CalcDate(PeriodLength2, AgingDate[5]);
        AgingDate[3] := CalcDate(PeriodLength2, AgingDate[4]);
        AgingDate[2] := CalcDate(PeriodLength2, AgingDate[3]);
        AgingDate[1] := CalcDate(PeriodLength2, AgingDate[2]);
        if AgingDate[2] <= AgingDate[1] then
            Error(PeriodLengthErr);

        AgingDateHeader1 := Format(AgingDate[1]) + ' - ' + Format(AgingDate[2]);
        AgingDateHeader2 := Format(AgingDate[2] + 1) + ' - ' + Format(AgingDate[3]);
        AgingDateHeader3 := Format(AgingDate[3] + 1) + ' - ' + Format(AgingDate[4]);
        AgingDateHeader4 := Format(AgingDate[4] + 1);
    end;

    local procedure UpdateBuffer(CurrencyCode: Code[10]; Date: Date; Amount: Decimal)
    var
        I: Integer;
        GoOn: Boolean;
    begin
        TempAgingBandBuf.Init();
        TempAgingBandBuf."Currency Code" := CurrencyCode;
        if not TempAgingBandBuf.Find then
            TempAgingBandBuf.Insert();
        I := 1;
        GoOn := true;
        while (I <= 5) and GoOn do begin
            if Date <= AgingDate[I] then
                if I = 1 then begin
                    TempAgingBandBuf."Column 1 Amt." := TempAgingBandBuf."Column 1 Amt." + Amount;
                    GoOn := false;
                end;
            if Date <= AgingDate[I] then
                if I = 2 then begin
                    TempAgingBandBuf."Column 2 Amt." := TempAgingBandBuf."Column 2 Amt." + Amount;
                    GoOn := false;
                end;
            if Date <= AgingDate[I] then
                if I = 3 then begin
                    TempAgingBandBuf."Column 3 Amt." := TempAgingBandBuf."Column 3 Amt." + Amount;
                    GoOn := false;
                end;
            if Date <= AgingDate[I] then
                if I = 4 then begin
                    TempAgingBandBuf."Column 4 Amt." := TempAgingBandBuf."Column 4 Amt." + Amount;
                    GoOn := false;
                end;
            if Date <= AgingDate[I] then
                if I = 5 then begin
                    TempAgingBandBuf."Column 5 Amt." := TempAgingBandBuf."Column 5 Amt." + Amount;
                    GoOn := false;
                end;
            I := I + 1;
        end;
        TempAgingBandBuf.Modify();
    end;

    procedure SkipReversedUnapplied(var DetailedCustLedgEntry: Record "Detailed Cust. Ledg. Entry"): Boolean
    var
        CustLedgEntry: Record "Cust. Ledger Entry";
    begin
        if PrintReversedEntries and PrintUnappliedEntries then
            exit(false);
        if not PrintUnappliedEntries then
            if DetailedCustLedgEntry.Unapplied then
                exit(true);
        if not PrintReversedEntries then begin
            CustLedgEntry.Get(DetailedCustLedgEntry."Cust. Ledger Entry No.");
            if CustLedgEntry.Reversed then
                exit(true);
        end;
        exit(false);
    end;

    procedure InitializeRequest(NewPrintEntriesDue: Boolean; NewPrintAllHavingEntry: Boolean; NewPrintAllHavingBal: Boolean; NewPrintReversedEntries: Boolean; NewPrintUnappliedEntries: Boolean; NewIncludeAgingBand: Boolean; NewPeriodLength: Text[30]; NewDateChoice: Option "Due Date","Posting Date"; NewLogInteraction: Boolean; NewStartDate: Date; NewEndDate: Date)
    begin
        InitRequestPageDataInternal;

        PrintEntriesDue := NewPrintEntriesDue;
        PrintAllHavingEntry := NewPrintAllHavingEntry;
        PrintAllHavingBal := NewPrintAllHavingBal;
        PrintReversedEntries := NewPrintReversedEntries;
        PrintUnappliedEntries := NewPrintUnappliedEntries;
        IncludeAgingBand := NewIncludeAgingBand;
        Evaluate(PeriodLength, NewPeriodLength);
        DateChoice := NewDateChoice;
        LogInteraction := NewLogInteraction;
        StartDate := NewStartDate;
        EndDate := NewEndDate;
    end;

    local procedure IsReportInPreviewMode(): Boolean
    var
        MailManagement: Codeunit "Mail Management";
    begin
        exit(CurrReport.Preview or MailManagement.IsHandlingGetEmailBody);
    end;

    procedure InitRequestPageDataInternal()
    begin
        if isInitialized then
            exit;

        isInitialized := true;

        if (not PrintAllHavingEntry) and (not PrintAllHavingBal) then
            PrintAllHavingBal := true;

        LogInteraction := SegManagement.FindInteractTmplCode(7) <> '';
        LogInteractionEnable := LogInteraction;

        if Format(PeriodLength) = '' then
            Evaluate(PeriodLength, '<1M+CM>');

        ShowPrintIfEmailIsMissing := SupportedOutputMethod = SupportedOutputMethod::Email;
    end;

    local procedure VerifyDates()
    begin
        if StartDate = 0D then
            Error(BlankStartDateErr);
        if EndDate = 0D then
            Error(BlankEndDateErr);
        if StartDate > EndDate then
            Error(StartDateLaterTheEndDateErr);
    end;

    local procedure PopulateTempCurrencies()
    begin
        CustLedgerEntry.Reset();
        CustLedgerEntry.SetCurrentKey("Currency Code");
        TempCurrency2.Init();
        while CustLedgerEntry.FindFirst do begin
            TempCurrency2.Code := CustLedgerEntry."Currency Code";
            TempCurrency2.Insert();
            CustLedgerEntry.SetFilter("Currency Code", '>%1', CustLedgerEntry."Currency Code");
        end;
    end;

    // G2S CAS-01379-X2R2Z9 
    local procedure UpdateVariables(CustLedger: Record "Cust. Ledger Entry")
    var
        PartialRecCount: Integer;
        DefaultQty, InvPercent : Decimal;
        PstInHeader: Record "Sales Invoice Header";
    begin
        PartialRecCount := 0;
        IsCompleteLineCount := 0;
        GrossAmount := 0;
        VariableDiscAmt2 := 0;
        totalLineAmount := 0;
        BaseLineAmt := 0;
        DiscLineAmt := 0;
        BaseLineAmt2 := 0;
        FixedDiscAmt2 := 0;
        VATAmt2 := 0;
        RecCount := 0;
        LineAmtBeforeVat2 := 0;
        PartialLineAmtBeforeVat2 := 0;
        LineAmtAfterVAT2 := 0;
        TotalDiscAmt2 := 0;

        CustLedEntry.Copy(CustLedger);
        if CustLedEntry."Document Type" = CustLedEntry."Document Type"::Invoice then begin
            PstSalesInvLine.SetFilter("Document No.", '%1', CustLedEntry."Document No.");
            PstSalesInvLine.SetFilter("Line Discount %", '<>%1', 0);
            PstSalesInvLine.SetFilter(Quantity, '<>%1', 0);
            if PstSalesInvLine.FindSet() then begin
                RecCount := PstSalesInvLine.Count();
                repeat
                    VariableDiscAmt := 0;
                    FixedDiscAmt := 0;
                    BaseLineAmt := 0;
                    DiscLineAmt := 0;
                    VATAmt := 0;
                    IsFixedDiscount := false;
                    IsVariableDiscount := false;
                    IsLineDiscountPercent := true;
                    PartialLineAmtBeforeVat := 0;
                    LineAmtBeforeVat := 0;
                    IsPartialTrxn := false;
                    DefaultQty := 0;
                    InvPercent := 0;

                    if PstSalesInvLine.Quantity = 0 then IsZeroQty := true else IsZeroQty := false;
                    if PstSalesInvLine."Fixed Rebate" <> 0 then IsFixedDiscount := true;
                    if PstSalesInvLine."Line Discount %" > PstSalesInvLine."Fixed Rebate" then IsVariableDiscount := true;

                    if not ((PstSalesInvLine."Line Discount %" <> 0) and (PstSalesInvLine."Line Discount %" > PstSalesInvLine."Fixed Rebate")) then
                        IsLineDiscountPercent := false;

                    if not IsZeroQty then begin
                        // Check if Partial Transaction
                        if (PstSalesInvLine."Fixed Rebate Amount to Inv." <> PstSalesInvLine."Fixed Rebate Amount") or
                        (PstSalesInvLine."Fixed Rebate Amount to Inv." < PstSalesInvLine."Fixed Rebate Amount") then begin
                            IsPartialTrxn := true;
                            IsLinePartial += 1;
                        end else
                            IsCompleteLineCount += 1;

                        if IsPartialTrxn then begin
                            PstInHeader.SetRange("No.", PstSalesInvLine."Document No.");
                            if PstInHeader.FindFirst() then
                                DefaultQty := GetDefaultQty(PstInHeader."Order No.", PstSalesInvLine."No.");

                            InvPercent := GetLineAmtPercentage(PstSalesInvLine.Quantity, DefaultQty);
                        end;

                        // Calculate discount 
                        //Line Discount % | Variable Discount Amount
                        if IsLineDiscountPercent then
                            if not IsZeroQty then begin
                                VariableDiscAmt := PstSalesInvLine."Rebate Discount";
                                VariableDiscAmt2 += PstSalesInvLine."Rebate Discount";
                            end;

                        //Fixed Discount % | Fixed Discount Amount
                        if IsPartialTrxn then begin
                            FixedDiscAmt := PstSalesInvLine."Fixed Rebate Amount to Inv.";
                            FixedDiscAmt2 += FixedDiscAmt;
                        end else begin
                            FixedDiscAmt := PstSalesInvLine."Fixed Rebate Amount";
                            FixedDiscAmt2 += FixedDiscAmt;
                        end;

                        TotalDiscAmt := FixedDiscAmt + VariableDiscAmt;
                        TotalDiscAmt2 += TotalDiscAmt;

                        BaseLineAmt := PstSalesInvLine.Quantity * PstSalesInvLine."Unit Price";
                        BaseLineAmt2 += BaseLineAmt;

                        if not IsPartialTrxn then begin
                            if IsFixedDiscount and not IsVariableDiscount then begin
                                LineAmtBeforeVat := (PstSalesInvLine.Quantity * PstSalesInvLine."Unit Price") - FixedDiscAmt;
                                LineAmtBeforeVat2 += LineAmtBeforeVat;
                            end;

                            if IsVariableDiscount and IsFixedDiscount then begin
                                LineAmtBeforeVat := (PstSalesInvLine.Quantity * PstSalesInvLine."Unit Price") - TotalDiscAmt;
                                LineAmtBeforeVat2 += LineAmtBeforeVat;
                            end;

                            if not IsVariableDiscount and not IsFixedDiscount then begin
                                LineAmtBeforeVat := PstSalesInvLine.Quantity * PstSalesInvLine."Unit Price";
                                LineAmtBeforeVat2 += LineAmtBeforeVat;
                            end;
                        end else begin
                            PartialLineAmtBeforeVat := (PstSalesInvLine.Quantity * PstSalesInvLine."Unit Price") - PstSalesInvLine."Fixed Rebate Amount to Inv."; //((PstSalesInvLine.Quantity * PstSalesInvLine."Unit Price") * (PstSalesInvLine."Fixed Rebate" / 100));
                            PartialLineAmtBeforeVat2 += PartialLineAmtBeforeVat;
                        end;

                        // Vat Calculation
                        if PstSalesInvLine."VAT %" > 0 then begin
                            VATAmt := (LineAmtBeforeVat + PartialLineAmtBeforeVat) * (PstSalesInvLine."VAT %" / 100);
                            VATAmt2 += VATAmt;
                            LineAmtAfterVAT := LineAmtBeforeVat + PartialLineAmtBeforeVat + VATAmt;
                            GrossAmountVat += LineAmtAfterVAT;

                            LineAmtAfterVAT2 += LineAmtAfterVAT;
                        end else begin
                            LineAmtAfterVAT2 += LineAmtBeforeVat + PartialLineAmtBeforeVat;
                            GrossAmountNOVat += LineAmtAfterVAT2;
                        end;

                    end;
                until PstSalesInvLine.Next() = 0;
            end;

            PartialRecCount := getPartlineRec(CustLedger);
            totalLineAmount := LineAmtBeforeVat2 + PartialLineAmtBeforeVat2;

            if PartialRecCount = 0 then
                GrossAmount := BaseLineAmt2 + VATAmt2;

            if PartialRecCount > 0 then begin
                if PartialRecCount = RecCount then
                    GrossAmount := PartialLineAmtBeforeVat2 + TotalDiscAmt2 + VATAmt2
                else
                    GrossAmount := totalLineAmount + VATAmt2 + TotalDiscAmt2;
            end;

            GrossAmount := Round(GrossAmount, 0.01, '>')

        end else
            GrossAmount := 0;
    end;
    // G2S CAS-01379-X2R2Z9 

    local procedure getPartlineRec(CustLedger: Record "Cust. Ledger Entry"): Integer;
    var
        recCount: Integer;
    begin
        recCount := 0;
        IsLinePartial := 0;
        PstSalesInvLine2.Reset();
        PstSalesInvLine2.SetFilter("Document No.", '%1', CustLedger."Document No.");
        PstSalesInvLine2.SetFilter("Line Discount %", '<>%1', 0);
        if PstSalesInvLine2.FindSet() then begin
            repeat
                if (PstSalesInvLine2.Quantity <> 0) and (PstSalesInvLine2."Fixed Rebate Amount to Inv." < PstSalesInvLine2."Fixed Rebate Amount") then
                    IsLinePartial += 1;
            until PstSalesInvLine2.Next() = 0;
        end;
        recCount := IsLinePartial;
        exit(recCount);
    end;

    local procedure GetLineAmtPercentage(var InvQty: Decimal; DefaultQty: Decimal) InvPercentVal: Decimal
    begin
        exit((InvQty / DefaultQty) * 100)
    end;

    local procedure GetDefaultQty(var OrderNo: Code[20]; ItemNo: code[20]) DefaultQty: Decimal
    begin
        "Sales Line Archive".SetRange("Document No.", OrderNo);
        "Sales Line Archive".SetFilter("No.", '%1', ItemNo);
        if "Sales Line Archive".FindFirst() then
            exit("Sales Line Archive".Quantity);
    end;

    // G2S CAS-01379-X2R2Z9 
    var
        "Sales Header Archive": Record "Sales Header Archive";
        "Sales Line Archive": Record "Sales Line Archive";
        RecCount, IsLinePartial, IsCompleteLineCount : Integer;
        CustLedEntry: Record "Cust. Ledger Entry";
        IsZeroQty, IsLineDiscountPercent, IsPartialTrxn, IsFixedDiscount, IsVariableDiscount : Boolean;
        totalLineAmount, GrossAmount, GrossAmountVat, GrossAmountNOVat, VATAmt, VATAmt2, LineAmtAfterVAT, LineAmtAfterVAT2, PartialLineAmtBeforeVat, PartialLineAmtBeforeVat2, LineAmtBeforeVat, LineAmtBeforeVat2, FixedDiscAmt, VariableDiscAmt, FixedDiscAmt2, VariableDiscAmt2, TotalDiscAmt, TotalDiscAmt2, BaseLineAmt, BaseLineAmt2, DiscLineAmt : Decimal;
        PstSalesInvLine, PstSalesInvLine2 : Record "Sales Invoice Line";
    // G2S CAS-01379-X2R2Z9 
}

