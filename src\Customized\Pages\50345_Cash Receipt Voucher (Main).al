page 50345 "Cash Receipt Voucher (Main)"
{
    // version CHI6.0

    // PROJECT : CHI 6.0
    // **********************************************************************************
    // SIGN
    // **********************************************************************************
    // CHI     :  CHI 6.0 Developments
    // UNL     :  Univision Nigeria Limited
    // SAA     :  SAHEED ADIO ADEOSUN
    // HO      :  <PERSON>
    // **********************************************************************************
    // VER      SIGN         DATE    DESCRIPTION
    // **********************************************************************************
    // 1.0      UNL      06-Dec-11   -> Form Created to display Cash Rcpt Document Details.
    // 
    // 3.0      SAA      08-Mar-12   -> Added codes to "Form-OnOpenForm" & "Form-OnNewRecord" to
    //                                  filter out Responsibility Centres in Vouchers.
    //                               -> Added the "Responsibility Centre" field to the form
    // 
    // 1.0      HO       7-Sep-12    -> Code added to "Form-OnDelereRecord()" to allow Archive of Cash Receipt Voucher No. when deleted.

    Editable = false;
    PageType = Document;
    SourceTable = "Voucher Header";
    SourceTableView = SORTING("Voucher Type", "Document No.")
                      WHERE("Voucher Type" = CONST(CRV),
                            Status = CONST(Released),
                            "Cash Account Type" = FILTER(Main));

    layout
    {
        area(content)
        {
            group(General)
            {
                Caption = 'General';
                field("Document No."; "Document No.")
                {
                    ApplicationArea = all;

                    trigger OnAssistEdit();
                    begin
                        if AssistEdit(xRec) then
                            CurrPage.UPDATE;
                    end;
                }
                field("Posting Date"; "Posting Date")
                {
                    ApplicationArea = all;

                    trigger OnValidate();
                    begin
                        // SAA 3.0 >>
                        PostingDateValidate;
                        // SAA 3.0 <<
                    end;
                }
                field("Account No."; "Account No.")
                {
                    ApplicationArea = all;
                    Caption = 'Debit Account No.';
                }
                field("Account Name"; "Account Name")
                {
                    ApplicationArea = all;
                }
                field(Status; Status)
                {
                    ApplicationArea = all;
                }
                field(Narration; Narration)
                {
                    ApplicationArea = all;
                }
                field("Currency Code"; "Currency Code")
                {
                    ApplicationArea = all;
                    Editable = false;

                    trigger OnAssistEdit();
                    begin
                        ChangeExchangeRate.SetParameter("Currency Code", "Currency Factor", "Posting Date");
                        if ChangeExchangeRate.RUNMODAL = ACTION::OK then begin
                            VALIDATE("Currency Factor", ChangeExchangeRate.GetParameter);
                        end;
                        CLEAR(ChangeExchangeRate);
                    end;
                }
                field("Amount (LCY)"; "Amount (LCY)")
                {
                    ApplicationArea = all;
                }
                field("Shortcut Dimension 1 Code"; "Shortcut Dimension 1 Code")
                {
                    ApplicationArea = all;
                    Editable = true;
                }
                field("Shortcut Dimension 2 Code"; "Shortcut Dimension 2 Code")
                {
                    ApplicationArea = all;
                    Editable = true;
                }
                field("Responsibility Center"; "Responsibility Center")
                {
                    ApplicationArea = all;
                }
            }
            part(VoucherLines; "Main Cash Rcpt Vcher Sform")
            {
                ApplicationArea = all;
                SubPageLink = "Journal Template Name" = FIELD("Journal Template Code"),
                              "Journal Batch Name" = FIELD("Journal Batch Name"),
                              "Document No." = FIELD("Document No.");
            }
            group(Usertrail)
            {
                Caption = 'Usertrail';
                field("Created By"; "Created By")
                {
                    ApplicationArea = all;
                }
                field("Created By Name"; "Created By Name")
                {
                    ApplicationArea = all;
                }
                field("Created Date"; "Created Date")
                {
                    ApplicationArea = all;
                }
                field("Created Time"; "Created Time")
                {
                    ApplicationArea = all;
                }
                field("Modified By"; "Modified By")
                {
                    ApplicationArea = all;
                }
                field("Modified By Name"; "Modified By Name")
                {
                    ApplicationArea = all;
                }
                field("Modified Date"; "Modified Date")
                {
                    ApplicationArea = all;
                }
                field("Modified Time"; "Modified Time")
                {
                    ApplicationArea = all;
                }
            }
            group("Receipt Details")
            {
                Caption = 'Receipt Details';
                field("Receiving Type"; "Receiving Type")
                {
                    ApplicationArea = all;
                }
                field("Receiving Code"; "Receiving Code")
                {
                    ApplicationArea = all;
                }
                field("Received From"; "Received From")
                {
                    ApplicationArea = all;
                }
            }
        }
        area(FactBoxes)
        {
            part("Attached Documents"; "Document Attachment Factbox")
            {
                ApplicationArea = All;
                Caption = 'Attachments';
                SubPageLink = "Table ID" = CONST(50117),
                                "No." = FIELD("Document No.");
                // Type = FIELD("Voucher Type");
            }
            systempart(PyamentTermsLinks; Links)
            {
                ApplicationArea = RecordLinks;
            }
            systempart(PyamentTermsNotes; Notes)
            {
                ApplicationArea = Notes;
            }
        }
        //g2s29Dev23
    }

    actions
    {
        area(navigation)
        {
            group("&Voucher")
            {
                Caption = '&Voucher';
                separator(Separator1000000067)
                {
                }
                action(Dimensions)
                {
                    ApplicationArea = all;
                    Caption = 'Dimensions';

                    trigger OnAction();
                    begin
                        ShowDocDim;
                    end;
                }
                separator(Separator1000000069)
                {
                }
                action(Approvals)
                {
                    ApplicationArea = all;
                    Caption = 'Approvals';

                    trigger OnAction();
                    begin
                        ApprovalEntries.Setfilters(DATABASE::"Voucher Header", 23, "Document No.");
                        ApprovalEntries.RUN;
                    end;
                }
            }
            group("F&unctions")
            {
                Caption = 'F&unctions';
                action("Send A&pproval Request")
                {
                    ApplicationArea = all;
                    Caption = 'Send A&pproval Request';
                    Visible = false;

                    trigger OnAction();
                    begin
                        // SAA 3.0 >>
                        CheckHeaderLines(Rec);
                        // SAA 3.0 <<
                        //IF ApprovalMgt.SendVoucherApprovalRequest(Rec) THEN;
                    end;
                }
                action("Cancel Approval Re&quest")
                {
                    ApplicationArea = all;
                    Caption = 'Cancel Approval Re&quest';
                    Visible = false;

                    trigger OnAction();
                    begin
                        //IF ApprovalMgt.CancelVoucherApprovalRequest(Rec,TRUE,TRUE) THEN;
                    end;
                }
                separator(Separator1000000049)
                {
                }
                action("Re&lease")
                {
                    ApplicationArea = all;
                    Caption = 'Re&lease';
                    ShortCutKey = 'Ctrl+F11';
                    Visible = false;

                    trigger OnAction();
                    begin
                        CheckHeaderLines(Rec);
                        PerformManualRelease;
                    end;
                }
                action("Re&open")
                {
                    ApplicationArea = all;
                    Caption = 'Re&open';

                    trigger OnAction();
                    begin
                        PerformManualReopen;
                    end;
                }
            }
            group("P&osting")
            {
                Caption = 'P&osting';
                action("P&ost")
                {
                    ApplicationArea = all;
                    Caption = 'P&ost';
                    ShortCutKey = 'F11';
                    Visible = false;

                    trigger OnAction();
                    var
                        GeneralJrnlBtchLRec: Record "Gen. Journal Batch";
                    begin
                        GeneralJrnlBtchLRec.Reset();
                        GeneralJrnlBtchLRec.SetRange("Journal Template Name", 'CRV');
                        GeneralJrnlBtchLRec.SetRange(Name, 'CRV');
                        if GeneralJrnlBtchLRec.FindFirst() then begin
                            GeneralJrnlBtchLRec.TestField("No. Series", '');
                            GeneralJrnlBtchLRec.TestField("Posting No. Series", '');
                        end;
                        VoucherPost.RUN(Rec);
                    end;
                }
                action(Preview)
                {
                    ApplicationArea = all;
                    Caption = 'Preview';
                    ShortCutKey = 'Shift+F2';

                    trigger OnAction();
                    var
                        GeneralJrnlBtchLRec: Record "Gen. Journal Batch";
                        DocumentNo: Code[20];
                        GLRegGRec: Record "G/L Register";
                        genJounlin: Record "Gen. Journal Line 2";
                        VoucherPreview: Codeunit "Voucher Preview Posting";
                    begin
                        DocumentNo := "Document No.";
                        ClearValues();
                        VoucherPreview.RUN(Rec);
                    end;
                }
                action("Post and &Print")
                {
                    ApplicationArea = all;
                    Caption = 'Post and &Print';
                    ShortCutKey = 'Shift+F11';

                    trigger OnAction();
                    var
                        GeneralJrnlBtchLRec: Record "Gen. Journal Batch";
                    begin
                        GeneralJrnlBtchLRec.Reset();
                        GeneralJrnlBtchLRec.SetRange("Journal Template Name", 'CRV');
                        GeneralJrnlBtchLRec.SetRange(Name, 'CRV');
                        if GeneralJrnlBtchLRec.FindFirst() then begin
                            GeneralJrnlBtchLRec.TestField("No. Series", '');
                            GeneralJrnlBtchLRec.TestField("Posting No. Series", '');
                        end;
                        VoucherPost.RUN(Rec);
                    end;
                }
                action("Cash Receipt Voucher Test Report")
                {
                    trigger OnAction()
                    var
                        VouHeader: Record "Voucher Header";
                    BEGIN
                        VouHeader.RESET;
                        VouHeader.SETRANGE("Voucher Type", "Voucher Type");
                        VouHeader.SETRANGE("Document No.", "Document No.");
                        if VouHeader.FINDFIRST then
                            REPORT.RUN(50563, true, false, VouHeader);
                    END;
                }
                //Balu 05282021>>
                action("Open Excel")
                {
                    ApplicationArea = all;
                    Caption = 'Open Excel';
                    Image = Open;
                    trigger OnAction()
                    var
                        GlLine2: Record "Gen. Journal Line 2";
                    begin
                        GlLine2.CreateExcel(Rec);
                    end;
                }
                //Balu 05282021<<
            }
        }
        area(processing)
        {
            action("&Print")
            {
                ApplicationArea = all;
                Caption = '&Print';
                Ellipsis = true;
                Promoted = true;
                PromotedCategory = Process;

                trigger OnAction();
                begin
                    VoucherHeader.SETRANGE("Voucher Type", "Voucher Type");
                    VoucherHeader.SETRANGE("Document No.", "Document No.");
                    if VoucherHeader.FINDFIRST then
                        REPORT.RUN(50193, true, false, VoucherHeader);
                end;
            }
        }
    }

    trigger OnDeleteRecord(): Boolean;
    begin
        DelDocNoArchive.ArchiveNo("Document No.", 19, TODAY, TIME, USERID, DATABASE::"Voucher Header"); //HO1.0
    end;

    trigger OnNewRecord(BelowxRec: Boolean);
    begin
        "Dim. Document Type" := "Dim. Document Type"::CRV;
        // SAA 3.0 >>
        "Responsibility Center" := UserMg.GetVoucherFilter();
        // SAA 3.0 <<
    end;

    trigger OnOpenPage();
    begin
        // SAA 3.0 >>
        /*IF UserMg.GetVoucherFilter() <> '' THEN BEGIN
          FILTERGROUP(2);
          SETRANGE("Responsibility Center",UserMg.GetVoucherFilter());
          FILTERGROUP(0);
        END;*/

        //SAA3.0 <<
        Usersetup.GET(USERID);
        // SAA 3.0 >>

        FILTERGROUP(2);
        SETFILTER("Responsibility Center", Usersetup.FilterResponsibilityCenter);
        FILTERGROUP(0);
        // SAA 3.0 <<

        if not Usersetup."View Main Cash Vouchers" then
            ERROR(Text50000, Usersetup.FIELDCAPTION("View Main Cash Vouchers"));
        //SAA3.0  >>

    end;

    var
        VoucherHeader: Record "Voucher Header";
        ApprovalMgt: Codeunit 1535;
        ApprovalEntry: Record "Approval Entry";
        ReleaseVoucher: Codeunit "Voucher Release";
        ApprovalEntries: Page 658;
        ChangeExchangeRate: Page "Change Exchange Rate";
        GenJrnlLine: Record "Gen. Journal Line";
        ReportPrint: Codeunit "Test Report-Print";
        VoucherPost: Codeunit "Voucher Post";
        UserMgt: Codeunit "User Setup Management";
        UserMg: Codeunit "User Setup Management Ext";
        DelDocNoArchive: Codeunit "Deleted Doc. No. Archive";
        Usersetup: Record "User Setup";
        Text50000: Label 'You do not have permission to %1.';

    procedure CheckHeaderLines(VoucherHeaderRec: Record "Voucher Header");
    var
        GenJnlLine: Record "Gen. Journal Line 2";//PK-GJ2
        VendorLedgerEntry: Record "Vendor Ledger Entry";
    begin
        with VoucherHeaderRec do begin
            TESTFIELD("Account No.");
            TESTFIELD("Shortcut Dimension 2 Code");
            TESTFIELD("Shortcut Dimension 1 Code");
            TESTFIELD(Narration);
            TESTFIELD("Received From");
            TESTFIELD("Receiving Type");
            if "Receiving Type" <> "Receiving Type"::Others then
                TESTFIELD("Receiving Code");

            GenJnlLine.SETRANGE(GenJnlLine."Journal Template Name", "Journal Template Code");
            GenJnlLine.SETRANGE(GenJnlLine."Journal Batch Name", "Journal Batch Name");
            GenJnlLine.SETRANGE(GenJnlLine."Document No.", "Document No.");
            if GenJnlLine.FIND('-') then begin
                repeat
                    GenJnlLine.TESTFIELD("Account No.");
                    GenJnlLine.TESTFIELD("Posting Date");
                    GenJnlLine.TESTFIELD(Amount);
                    GenJnlLine.TESTFIELD("Shortcut Dimension 1 Code");
                    GenJnlLine.TESTFIELD("Shortcut Dimension 2 Code");
                    //GenJnlLine.TESTFIELD("Description 2");
                    GenJnlLine.TESTFIELD(Cleared, true);

                    if (GenJnlLine."Account Type" = GenJnlLine."Account Type"::Customer)
                      or (GenJnlLine."Bal. Account Type" = GenJnlLine."Bal. Account Type"::Customer) then
                        GenJnlLine.TESTFIELD("Ship to Code");


                //GenJnlLine."Posting Date" := TODAY;

                //GenJnlLine.MODIFY;
                until GenJnlLine.NEXT = 0;
            end;
        end;
    end;

    procedure PostingDateValidate();
    var
        Text50201: Label 'Posting Date must not be a future date';
    begin
        if "Posting Date" > TODAY then
            ERROR(Text50201);
    end;
}

