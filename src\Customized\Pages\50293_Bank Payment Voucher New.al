page 50293 "Bank Payment Voucher New"
{
    // version CHI6.0

    // PROJECT : CHI 6.0
    // **********************************************************************************
    // SIGN
    // **********************************************************************************
    // CHI     :  CHI 6.0 Developments
    // UNL     :  Univision Nigeria Limited
    // SAA     :  SAHEED ADIO ADEOSUN
    // **********************************************************************************
    // VER      SIGN         DATE   DESCRIPTION
    // **********************************************************************************
    // 1.0      UNL      06-Dec-11  -> Form Created to display Bank Pmt. Document Details.
    // 
    // 3.0      SAA      08-Mar-12  -> Added codes to "Form-OnOpenForm" to filter out Responsibility Centres in Vouchers.
    //                              -> Added the "Responsibility Centre" field to the form.
    //                   21-May-12  -> Added codes to assign and unassign Balance Account Type and Balance Account No. to
    //                                 enable check printing.
    // 
    // 1.0      HO       07-Sep-12  -> Code added to "Form-OnDelereRecord()" to allow Archive of deleted Bank Payment Voucher Document No.

    DeleteAllowed = true;
    PageType = Document;
    SourceTable = "Voucher Header";
    SourceTableView = SORTING("Voucher Type", "Document No.")
                      WHERE("Voucher Type" = CONST(BPV),
                            Status = FILTER(<> Released),
                            "Multiple Batch" = FILTER(false));
    layout
    {
        area(content)
        {
            group(General)
            {
                Caption = 'General';
                field("Document No."; "Document No.")
                {
                    ApplicationArea = all;
                    Editable = false;

                    trigger OnAssistEdit();
                    begin
                        if AssistEdit(xRec) then
                            CurrPage.UPDATE;
                    end;
                }
                field("Posting Date"; "Posting Date")
                {
                    ApplicationArea = all;
                    trigger OnValidate();
                    begin
                        // SAA 3.0 >>
                        PostingDateValidate;
                        // SAA 3.0 <<
                    end;
                }


                field("Transaction Type"; "Transaction Type")
                {
                    ApplicationArea = all;
                    trigger OnValidate();
                    begin
                        if "Transaction Type" <> "Transaction Type"::Import then
                            "Import File No." := '';
                        if "Transaction Type" <> "Transaction Type"::" " then
                            TESTFIELD("Account No.", '');
                    end;
                }
                field("WHT Applicable"; "WHT Applicable")
                {
                    ApplicationArea = all;
                    trigger OnValidate()
                    var
                        genjoulrec: Record "Gen. Journal Line";
                    begin
                        IF "WHT Applicable" then begin
                            genjoulrec.Reset();
                            genjoulrec.Setrange("Journal Template Name", 'BPV');
                            genjoulrec.Setrange("Journal Batch Name", 'BPV');
                            genjoulrec.Setrange("Voucher Type", genjoulrec."Voucher Type"::BPV);
                            genjoulrec.Setrange("Document No.", "Document No.");
                            genjoulrec.Setrange("Document Type", genjoulrec."Document Type"::Payment);
                            IF genjoulrec.FindFirst() then
                                Error('You cannot insert more than one line if WHT is Applicable')
                        end Else Begin
                            genjoulrec.Reset();
                            genjoulrec.Setrange("Journal Template Name", 'BPV');
                            genjoulrec.Setrange("Journal Batch Name", 'BPV');
                            genjoulrec.Setrange("Voucher Type", genjoulrec."Voucher Type"::BPV);
                            genjoulrec.Setrange("Document No.", "Document No.");
                            genjoulrec.Setrange("Document Type", genjoulrec."Document Type"::Payment);
                            IF genjoulrec.FindFirst() then
                                repeat
                                    Clear(genjoulrec."WHT %");
                                    Clear(genjoulrec."WHT Account");
                                    Clear(genjoulrec."WHT Amount");
                                    Clear(genjoulrec."WHT Amount(LCY)");
                                    Clear(genjoulrec."WHT Group");
                                    genjoulrec.Modify();
                                until genjoulrec.Next = 0;
                        End;
                    end;

                }
                field("Import File No."; "Import File No.")
                {
                    ApplicationArea = all;
                    trigger OnValidate();
                    begin
                        if "Import File No." <> '' then
                            TESTFIELD("Transaction Type", "Transaction Type"::Import);
                    end;
                }
                field("Clearing File No."; "Clearing File No.")
                {
                    ApplicationArea = all;
                }
                field("Responsibility Center"; "Responsibility Center")
                {
                    ApplicationArea = all;
                }
                field("Account No."; "Account No.")
                {
                    ApplicationArea = all;
                    Caption = 'Credit Account No.';

                    trigger OnValidate();
                    begin
                        if "Account No." <> '' then begin
                            TESTFIELD("Transaction Type");
                            if "Transaction Type" = "Transaction Type"::Import then
                                TESTFIELD("Import File No.");
                        end;
                    end;
                }
                field("Account Name"; "Account Name")
                {
                    ApplicationArea = all;
                }
                field(Narration; Narration)
                {
                    ApplicationArea = all;
                }
                field("Currency Code"; "Currency Code")
                {
                    ApplicationArea = all;
                    Editable = false;

                    trigger OnAssistEdit();
                    begin
                        ChangeExchangeRate.SetParameter("Currency Code", "Currency Factor", "Posting Date");
                        if ChangeExchangeRate.RUNMODAL = ACTION::OK then begin
                            VALIDATE("Currency Factor", ChangeExchangeRate.GetParameter);
                        end;
                        CLEAR(ChangeExchangeRate);
                    end;
                }
                field("Amount (LCY)"; "Amount (LCY)")
                {
                    ApplicationArea = all;
                }
                field("Shortcut Dimension 1 Code"; "Shortcut Dimension 1 Code")
                {
                    ApplicationArea = all;
                }
                field("Shortcut Dimension 2 Code"; "Shortcut Dimension 2 Code")
                {
                    ApplicationArea = all;
                }
                field("Bank Payment Type"; "Bank Payment Type")
                {
                    ApplicationArea = all;
                }
                field(Status; Status)
                {
                    ApplicationArea = all;
                }
                field("Account Type"; "Account Type")
                {
                    ApplicationArea = all;
                }
            }
            part(VoucherLines; "Bank Payment Voucher Subform")
            {
                ApplicationArea = all;
                SubPageLink = "Journal Template Name" = FIELD("Journal Template Code"),
                              "Journal Batch Name" = FIELD("Journal Batch Name"),
                              "Document No." = FIELD("Document No.");
            }
            group(Usertrail)
            {
                Caption = 'Usertrail';
                field("Created By"; "Created By")
                {
                    ApplicationArea = all;
                }
                field("Created By Name"; "Created By Name")
                {
                    ApplicationArea = all;
                }
                field("Created Date"; "Created Date")
                {
                    ApplicationArea = all;
                }
                field("Created Time"; "Created Time")
                {
                    ApplicationArea = all;
                }
                field("Modified By"; "Modified By")
                {
                    ApplicationArea = all;
                }
                field("Modified By Name"; "Modified By Name")
                {
                    ApplicationArea = all;
                }
                field("Modified Date"; "Modified Date")
                {
                    ApplicationArea = all;
                }
                field("Modified Time"; "Modified Time")
                {
                    ApplicationArea = all;
                }
            }
            group("Payment Details")
            {
                Caption = 'Payment Details';
                field("Payable To"; "Payable To")
                {
                    ApplicationArea = all;
                }
                field("Payable Code"; "Payable Code")
                {
                    ApplicationArea = all;
                }
                field("Payable Name"; "Payable Name")
                {
                    ApplicationArea = all;
                }
                field(ToBeCollectedBy; ToBeCollectedBy)
                {
                    ApplicationArea = all;
                }
                field("Payment Mode"; "Payment Mode")
                {
                    ApplicationArea = all;
                    trigger OnValidate()
                    begin
                        Teller();
                    end;
                }
                field("Teller / Cheque No."; "Teller / Cheque No.")
                {
                    ApplicationArea = all;
                    Enabled = TellerVar;

                }
                field("Teller / Cheque Date"; "Teller / Cheque Date")
                {
                    ApplicationArea = all;
                }
                field(PaymentSettlementOf; PaymentSettlementOf)
                {
                    ApplicationArea = all;
                }
            }
            group("Inter-Company")
            {
                Caption = 'Inter-Company';
                field("IC Partner G/L Acc. No."; "IC Partner G/L Acc. No.")
                {
                    ApplicationArea = all;
                }
            }
        }
        area(FactBoxes)
        {
            part("Attached Documents"; "Document Attachment Factbox")
            {
                ApplicationArea = All;
                Caption = 'Attachments';
                SubPageLink = "Table ID" = CONST(50117),
                                "No." = FIELD("Document No.");
                // Type = FIELD("Voucher Type");
            }
            systempart(PyamentTermsLinks; Links)
            {
                ApplicationArea = RecordLinks;
            }
            systempart(PyamentTermsNotes; Notes)
            {
                ApplicationArea = Notes;
            }
        }
        //g2s29Dev23
    }

    actions
    {
        area(navigation)
        {
            group("&Voucher")
            {
                Caption = '&Voucher';
                separator(Separator1000000067)
                {
                }
                action(Dimensions)
                {
                    ApplicationArea = all;
                    Caption = 'Dimensions';

                    trigger OnAction();
                    begin
                        ShowDocDim;
                    end;
                }
                separator(Separator1000000069)
                {
                }
                action(Approvals)
                {
                    ApplicationArea = all;
                    Caption = 'Approvals';
                    Visible = OpenApprovalEntriesExistForCurrUser;
                    Promoted = true;
                    PromotedIsBig = true;
                    PromotedCategory = Process;
                    PromotedOnly = true;
                    trigger OnAction();
                    begin
                        approvalmngmt.ApproveRecordApprovalRequest(RecordId());
                    end;
                }
                /*action(InsertLine)
                {
                    ApplicationArea = all;
                    Caption = 'Insert Line';
                    Promoted = true;
                    PromotedCategory = Process;
                    ShortcutKey = 'Ctrl+N';
                    trigger OnAction();
                    var
                        genjoulrec: Record "Gen. Journal Line";
                        linen: Integer;
                    begin
                        genjoulrec.RESET();
                        genjoulrec.SetRange("Journal Template Name", 'BPV');
                        genjoulrec.SetRange("Journal Batch Name", 'BPV');
                        //genjoulrec.SetRange("Document No.", "Document No.");
                        IF genjoulrec.FINDLAST THEN
                            linen := 10000 + genjoulrec."Line No."
                        else
                            linen := 10000;
                        IF "WHT Applicable" then begin
                            genjoulrec.Reset();
                            genjoulrec.Setrange("Journal Template Name", 'BPV');
                            genjoulrec.Setrange("Journal Batch Name", 'BPV');
                            genjoulrec.Setrange("Voucher Type", genjoulrec."Voucher Type"::BPV);
                            genjoulrec.Setrange("Document No.", "Document No.");
                            genjoulrec.Setrange("Document Type", genjoulrec."Document Type"::Payment);
                            IF genjoulrec.FindFirst() then
                                Error('You cannot insert more than one line if WHT is Applicable')
                        end;
                        clear(genjoulrec);
                        genjoulrec.init();
                        genjoulrec."Journal Template Name" := 'BPV';
                        genjoulrec."Journal Batch Name" := 'BPV';
                        genjoulrec."Voucher Type" := genjoulrec."Voucher Type"::BPV;
                        genjoulrec."Document No." := "Document No.";
                        genjoulrec."Document Type" := genjoulrec."Document Type"::Payment; //PJ
                        genjoulrec."Posting Date" := Today; //PJ
                        genjoulrec."Line No." := linen;
                        genjoulrec.insert(True);
                        Message('Line Inserted %1..%2', genjoulrec."Journal Template Name", genjoulrec."Journal Batch Name");
                        //insert(true);
                        CurrPage.Update();
                    end;
                }*/
                action("Insert WHT Entries")
                {
                    ApplicationArea = Basic, Suite;
                    Caption = 'Insert WHT Entries';
                    Ellipsis = true;
                    Image = Insert;
                    trigger OnAction()
                    var
                        UpdateWHTEntryToBPV: Report "Update WHT Entries To BPV";
                    begin
                        Clear(UpdateWHTEntryToBPV);
                        UpdateWHTEntryToBPV.SetValues("Journal Template Code", "Journal Batch Name", "Document No.");
                        UpdateWHTEntryToBPV.Run();
                    end;
                }
            }
            group("F&unctions")
            {
                Caption = 'F&unctions';
                action("Send Approval Request")
                {
                    ApplicationArea = All;
                    Image = SendApprovalRequest;
                    Visible = Not OpenApprEntrEsists and CanrequestApprovForFlow;
                    Promoted = true;
                    PromotedIsBig = true;
                    PromotedCategory = Process;
                    PromotedOnly = true;
                    trigger OnAction()
                    var
                        GenLineLRec: Record "Gen. Journal Line";
                    begin
                        GenLineLRec.reset;
                        GenLineLRec.SetRange("Journal Template Name", "Journal Template Code");
                        GenLineLRec.SetRange("Journal Batch Name", "Journal Batch Name");
                        GenLineLRec.SetRange("Document No.", "Document No.");
                        GenLineLRec.SetRange(Quantity, 0);
                        IF GenLineLRec.findset then
                            repeat
                                error('quantity is zero in Gen Journal for Line No. %1', GenLineLRec."Line No.");
                            until GenLineLRec.next = 0;
                        TestField("Posting Date");
                        TestField("Account No.");
                        TestField("Account Type", "Account Type"::"Bank Account");
                        CheckDimensions();
                        ClearValues();
                        CheckAppliesAmounts();
                        IF allinoneCU.CheckJournalVoucherApprovalsWorkflowEnabled(Rec) then begin
                            // >>>>>> G2S CAS-01282-W9Y4B4 15/05/24
                            validateVoucherLine();
                            // <<<<<< G2S CAS-01282-W9Y4B4 15/05/24
                            allinoneCU.OnSendJournalVoucherForApproval(Rec);
                        end;
                    end;
                }
                action("Cancel Approval Request")
                {
                    ApplicationArea = All;
                    Image = CancelApprovalRequest;
                    Visible = CanCancelapprovalforrecord or CanCancelapprovalforflow;
                    Promoted = true;
                    PromotedIsBig = true;
                    PromotedCategory = Process;
                    PromotedOnly = true;
                    trigger OnAction()
                    begin
                        allinoneCU.OnCancelJournalVoucherForApproval(Rec);
                    end;
                }
                separator(Separator1*********)
                {
                }
                action("Re&lease")
                {
                    ApplicationArea = all;
                    Caption = 'Re&lease';
                    ShortCutKey = 'Ctrl+F11';
                    Image = ReleaseDoc;
                    trigger OnAction()
                    begin
                        CheckDimensions();
                        CheckAppliesAmounts();
                        TestField("Posting Date");
                        TestField("Account Type", "Account Type"::"Bank Account");
                        IF WorkflowManagement.CanExecuteWorkflow(Rec, allinoneCU.RunworkflowOnSendJournalVoucherforApprovalCode()) then
                            error('Workflow is enabled. You can not release manually.');

                        // >>>>>> G2S CAS-01282-W9Y4B4 15/05/24
                        validateVoucherLine();
                        // <<<<<< G2S CAS-01282-W9Y4B4 15/05/24

                        IF Status <> Status::Released then BEGIN
                            Status := Status::Released;
                            Modify();
                            Message('Document has been Released.');
                        end;
                    end;
                }
                action("Re&open")
                {
                    ApplicationArea = all;
                    Caption = 'Re&open';
                    Image = ReOpen;
                    trigger OnAction();
                    begin
                        RecordRest.Reset();
                        RecordRest.SetRange(ID, 50117);
                        RecordRest.SetRange("Record ID", Rec.RecordId());
                        IF RecordRest.FindFirst() THEN
                            error('This record is under in workflow process. Please cancel approval request if not required.');
                        IF Status <> Status::Open then BEGIN
                            Status := Status::Open;
                            Modify();
                            Message('Document has been Reopened.');
                        end;
                    end;
                }

                separator(Separator1102152025)
                {
                }
                action("P&review Check")
                {
                    ApplicationArea = all;
                    Caption = 'P&review Check';
                    RunObject = Page "Check Preview";
                    Visible = false;
                }
                action("Print Check")
                {
                    ApplicationArea = all;
                    Caption = 'Print Check';

                    trigger OnAction();
                    var
                        GenJnlLine: Record "Gen. Journal Line" temporary;
                        GenJnlLine1: Record "Gen. Journal Line";
                        DocPrint: Codeunit "Document-Print";
                    begin
                        //Print Check
                        // SAA 3.0 >>
                        CheckHeaderLines(Rec);
                        TESTFIELD(Status, Status::Released);
                        AssignBalAcctNo := false;
                        CLEAR(GenJnlLine1);
                        GenJnlLine1.SETRANGE("Journal Template Name", "Journal Template Code");
                        GenJnlLine1.SETRANGE("Journal Batch Name", "Journal Batch Name");
                        GenJnlLine1.SETRANGE(GenJnlLine1."Document No.", "Document No.");
                        if GenJnlLine1.FINDSET then
                            repeat
                                //GenJnlLine.INIT;
                                //GenJnlLine.TRANSFERFIELDS(GenJnlLine1);
                                GenJnlLine1."Bal. Account Type" := GenJnlLine1."Bal. Account Type"::"Bank Account";
                                GenJnlLine1."Bal. Account No." := Rec."Account No.";
                                GenJnlLine1.MODIFY;
                                COMMIT;
                                AssignBalAcctNo := true;
                            until GenJnlLine1.NEXT = 0;

                        // SAA 3.0 <<

                        GenJnlLine.RESET;
                        GenJnlLine.COPY(GenJnlLine1);
                        GenJnlLine.SETRANGE("Journal Template Name", "Journal Template Code");
                        GenJnlLine.SETRANGE("Journal Batch Name", "Journal Batch Name");
                        GenJnlLine.SETRANGE("Document No.", "Document No.");
                        DocPrint.PrintCheck(GenJnlLine);
                        //CODEUNIT.RUN(CODEUNIT::"Adjust Gen. Journal Balance",GenJnlLine);
                        //END;

                        // SAA3.0 >>
                        if AssignBalAcctNo then begin
                            CLEAR(GenJnlLine1);
                            GenJnlLine1.SETRANGE("Journal Template Name", "Journal Template Code");
                            GenJnlLine1.SETRANGE("Journal Batch Name", "Journal Batch Name");
                            GenJnlLine1.SETRANGE(GenJnlLine1."Document No.", "Document No.");
                            if GenJnlLine1.FINDSET then
                                repeat
                                    if GenJnlLine1."Check Printed" then begin
                                        GenJnlLine1."Check Printed" := false;
                                        GenJnlLine1.MODIFY;
                                        GenJnlLine1."Bal. Account Type" := GenJnlLine1."Bal. Account Type"::"G/L Account";
                                        GenJnlLine1."Bal. Account No." := ''; //Rec."Account No.";
                                        GenJnlLine1."Bank Payment Type" := GenJnlLine1."Bank Payment Type"::"Computer Check";
                                        GenJnlLine1."Check Printed" := true;
                                        GenJnlLine1.MODIFY;
                                        COMMIT;
                                    end;
                                until GenJnlLine1.NEXT = 0;
                        end;
                        // SAA3.0 <<
                    end;
                }
                action("Void Check")
                {
                    ApplicationArea = all;
                    Caption = 'Void Check';

                    trigger OnAction();
                    begin
                        CLEAR(Remarks);
                        d.OPEN('Enter Reason for Void and Press ENTER KEY \ Reason : #1#####################################');
                        //d.INPUT(1, Remarks);CHI 9.0
                        d.CLOSE;

                        if Remarks = '' then
                            repeat
                                MESSAGE('Please enter Reason again');
                                d.OPEN('Enter Reason for Void and Press ENTER KEY \ Reason : #1#####################################');
                                //d.INPUT(1, Remarks);CHI 9.0
                                d.CLOSE;
                            until Remarks <> '';
                        TESTFIELD(Status, Status::Released);
                        CurrPage.VoucherLines.PAGE.VoidCheque(Remarks);
                    end;
                }
                separator("---")
                {
                    Caption = '---';
                }
                action(Comments)
                {
                    ApplicationArea = all;
                    Caption = 'Comments';
                    RunObject = Page "Approval Comments";
                    RunPageLink = "Document Type" = FILTER('BPV'),
                                  "Document No." = FIELD("Document No.");
                }
            }
            /*
            group("Upload JVS")
            {
                action("Import From Excel")
                {
                    Caption = 'Import Sales JVS';
                    ApplicationArea = all;
                    Image = Import;
                    RunObject = report "Import Sales JVS";
                }
            }*/
            group("P&osting")
            {
                Caption = 'P&osting';
                action("P&ost")
                {
                    ApplicationArea = all;
                    Caption = 'P&ost';
                    ShortCutKey = 'F11';
                    Visible = false;

                    trigger OnAction();
                    begin
                        GenJrnlLine2.SETRANGE(GenJrnlLine2."Document No.", "Document No.");
                        if GenJrnlLine2.FINDSET then
                            repeat
                                if GenJrnlLine2."Bank Payment Type" = GenJrnlLine2."Bank Payment Type"::"Computer Check" then
                                    if (GenJrnlLine2."Check Printed" = false) or (GenJrnlLine2."Computer Check No." = '') then
                                        ERROR('You must Print a Cheque');
                            until GenJrnlLine2.NEXT = 0;
                        VoucherPost.RUN(Rec);
                    end;
                }
                action(Preview)
                {
                    ApplicationArea = all;
                    Caption = 'Preview';
                    ShortCutKey = 'Shift+F2';

                    trigger OnAction();
                    var
                        GeneralJrnlBtchLRec: Record "Gen. Journal Batch";
                        DocumentNo: Code[20];
                        GLRegGRec: Record "G/L Register";
                        genJounlin: Record "Gen. Journal Line 2";
                        VoucherPreview: Codeunit "Voucher Preview Posting";
                    begin
                        DocumentNo := "Document No.";
                        ClearValues();
                        VoucherPreview.RUN(Rec);
                    end;
                }
                action("Post and &Print")
                {
                    ApplicationArea = all;
                    Caption = 'Post and &Print';
                    // Enabled = PaymentStatus; // >>>>>> G2S 14/01/2025 CAS-01386-K2V6T3
                    ShortCutKey = 'Shift+F11';
                    Enabled = BankPaymentStatus;

                    trigger OnAction();
                    begin
                        // SAA 3.0 >>
                        CheckHeaderLines(Rec);
                        // SAA 3.0 <<
                        VoucherPost.RUN(Rec);


                    end;
                }
                action("Bank Payment Voucher Test Report")
                {
                    trigger OnAction()
                    var
                        VouHeader: Record "Voucher Header";
                    BEGIN
                        VouHeader.RESET;
                        VouHeader.SETRANGE("Voucher Type", "Voucher Type");
                        VouHeader.SETRANGE("Document No.", "Document No.");
                        if VouHeader.FINDFIRST then
                            REPORT.RUN(50563, true, false, VouHeader);
                    END;
                }

                //Balu ********>>
                action("Open Excel")
                {
                    ApplicationArea = all;
                    Caption = 'Open Excel';
                    Image = Open;
                    trigger OnAction()
                    var
                        GlLine2: Record "Gen. Journal Line 2";
                    begin
                        GlLine2.CreateExcel(Rec);
                    end;
                }
                //Balu ********<<
            }
        }
        area(processing)
        {
            action("&Print")
            {
                ApplicationArea = all;
                Caption = '&Print';
                Ellipsis = true;
                Promoted = true;
                PromotedCategory = Process;

                trigger OnAction();
                begin
                    VoucherHeader.SETRANGE("Voucher Type", "Voucher Type");
                    VoucherHeader.SETRANGE("Document No.", "Document No.");
                    if VoucherHeader.FINDFIRST then
                        //REPORT.RUN(50195, true, false, VoucherHeader);
                        Report.Run(50083, true, false, VoucherHeader);
                end;
            }
        }
    }

    trigger OnDeleteRecord(): Boolean;
    begin
        DelDocNoArchive.ArchiveNo("Document No.", 20, TODAY, TIME, USERID, DATABASE::"Voucher Header"); //HO1.0
        // B2BMS08022021>>
        TestField(Status, Status::Open);
        // B2BMS08022021<<
    end;

    trigger OnNewRecord(BelowxRec: Boolean);
    begin
        "Dim. Document Type" := "Dim. Document Type"::BPV;
        // SAA 3.0 >>
        "Responsibility Center" := UserMg.GetVoucherFilter();
        // SAA 3.0 <<
    end;

    trigger OnAfterGetCurrRecord()
    begin
        BankPaymentStatus := CheckBankpaymentStatus();
    end;

    trigger OnOpenPage();
    begin
        // SAA 3.0 >>
        if UserMg.GetVoucherFilter() <> '' then begin
            FILTERGROUP(2);
            SETRANGE("Responsibility Center", UserMg.GetVoucherFilter());
            FILTERGROUP(0);
        end;
        // SAA 3.0 <<
    end;

    trigger OnModifyRecord(): Boolean
    BEGIN
        TestField(Status, Status::Open);
    END;

    trigger OnAfterGetRecord()
    BEGIN
        Teller();
        OpenAppEntrExistsForCurrUser := approvalmngmt.HasOpenApprovalEntriesForCurrentUser(RecordId());
        OpenApprEntrEsists := approvalmngmt.HasOpenApprovalEntries(RecordId());
        CanCancelapprovalforrecord := approvalmngmt.CanCancelApprovalForRecord(RecordId());
        workflowwebhookmangt.GetCanRequestAndCanCancel(RecordId(), CanrequestApprovForFlow, CanCancelapprovalforflow);

    END;

    procedure Teller()
    begin
        if "Payment Mode" = "Payment Mode"::Cheque then
            TellerVar := true
        else
            TellerVar := false;
    end;

    var
        TellerVar: Boolean;
        VoucherHeader: Record "Voucher Header";
        ApprovalMgt: Codeunit 1535;
        ApprovalEntry: Record "Approval Entry";
        ReleaseVoucher: Codeunit "Voucher Release";
        ApprovalEntries: Page 658;
        ChangeExchangeRate: Page "Change Exchange Rate";
        GenJrnlLine: Record "Gen. Journal Line";
        GenJrnlLine2: Record "Gen. Journal Line";
        ReportPrint: Codeunit "Test Report-Print";
        VoucherPost: Codeunit "Voucher Post";
        Text50201: Label 'To be collected by must not be Blank';
        Text50202: Label 'Cheque No must not be blank';
        UserMgt: Codeunit "User Setup Management";
        UserMg: Codeunit "User Setup Management Ext";
        AssignBalAcctNo: Boolean;
        Text50000: Label 'Do wish to the Cancel the Approval Request of this Document?';
        d: Dialog;
        Remarks: Text[80];
        DelDocNoArchive: Codeunit "Deleted Doc. No. Archive";
        WorkflowManagement: Codeunit "Workflow Management";
        allinoneCU: codeunit IJLSubEvents;
        RecordRest: record "Restricted Record";
        approvalmngmt: Codeunit "Approvals Mgmt.";
        OpenApprEntrEsists: Boolean;
        CanrequestApprovForFlow: Boolean;
        CanCancelapprovalforrecord: Boolean;
        CanCancelapprovalforflow: Boolean;
        OpenAppEntrExistsForCurrUser: Boolean;
        workflowwebhookmangt: Codeunit "Workflow Webhook Management";
        OpenApprovalEntriesExistForCurrUser: Boolean;
        BankPaymentStatus: Boolean;


    procedure CheckHeaderLines(VoucherHeaderRec: Record "Voucher Header");
    var
        GenJnlLine: Record "Gen. Journal Line 2";//PK-GJ2
        VendorLedgerEntry: Record "Vendor Ledger Entry";
        Vendor: Record Vendor;
    begin
        with VoucherHeaderRec do begin
            TestField("Shortcut Dimension 1 Code");
            TestField("Shortcut Dimension 2 Code");
            TESTFIELD("Transaction Type");
            TESTFIELD("Account No.");
            if "Transaction Type" = "Transaction Type"::Import then
                TESTFIELD("Import File No.");
            TESTFIELD(Narration);
            if "Account Type" <> "Account Type"::"Bank Account" then
                TESTFIELD("Responsibility Center");

            IF "Payment Mode" <> "Payment Mode"::"E-Payment" then
                if ToBeCollectedBy = '' then
                    ERROR(Text50201);

            //IF "Teller / Cheque No." = '' THEN
            //ERROR(Text50202);

            TESTFIELD("Shortcut Dimension 2 Code");
            TESTFIELD("Shortcut Dimension 1 Code");
            TESTFIELD("Posting Date");
            TESTFIELD(Narration);

            IF "Payment Mode" <> "Payment Mode"::"E-Payment" then
                TESTFIELD(ToBeCollectedBy);
            TESTFIELD(PaymentSettlementOf);

            if "Payable To" <> "Payable To"::Others then begin
                TESTFIELD("Payable Code");
                TESTFIELD("Payable To");
            end;
            /*
            IF VoucherHeader."Payment Mode" = VoucherHeader."Payment Mode"::Teller THEN
              VoucherHeader.TESTFIELD("Teller Bank Name") ELSE
              VoucherHeader.TESTFIELD("Bank Name");
            */

            //TESTFIELD("Teller / Cheque No.");

            GenJnlLine.SETRANGE(GenJnlLine."Journal Template Name", "Journal Template Code");
            GenJnlLine.SETRANGE(GenJnlLine."Journal Batch Name", "Journal Batch Name");
            GenJnlLine.SETRANGE(GenJnlLine."Document No.", "Document No.");
            if GenJnlLine.FIND('-') then begin
                repeat

                    if "Import File No." <> '' then
                        GenJnlLine.TESTFIELD("Charge Code");
                    GenJnlLine.TESTFIELD("Account No.");
                    GenJnlLine.TESTFIELD("Posting Date");
                    GenJnlLine.TESTFIELD(Amount);
                    GenJnlLine.TESTFIELD("Shortcut Dimension 1 Code");
                    GenJnlLine.TESTFIELD("Description 2");

                    if GenJnlLine."Account Type" = GenJnlLine."Account Type"::Vendor then begin

                        Vendor.GET(GenJnlLine."Account No.");
                        GenJnlLine.TESTFIELD("External Document No.");
                        //added based on change request 154
                        /*IF Vendor."Vendor Type" = Vendor."Vendor Type"::Import THEN BEGIN
                          IF Vendor."Currency Code" <> '' THEN BEGIN
                            IF GenJnlLine."Currency Code" <> Vendor."Currency Code" THEN
                              ERROR('Currency Code must be %1 on line %2 for this vendor %3',Vendor."Currency Code",GenJnlLine."Line No.",
                                Vendor.Name);
                          END ELSE
                            ERROR('Currency Code must not be blank for this vendor %1',Vendor.Name);
                            // GenJnlLine.TESTFIELD("Currency Code");
                            END; */
                        if (Vendor."Service Group" = Vendor."Service Group"::Supplier) or
                          (Vendor."Service Group" = Vendor."Service Group"::Contractor) then begin
                            GenJnlLine.TESTFIELD("Vendor Payment Type");
                            //  IF  GenJnlLine."Document No." <> 'BPR105156'  THEN BEGIN
                            if GenJnlLine."Vendor Payment Type" = GenJnlLine."Vendor Payment Type"::Advance then begin
                                GenJnlLine.TESTFIELD("LPO No.");
                                GenJnlLine.TESTFIELD("PDS No.");
                            end;


                            //GenJnlLine.TESTFIELD("Applies-to Doc. Type");
                            //GenJnlLine.TESTFIELD("Applies-to Doc. No.");

                            //       IF GenJnlLine."Applies-to Doc. Type" = 0 THEN
                            //         ERROR('Applies-to Doc. Type must not be blank for this %1 Vendor',Vendor."Service Group");
                            // COMMENTED DUE TO PROBLEM WHILE POSTING THE BANK PAYMENT VOUCHERS WITHOUT APPLICATION.

                            if ((GenJnlLine."Applies-to Doc. No." = '') and (GenJnlLine."Applies-to ID" = '')) then
                                ERROR('Applies-to Doc. No. or Applies-to ID must not be blank for this Vendor', Vendor."Service Group");
                        end;
                        //END;

                    end;

                    //GenJnlLine."Posting Date" := TODAY;
                    if GenJnlLine."Account Type" = GenJnlLine."Account Type"::"Fixed Asset" then
                        GenJnlLine.TESTFIELD("FA Posting Type");
                    //Nyo
                    if GenJrnlLine."Account Type" = GenJrnlLine."Account Type"::"Fixed Asset" then
                        GenJnlLine.TESTFIELD("FA Posting Type");
                    if (GenJnlLine."Account Type" = GenJnlLine."Account Type"::Customer)
                      or (GenJnlLine."Bal. Account Type" = GenJnlLine."Bal. Account Type"::Customer) then
                        GenJnlLine.TESTFIELD("Responsibility Center");
                    /*if (GenJnlLine."FA Posting Type" = GenJnlLine."FA Posting Type"::"capital work in progress") or
                (GenJnlLine."FA Posting Type" = GenJnlLine."FA Posting Type"::"Acquisition Cost") then begin
                        //TESTFIELD("Shortcut Dimension 4 Code");
                        GenJnlLine.TESTFIELD("Capex No.");
                        GenJnlLine.TESTFIELD("Capex Line No.");
                    end;*///CHI 9.0
                    //Nyo
                    if GenJnlLine."FA Posting Type" = GenJnlLine."FA Posting Type"::Maintenance then begin
                        GenJnlLine.TESTFIELD("Maintenance Code");
                    end;


                    if GenJnlLine."Account Type" = GenJnlLine."Account Type"::"G/L Account" then begin
                        GenJnlLine.TESTFIELD("Gen. Bus. Posting Group", '');
                        GenJnlLine.TESTFIELD("Gen. Prod. Posting Group", '');
                        GenJnlLine.TESTFIELD("Gen. Posting Type", 0);
                        GenJnlLine.TESTFIELD("VAT Bus. Posting Group", '');
                        GenJnlLine.TESTFIELD("VAT Prod. Posting Group", '');
                        GenJnlLine.TESTFIELD("Bal. Gen. Bus. Posting Group", '');
                        GenJnlLine.TESTFIELD("Bal. Gen. Prod. Posting Group", '');
                        GenJnlLine.TESTFIELD("Bal. VAT Bus. Posting Group", '');
                        GenJnlLine.TESTFIELD("Bal. VAT Prod. Posting Group", '');
                    end;

                    if GenJnlLine."IC Partner Code" <> '' then
                        GenJnlLine.TESTFIELD("IC Partner G/L Acc. No.");

                /* if "Shortcut Dimension 12 Code" <> '' then
                     GenJnlLine.TESTFIELD("Shortcut Dimension 13 Code");*///CHI2.0

                //GenJnlLine.MODIFY;

                until GenJnlLine.NEXT = 0;
            end;
        end;

    end;

    local procedure CheckBankpaymentStatus(): Boolean
    var
    begin
        if Rec."Bank Payment Status" = Rec."Bank Payment Status"::Successful then
            EXIT(true) else
            EXIT(false);
    end;

    /// >>>>>> G2S CAS-01282-W9Y4B4 15/05/24
    internal procedure validateVoucherLine()
    var
        bankPaymentVoucherSubform: record "Gen. Journal Line 2";
    begin
        if "Voucher Type" = "Voucher Type"::BPV then begin
            bankPaymentVoucherSubform.SetCurrentKey("Document No.");
            bankPaymentVoucherSubform.SetFilter("Document No.", "Document No.");
            if bankPaymentVoucherSubform.FindSet() then begin
                repeat
                    bankPaymentVoucherSubform.Validate("Multiple Batch", false);
                    bankPaymentVoucherSubform.modify();
                until bankPaymentVoucherSubform.Next() = 0;
            end;
        end;
        Commit();
    end;
    /// <<<<<< G2S CAS-01282-W9Y4B4 15/05/24

    procedure PostingDateValidate();
    var
        Text50201: Label 'Posting Date must not be a future date';
    begin
        if "Posting Date" > TODAY then
            ERROR(Text50201);
    end;

    local procedure CheckDimensions()
    var
        GenJnlLine: Record "Gen. Journal Line 2";
    begin
        TestField("Shortcut Dimension 1 Code");
        TestField("Shortcut Dimension 2 Code");
        GenJnlLine.Reset();
        GenJnlLine.SetRange("Journal Batch Name", "Journal Template Code");
        GenJnlLine.SetRange("Journal Batch Name", "Journal Batch Name");
        GenJnlLine.SetRange("Document No.", "Document No.");
        GenJnlLine.SetRange("Voucher type", GenJnlLine."Voucher type"::BPV);
        if GenJnlLine.FindSet() then
            repeat
                GenJnlLine.TestField("Shortcut Dimension 1 Code");
                GenJnlLine.TestField("Shortcut Dimension 2 Code");
                GenJnlLine.TestField(Amount);
                GenJnlLine.TestField("Posting Date");
            until GenJnlLine.Next() = 0;
    end;
}

