report 50047 "Non Returnable GatePass"
{
    UsageCategory = Administration;
    ApplicationArea = All;
    RDLCLayout = 'Nrgpwithdisclaimer.rdl'; //'Nrgp.rdl';
    Caption = 'Non Returnable GatePass_50047';

    dataset
    {
        dataitem("Posted Gate Entry Header"; "Posted Gate Entry Header")
        {
            column(No_; "No.")
            {

            }

            column(Posting_Date; FORMAT("Posting Date") + ' ' + FORMAT("Posting Time"))//PKONAU27
            {

            }
            column(Location_Code; "Location Code")
            {

            }
            column(Transporter_No_; "Transporter No.")
            {
            }
            column(Transporter_Name; "Transporter Name")
            {

            }
            column(User_ID; "User ID")
            {

            }
            column(Vehicle_No_; "Vehicle No.")
            {
            }

            column(DispatchedNoCaption; DispatchedNoCaptionLbl)
            {

            }
            column(DispatchedDateCaption; DispatchedDateCaptionLbl)
            {

            }
            column(NoCaption; NoCaptionLbl)
            {

            }
            column(DescriptionCaption; DescriptionCaptionLbl)
            {

            }
            column(QtytoDispatchCaption; QtytoDispatchCaptionLbl)
            {

            }
            column(TransporteCaption; TransporteCaptionLbl)
            {

            }
            column(TransporterNameCaption; TransporterNameCaptionLbl)
            {

            }
            column(VehicleFACaption; VehicleFACaptionLbl)
            {

            }
            column(VehicleLcCaptionLbl; VehicleLcCaptionLbl)
            {

            }
            column(DriverNameCaption; DriverNameCaptionLbl)
            {

            }
            column(TransporterCaption; TransporterCaptionLbl)
            {

            }
            column(StoreCaption; StoreCaptionLbl)
            {

            }
            column(WarrantRequired; WarrantyReq)
            {

            }
            dataitem("Posted Gate Entry Line"; "Posted Gate Entry Line")
            {
                DataItemLink = "Entry Type" = field("Entry Type"), Type = field(Type), "Gate Entry No." = field("No.");
                column(Challan_No_; "Challan No.")
                {

                }
                column(Source_Type; "Source Type")
                {

                }
                column(source_No; "Source No.")
                {

                }
                column(Description; Description)
                {
                }
                column(Line_No_; "Line No.")
                {

                }
                column(Gate_Entry_No_; "Gate Entry No.")
                {

                }
                column(CompanyInfoPicture; CompanyInfo.Picture)
                {
                }
                column(TodayDate; FORMAT(Today, 0, 4))
                {

                }
                column(NoLRec; NoLRec)
                {

                }
                column(DescriptionLVar; DescriptionLVar)
                {

                }
                column(QtyDispatchLRec; QtyDispatchLRec)
                {

                }
                column(DriverGRec; DriverGRec)
                {

                }
                column(Tonn; Tonn)
                {

                }
                column(PostedLoadingSlip; 'Driver No: ' + PostedLoadingSlip."Driver Name")
                {

                }
                column(PostedLoadingSlipCN; 'Driver Contact No.' + PostedLoadingSlip."Driver Contact No.")
                {

                }
                column(TranspoterName; 'Transpoter Details  ' + TranspoterName)
                {

                }

                trigger OnAfterGetRecord()


                begin
                    PostedLoadingSlip.Reset();
                    PostedLoadingSlip.SetRange("No.", "Source No.");
                    IF PostedLoadingSlip.FindFirst() then;
                    Clear(DescriptionLVar);
                    Clear(NoLRec);
                    clear(QtyDispatchLRec);
                    clear(DriverGRec);
                    Clear(Tonn);
                    Clear(TranspoterName);
                    case "Source Type" of
                        "Source Type"::"Sales Shipment":
                            BEGIN
                                TranspoterName := "Posted Gate Entry Header"."Transporter No." + ' ' + "Posted Gate Entry Header"."Transporter Name";
                                SalesShipmentHeaderLRec.reset;
                                SalesShipmentHeaderLRec.SetRange("No.", "Source No.");
                                IF SalesShipmentHeaderLRec.findfirst then begin
                                    SalesShipmentLinLRec.Reset();
                                    SalesShipmentLinLRec.SetRange("Document No.", SalesShipmentHeaderLRec."No.");
                                    SalesShipmentLinLRec.SetRange("Line No.", "Line No.");
                                    if SalesShipmentLinLRec.FindSet() then
                                        repeat
                                            DescriptionLVar := SalesShipmentLinLRec.Description;
                                            NoLRec := SalesShipmentLinLRec."No.";
                                            QtyDispatchLRec := SalesShipmentLinLRec.Quantity;
                                        until SalesShipmentLinLRec.Next() = 0;
                                end
                            End;
                        "Source Type"::"Posted Loading Slip":
                            BEGIN
                                loadingSlipHrdLRec.reset;
                                loadingSlipHrdLRec.SetRange("No.", "Source No.");
                                IF loadingSlipHrdLRec.findfirst then begin

                                    WarrantyReq := false;
                                    Destinationrec.SetRange("Destination code", loadingSlipHrdLRec."Transport To Location");
                                    if Destinationrec.FindSet() then
                                        if destinationrec."Destination Type" = destinationrec."Destination Type"::"Outside Lagos" then
                                            WarrantyReq := true else
                                            WarrantyReq := false;

                                    loadingSlipLineLRec.Reset();
                                    loadingSlipLineLRec.SetRange("Document No.", "Source No.");
                                    loadingSlipLineLRec.SetRange("Line No.", "Source Line No.");
                                    if loadingSlipLineLRec.Findfirst() then
                                        repeat
                                            DescriptionLVar := loadingSlipLineLRec.Description;
                                            NoLRec := loadingSlipLineLRec."No.";
                                            QtyDispatchLRec := loadingSlipLineLRec."Qty. Loading";
                                            DriverGRec := loadingSlipHrdLRec."Driver Name";
                                            Tonn := loadingSlipLineLRec."Wt. of the Qty Loading in Tons";
                                            if loadingSlipHrdLRec."Vehicle No." <> '' then begin
                                                if Vendor.Get(loadingSlipHrdLRec."Party No.") then
                                                    TranspoterName := loadingSlipHrdLRec."Party No." + '_' + Vendor.Name;
                                            end else
                                                TranspoterName := 'Self';
                                        until loadingSlipLineLRec.Next() = 0;
                                end;
                            END;
                        "Source Type"::"Purchase Return Shipment":
                            BEGIN
                                PurchaseRetnLRec.reset;
                                PurchaseRetnLRec.SetRange("No.", "Source No.");
                                IF PurchaseRetnLRec.findfirst then begin
                                    PurchaseRetLinLRec.Reset();
                                    PurchaseRetLinLRec.SetRange("Document No.", PurchaseRetnLRec."No.");
                                    if PurchaseRetLinLRec.FindSet() then
                                        repeat
                                            DescriptionLVar := PurchaseRetLinLRec.Description;
                                            NoLRec := PurchaseRetLinLRec."No.";
                                            QtyDispatchLRec := PurchaseRetLinLRec.Quantity;
                                        until PurchaseRetLinLRec.Next() = 0;
                                end

                            END;
                    end;
                end;


            }
            trigger OnAfterGetRecord()


            begin
                PostedLoadingSlip.Reset();
                PostedLoadingSlip.SetRange("Gate Entry No.", "No.");

                IF PostedLoadingSlip.findfirst then begin

                    WarrantyReq := false;
                    Destinationrec.SetRange("Destination code", PostedLoadingSlip."Transport To Location");
                    if Destinationrec.FindSet() then
                        if destinationrec."Destination Type" = destinationrec."Destination Type"::"Outside Lagos" then
                            WarrantyReq := true else
                            WarrantyReq := false;
                end;

            end;

        }
    }
    requestpage
    {
        layout
        {
            /* area(Content)
             {
                 group(GroupName)
                 {
                     field(No; NoLRec)
                     {

                     }

                 }
             }*/
        }

        actions
        {
            area(processing)
            {
                action(ActionName)
                {
                    ApplicationArea = All;

                }
            }
        }
    }
    trigger OnInitReport()
    begin
        CompanyInfo.Get;
        CompanyInfo.CalcFields(Picture);
    end;

    var
        CompanyInfo: Record "Company Information";
        DispatchedDateCaptionLbl: Label 'Dispatched Date';
        DispatchedNoCaptionLbl: Label 'Dispatched No.';
        NoCaptionLbl: Label 'No.';
        DescriptionCaptionLbl: label 'Description';
        QtytoDispatchCaptionLbl: label 'Qty.to.Dispatch';
        TransporteCaptionLbl: Label 'Transporter No.';
        TransporterNameCaptionLbl: Label 'Transporter Name';
        VehicleFACaptionLbl: Label 'Vehicle No.';
        VehicleLcCaptionLbl: Label 'Vehicle Lic Plate No.';
        DriverNameCaptionLbl: Label 'Driver Name';
        TransporterCaptionLbl: Label 'Transporter Sign';
        StoreCaptionLbl: label 'Store Office Sign';

        SalesShipmentHeaderLRec: record "Sales Shipment Header";
        SalesShipmentLinLRec: record "Sales Shipment Line";
        loadingSlipHrdLRec: record "Posted Loading Slip Header";
        loadingSlipLineLRec: Record "Posted Loading Slip Line";
        PurchaseRetnLRec: record "Return Shipment Header";
        PurchaseRetLinLRec: Record "Return Shipment Line";
        DescriptionLVar: text[250];
        NoLRec: Code[20];
        QtyDispatchLRec: Decimal;
        DriverGRec: text[250];
        UOMCaptionLbl: Label 'UOM';
        Tonn: Decimal;
        PostedLoadingSlip: Record "Posted Loading SLip Header";
        TranspoterName: Text;
        Vendor: Record Vendor;
        WarrantyReq: Boolean;
        Destinationrec: record Destination;
}