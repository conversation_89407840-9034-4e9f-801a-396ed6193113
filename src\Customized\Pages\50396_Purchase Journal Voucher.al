page 50396 "Purchase Journal Voucher"
{
    // version CHI6.0

    // PROJECT : CHI 6.0
    // **********************************************************************************
    // SIGN
    // **********************************************************************************
    // CHI     :  CHI 6.0 Developments
    // UNL     :  Univision Nigeria Limited
    // SAA     :  SAHEED ADIO ADEOSUN
    // HO      :  Henry <PERSON>ben
    // **********************************************************************************
    // VER      SIGN         DATE          DESCRIPTION
    // **********************************************************************************
    // 1.0      UNL      06-Dec-11      -> Form Created to display journal voucher.
    // 1.0      HO       07-Sep-12   -> Code added to "Form-OnDelereRecord()" to allow Archive of deleted Journal Voucher Document No.

    PageType = Document;
    SourceTable = "Voucher Header";
    SourceTableView = WHERE("Voucher Type" = FILTER(JV),
                            "JV Type" = FILTER(" " | Purchase),
                            Status = FILTER(<> Released));
    layout
    {
        area(content)
        {
            group(General)
            {
                Caption = 'General';
                field("Document No."; "Document No.")
                {
                    ApplicationArea = all;

                    trigger OnAssistEdit();
                    begin
                        if AssistEdit(xRec) then
                            CurrPage.UPDATE;
                    end;
                }
                field("Document Date"; "Document Date")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Posting Date"; "Posting Date")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field(Narration; Narration)
                {
                    ApplicationArea = all;
                }
                field("Amount (LCY)"; "Amount (LCY)")
                {
                    ApplicationArea = all;
                }
                field(Status; Status)
                {
                    ApplicationArea = all;
                }
            }
            part(VoucherLines; "Purchase JV Subform")
            {
                ApplicationArea = all;
                SubPageLink = "Journal Template Name" = FIELD("Journal Template Code"),
                              "Journal Batch Name" = FIELD("Journal Batch Name"),
                              "Document No." = FIELD("Document No.");
            }
            group(Usertrail)
            {
                Caption = 'Usertrail';
                field("Created By"; "Created By")
                {
                    ApplicationArea = all;
                }
                field("Created By Name"; "Created By Name")
                {
                    ApplicationArea = all;
                }
                field("Created Date"; "Created Date")
                {
                    ApplicationArea = all;
                }
                field("Created Time"; "Created Time")
                {
                    ApplicationArea = all;
                }
                field("Modified By"; "Modified By")
                {
                    ApplicationArea = all;
                }
                field("Modified By Name"; "Modified By Name")
                {
                    ApplicationArea = all;
                }
                field("Modified Date"; "Modified Date")
                {
                    ApplicationArea = all;
                }
                field("Modified Time"; "Modified Time")
                {
                    ApplicationArea = all;
                }
            }
        }
        area(FactBoxes)
        {
            part("Attached Documents"; "Document Attachment Factbox")
            {
                ApplicationArea = All;
                Caption = 'Attachments';
                SubPageLink = "Table ID" = CONST(50117),
                                "No." = FIELD("Document No.");
                // Type = FIELD("Voucher Type");
            }
            systempart(PyamentTermsLinks; Links)
            {
                ApplicationArea = RecordLinks;
            }
            systempart(PyamentTermsNotes; Notes)
            {
                ApplicationArea = Notes;
            }
        }
        //g2s29Dev23
    }

    actions
    {
        area(navigation)
        {
            group("&Voucher")
            {
                Caption = '&Voucher';
                separator(Separator1000000067)
                {
                }
                action(Dimensions)
                {
                    ApplicationArea = all;
                    Caption = 'Dimensions';

                    trigger OnAction();
                    begin
                        ShowDocDim;
                    end;
                }
                separator(Separator1000000069)
                {
                }
                action(Approvals)
                {
                    ApplicationArea = all;
                    Caption = 'Approvals';

                    trigger OnAction();
                    begin
                        approvalmngmt.ApproveRecordApprovalRequest(RecordId());
                    end;
                }
                action(InsertLine)
                {
                    Visible = false;//Balu OnFeb22
                    ApplicationArea = all;
                    Caption = 'Insert Line';
                    Promoted = true;
                    PromotedCategory = Process;
                    ShortcutKey = 'Ctrl+I';
                    trigger OnAction();
                    var
                        genjoulrec: Record "Gen. Journal Line";
                        linen: Integer;
                    begin
                        genjoulrec.RESET();
                        genjoulrec.SetRange("Journal Template Name", 'PURCHASES');
                        genjoulrec.SetRange("Journal Batch Name", 'PURCHJV');
                        //genjoulrec.SetRange("Document No.", "Document No.");
                        IF genjoulrec.FINDLAST THEN
                            linen := 10000 + genjoulrec."Line No."
                        else
                            linen := 10000;

                        clear(genjoulrec);
                        genjoulrec.init();
                        genjoulrec."Journal Template Name" := 'PURCHASES';
                        genjoulrec."Journal Batch Name" := 'PURCHJV';
                        genjoulrec."Voucher Type" := genjoulrec."Voucher Type"::JV;
                        genjoulrec."Document No." := "Document No.";
                        genjoulrec."Posting Date" := Today; //PJ
                        genjoulrec."Line No." := linen;
                        genjoulrec.insert(True);
                        Message('Line Inserted %1..%2', genjoulrec."Journal Template Name", genjoulrec."Journal Batch Name");
                        //insert(true);
                        CurrPage.Update();
                    end;
                }
            }
            group("F&unctions")
            {
                Caption = 'F&unctions';
                action("Send Approval Request")
                {
                    ApplicationArea = All;
                    Image = SendApprovalRequest;
                    Visible = Not OpenApprEntrEsists and CanrequestApprovForFlow;
                    Promoted = true;
                    PromotedIsBig = true;
                    PromotedCategory = Process;
                    PromotedOnly = true;
                    trigger OnAction()
                    Var
                        GeneralJournalLines: Record "Gen. Journal Line 2";
                    begin
                        GeneralJournalLines.Reset();
                        GeneralJournalLines.SetRange("Journal Template Name", "Journal Template Code");
                        GeneralJournalLines.SetRange("Journal Batch Name", "Journal Batch Name");
                        GeneralJournalLines.SetRange("Voucher type", "Voucher Type");
                        GeneralJournalLines.SetRange("Document No.", "Document No.");
                        if GeneralJournalLines.FindSet() then begin
                            GeneralJournalLines.CalcSums(Amount);
                            if GeneralJournalLines.Amount <> 0 then
                                Error('Balance Not matching');
                        end;
                        TestField("Posting Date");
                        CheckHeaderLines(Rec);
                        IF allinoneCU.CheckJournalVoucherApprovalsWorkflowEnabled(Rec) then
                            allinoneCU.OnSendJournalVoucherForApproval(Rec);
                    end;
                }
                action("Cancel Approval Request")
                {
                    ApplicationArea = All;
                    Image = CancelApprovalRequest;
                    Visible = CanCancelapprovalforrecord or CanCancelapprovalforflow;
                    Promoted = true;
                    PromotedIsBig = true;
                    PromotedCategory = Process;
                    PromotedOnly = true;
                    trigger OnAction()
                    begin
                        allinoneCU.OnCancelJournalVoucherForApproval(Rec);
                    end;
                }
                separator(Separator1000000049)
                {
                }
                action("Re&lease")
                {
                    ApplicationArea = all;
                    Caption = 'Re&lease';
                    ShortCutKey = 'Ctrl+F11';
                    Image = ReleaseDoc;
                    trigger OnAction()
                    Var
                        GeneralJournalLines: Record "Gen. Journal Line 2";
                    begin
                        CheckHeaderLines(Rec);
                        GeneralJournalLines.Reset();
                        GeneralJournalLines.SetRange("Journal Template Name", "Journal Template Code");
                        GeneralJournalLines.SetRange("Journal Batch Name", "Journal Batch Name");
                        GeneralJournalLines.SetRange("Voucher type", "Voucher Type");
                        GeneralJournalLines.SetRange("Document No.", "Document No.");
                        if GeneralJournalLines.FindSet() then begin
                            GeneralJournalLines.CalcSums(Amount);
                            if GeneralJournalLines.Amount <> 0 then
                                Error('Balance Not matching');
                        end;
                        TestField("Posting Date");
                        IF WorkflowManagement.CanExecuteWorkflow(Rec, allinoneCU.RunworkflowOnSendJournalVoucherforApprovalCode()) then
                            error('Workflow is enabled. You can not release manually.');

                        IF Status <> Status::Released then BEGIN
                            Status := Status::Released;
                            Modify();
                            Message('Document has been Released.');
                        end;
                    end;
                }
                action("Re&open")
                {
                    ApplicationArea = all;
                    Caption = 'Re&open';
                    Image = ReOpen;
                    trigger OnAction();
                    begin
                        RecordRest.Reset();
                        RecordRest.SetRange(ID, 50117);
                        RecordRest.SetRange("Record ID", Rec.RecordId());
                        IF RecordRest.FindFirst() THEN
                            error('This record is under in workflow process. Please cancel approval request if not required.');
                        IF Status <> Status::Open then BEGIN
                            Status := Status::Open;
                            Modify();
                            Message('Document has been Reopened.');
                        end;
                    end;
                }
                separator("---")
                {
                    Caption = '---';
                }
                action(Comments)
                {
                    ApplicationArea = all;
                    Caption = 'Comments';
                    RunObject = Page "Approval Comments";
                    RunPageLink = "Document Type" = FILTER('JV'),
                                  "Document No." = FIELD("Document No.");
                }
            }
            group("P&osting")
            {
                Caption = 'P&osting';
                action("P&ost")
                {
                    ApplicationArea = all;
                    Caption = 'P&ost';
                    ShortCutKey = 'F11';

                    trigger OnAction();
                    begin
                        // SAA 3.0 >>
                        CheckHeaderLines(Rec);
                        // SAA 3.0 <<
                        VoucherPost.RUN(Rec);
                    end;
                }
                action(Preview)
                {
                    ApplicationArea = all;
                    Caption = 'Preview';
                    ShortCutKey = 'Shift+F2';

                    trigger OnAction();
                    var
                        GeneralJrnlBtchLRec: Record "Gen. Journal Batch";
                        DocumentNo: Code[20];
                        GLRegGRec: Record "G/L Register";
                        genJounlin: Record "Gen. Journal Line 2";
                        VoucherPreview: Codeunit "Voucher Preview Posting";
                    begin
                        DocumentNo := "Document No.";
                        ClearValues();
                        VoucherPreview.RUN(Rec);
                    end;
                }
                action("Post and &Print")
                {
                    ApplicationArea = all;
                    Caption = 'Post and &Print';
                    ShortCutKey = 'Shift+F11';

                    trigger OnAction();
                    begin
                        // SAA 3.0 >>
                        CheckHeaderLines(Rec);
                        // SAA 3.0 <<
                        VoucherPost.RUN(Rec);
                    end;
                }
            }
        }
        area(processing)
        {
            action("&Print")
            {
                ApplicationArea = all;
                Caption = '&Print';
                Ellipsis = true;
                Promoted = true;
                PromotedCategory = Process;

                trigger OnAction();
                begin
                    VoucherHeader.SETRANGE("Voucher Type", "Voucher Type");
                    VoucherHeader.SETRANGE("Document No.", "Document No.");
                    if VoucherHeader.FINDFIRST then
                        REPORT.RUN(Report::Voucher, true, false, VoucherHeader);
                end;
            }
            //PhaniFeb182021>>
            action("Purchase Journal Voucher Test Report")
            {
                trigger OnAction()
                var
                    VouHeader: Record "Voucher Header";
                BEGIN
                    VouHeader.RESET;
                    VouHeader.SETRANGE("Voucher Type", "Voucher Type");
                    VouHeader.SETRANGE("Document No.", "Document No.");
                    if VouHeader.FINDFIRST then
                        REPORT.RUN(50563, true, false, VouHeader);
                END;
            }
            //PhaniFeb182021<<
            //Balu 05282021>>
            action("Open Excel")
            {
                ApplicationArea = all;
                Caption = 'Open Excel';
                Image = Open;
                trigger OnAction()
                var
                    GlLine2: Record "Gen. Journal Line 2";
                begin
                    GlLine2.CreateExcel(Rec);
                end;
            }
            //Balu 05282021<<
        }
    }

    trigger OnDeleteRecord(): Boolean;
    begin
        DelDocNoArchive.ArchiveNo("Document No.", 7, TODAY, TIME, USERID, DATABASE::"Voucher Header"); //HO1.0
        // B2BMS08022021>>
        TestField(Status, Status::Open);
        // B2BMS08022021<<
    end;

    trigger OnNewRecord(BelowxRec: Boolean);
    begin
        "Dim. Document Type" := "Dim. Document Type"::JV;
        "Document Date" := TODAY;
        "JV Type" := "JV Type"::Purchase;
    end;

    trigger OnModifyRecord(): Boolean
    BEGIN
        TestField(Status, Status::Open);
    END;

    trigger OnAfterGetRecord()
    BEGIN
        OpenAppEntrExistsForCurrUser := approvalmngmt.HasOpenApprovalEntriesForCurrentUser(RecordId());
        OpenApprEntrEsists := approvalmngmt.HasOpenApprovalEntries(RecordId());
        CanCancelapprovalforrecord := approvalmngmt.CanCancelApprovalForRecord(RecordId());
        workflowwebhookmangt.GetCanRequestAndCanCancel(RecordId(), CanrequestApprovForFlow, CanCancelapprovalforflow);
    END;

    var
        VoucherHeader: Record "Voucher Header";
        ApprovalMgt: Codeunit 1535;
        ApprovalEntry: Record "Approval Entry";
        ReleaseVoucher: Codeunit "Voucher Release";
        ApprovalEntries: Page 658;
        ChangeExchangeRate: Page "Change Exchange Rate";
        GenJrnlLine: Record "Gen. Journal Line";
        ReportPrint: Codeunit "Test Report-Print";
        VoucherPost: Codeunit "Voucher Post";
        DelDocNoArchive: Codeunit "Deleted Doc. No. Archive";
        VendorNoPresent: Boolean;
        Text50000: Label 'At least one Journal Line must be a Vendor Account';
        WorkflowManagement: Codeunit "Workflow Management";
        allinoneCU: codeunit IJLSubEvents;
        RecordRest: record "Restricted Record";
        approvalmngmt: Codeunit "Approvals Mgmt.";
        OpenApprEntrEsists: Boolean;
        CanrequestApprovForFlow: Boolean;
        CanCancelapprovalforrecord: Boolean;
        CanCancelapprovalforflow: Boolean;
        OpenAppEntrExistsForCurrUser: Boolean;
        workflowwebhookmangt: Codeunit "Workflow Webhook Management";

    procedure CheckHeaderLines(VoucherHeaderRec: Record "Voucher Header");
    var
        GenJnlLine: Record "Gen. Journal Line 2";//PK-GJ2
        VendorLedgerEntry: Record "Vendor Ledger Entry";
        Vendor: Record Vendor;
        Text50200: Label 'Maturity Date must not be blank for this line no - %1';
        Text50201: Label 'The %1 in Journal Header must be zero';
        Text50202: Label 'There is no Journals lines to approve';
    begin
        with VoucherHeaderRec do begin
            TESTFIELD(Narration);
            TestField("Shortcut Dimension 1 Code");
            TestField("Shortcut Dimension 2 Code");
            VendorNoPresent := false;
            GenJnlLine.SETRANGE(GenJnlLine."Journal Template Name", "Journal Template Code");
            GenJnlLine.SETRANGE(GenJnlLine."Journal Batch Name", "Journal Batch Name");
            GenJnlLine.SETRANGE(GenJnlLine."Document No.", "Document No.");
            if GenJnlLine.FIND('-') then begin
                CALCFIELDS("Amount (LCY)");
                if "Amount (LCY)" <> 0 then
                    ERROR(Text50201, FIELDCAPTION("Amount (LCY)"));
                repeat
                    GenJnlLine.TESTFIELD("Account No.");
                    GenJnlLine.TESTFIELD("Posting Date");
                    GenJnlLine.TESTFIELD(Amount);
                    GenJnlLine.TESTFIELD("Shortcut Dimension 1 Code");
                    GenJnlLine.TESTFIELD("Shortcut Dimension 2 Code");
                    GenJnlLine.TESTFIELD(Narration);
                    if GenJnlLine."Account Type" = GenJnlLine."Account Type"::"Fixed Asset" then
                        GenJnlLine.TESTFIELD("FA Posting Type");
                    //RKD >> Capex No,capex Line No made mandatory.
                    /*
                    if (GenJnlLine."FA Posting Type" = GenJnlLine."FA Posting Type" ::"capital work in progress") or
                       (GenJnlLine."FA Posting Type" = GenJnlLine."FA Posting Type"::"Acquisition Cost") then
                    begin
                      GenJnlLine.TESTFIELD("Capex No.");
                      GenJnlLine.TESTFIELD("Capex Line No.");
                    end;*/ //CHI1.0
                           //RKD <<
                    if GenJnlLine."FA Posting Type" = GenJnlLine."FA Posting Type"::Maintenance then
                        GenJnlLine.TESTFIELD("Maintenance Code");
                    if GenJnlLine."Account Type" in [GenJnlLine."Account Type"::"G/L Account",
                      GenJnlLine."Account Type"::"Bank Account"] then begin
                        GenJnlLine.TESTFIELD("Gen. Bus. Posting Group", '');
                        GenJnlLine.TESTFIELD("Gen. Prod. Posting Group", '');
                        GenJnlLine.TESTFIELD("Gen. Posting Type", 0);
                        GenJnlLine.TESTFIELD("VAT Bus. Posting Group", '');
                        GenJnlLine.TESTFIELD("VAT Prod. Posting Group", '');
                        GenJnlLine.TESTFIELD("Bal. Gen. Bus. Posting Group", '');
                        GenJnlLine.TESTFIELD("Bal. Gen. Prod. Posting Group", '');
                        GenJnlLine.TESTFIELD("Bal. VAT Bus. Posting Group", '');
                        GenJnlLine.TESTFIELD("Bal. VAT Prod. Posting Group", '');
                    end;
                    if GenJnlLine."Account Type" in [GenJnlLine."Account Type"::Vendor] then begin
                        VendorNoPresent := true;
                        //added to mandate currency code for Import Vendors. RN:154
                        //IF GenJnlLine."Account Type" = GenJnlLine."Account Type"::Vendor THEN BEGIN
                        Vendor.GET(GenJnlLine."Account No.");
                        /*    IF Vendor."Vendor Type" = Vendor."Vendor Type"::Import THEN
                              IF Vendor."Currency Code" <> '' THEN BEGIN
                                IF GenJnlLine."Currency Code" <> Vendor."Currency Code" THEN
                                  ERROR('Currency Code must be %1 on line %2 for this vendor %3',Vendor."Currency Code",GenJnlLine."Line No.",
                                    Vendor.Name);
                              END ELSE
                                ERROR('Currency Code must not be blank for this vendor %1',Vendor.Name);
                                *///END;  //<<
                                  // GenJnlLine.TESTFIELD("Currency Code");
                    end;
                    if (GenJnlLine."Bank Doc. Type" in [3]) and (GenJnlLine."Maturity Date" = 0D) then
                        ERROR(Text50200, GenJnlLine."Line No.");
                    if GenJnlLine."Loan ID" <> '' then
                        GenJnlLine.TESTFIELD("Applies-to Doc. No.");
                until GenJnlLine.NEXT = 0;
            end else
                ERROR(Text50202);
        end;

        if not VendorNoPresent then
            ERROR(Text50000);

    end;

    procedure PostingDateValidate();
    var
        Text50201: Label 'Posting Date must not be a future date';
    begin
        if "Posting Date" > TODAY then
            ERROR(Text50201);
    end;

    procedure AccountNoValidate();
    var
        GenJnlLine: Record "Gen. Journal Line 2";//PK-GJ2
        Text50200: Label 'Please use purchase journal to make vendor corrections';
        Text50201: Label 'Please use sales journal to make customer corrections';
    begin
        //IF "Account Type" = "Account Type"::Vendor THEN
        //ERROR(Text50200) ELSE

        if "Account Type" = "Account Type"::Customer then
            ERROR(Text50201);

        //IF "Bal. Account No." <> 'BKLOGTR01' THEN
        //IF "Account Type" = "Account Type"::"Bank Account" THEN
        //TESTFIELD("Bank Doc. Type");

        //IF ("Account Type"="Account Type"::"G/L Account") AND ("Bal. Account Type" = "Bal. Account Type"::"G/L Account") THEN
        //"Bank Doc. Type" := 0;
    end;

    procedure AccountTypeValidate();
    var
        GenJnlLine: Record "Gen. Journal Line";
        Text50200: Label 'Please use purchase journal to make vendor corrections';
        Text50201: Label 'Please use sales journal to make customer corrections';
    begin
        //IF "Account Type" = "Account Type"::Vendor THEN
        //ERROR(Text50200) ELSE

        if "Account Type" = "Account Type"::Customer then
            ERROR(Text50201);

        //IF "Bal. Account No." <> 'BKLOGTR01' THEN
        //IF "Account Type" = "Account Type"::"Bank Account" THEN
        //TESTFIELD("Bank Doc. Type");

        //IF ("Account Type"="Account Type"::"G/L Account") AND ("Bal. Account Type" = "Bal. Account Type"::"G/L Account") THEN
        //  "Bank Doc. Type" := 0;
    end;
}

