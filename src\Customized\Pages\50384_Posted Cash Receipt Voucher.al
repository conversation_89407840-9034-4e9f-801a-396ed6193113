page 50384 "Posted Cash Receipt Voucher"
{
    // version CHI6.0

    // PROJECT : CHI 6.0
    // **********************************************************************************
    // SIGN
    // **********************************************************************************
    // CHI     :  CHI 6.0 Developments
    // UNL     :  Univision Nigeria Limited
    // SAA     :  SAHEED ADIO ADEOSUN
    // **********************************************************************************
    // VER      SIGN         DATE          DESCRIPTION
    // **********************************************************************************
    // 1.0      UNL      06-Dec-11      -> Form Created to display Posted Cash Rcpt Document Details.
    // 3.0      SAA      04-Apr-13      -> Added code in OnPush function of PRINT.
    // 3.0      SAA      18-Apr-17      -> Added codes to OnPush() of Print button to flag error if user does not have
    //                                     permission to reprint.

    DeleteAllowed = false;
    Editable = false;
    InsertAllowed = false;
    ModifyAllowed = false;
    PageType = Document;
    SourceTable = "Posted Voucher Header";
    SourceTableView = WHERE("Voucher Type" = FILTER(CRV));

    layout
    {
        area(content)
        {
            group(General)
            {
                Caption = 'General';
                field("Document No."; "Document No.")
                {
                }
                field("Posting Date"; "Posting Date")
                {
                }
                field("Account Type"; "Account Type")
                {
                }
                field("Account No."; "Account No.")
                {
                    Caption = 'Debit Account No.';
                }
                field("Account Name"; "Account Name")
                {
                }
                field(Narration; Narration)
                {
                }
                field("Currency Code"; "Currency Code")
                {
                }
                field("Amount (LCY)"; "Amount (LCY)")
                {
                }
            }
            part(VoucherLines; "Posted Cash Rcpt. Vchr Subform")
            {
                SubPageLink = "Document No." = FIELD("Document No."),
                              "Credit Amount" = FILTER(<> 0);
                SubPageView = SORTING("Document No.");
            }
            group(Usertrail)
            {
                Caption = 'Usertrail';
                field("Created By"; "Created By")
                {
                }
                field("Created By Name"; "Created By Name")
                {
                }
                field("Created Date"; "Created Date")
                {
                }
                field("Created Time"; "Created Time")
                {
                }
                field("Posted By"; "Posted By")
                {
                }
                field("Posted By Name"; "Posted By Name")
                {
                }
                field("Posted Date"; "Posted Date")
                {
                }
                field("Posted Time"; "Posted Time")
                {
                }
                field("Voucher No."; "Voucher No.")
                {
                }
                field("Modified By"; "Modified By")
                {
                }
                field("Modified By Name"; "Modified By Name")
                {
                }
                field("Modified Date"; "Modified Date")
                {
                }
                field("Modified Time"; "Modified Time")
                {
                }
            }
            group("Receive Details")
            {
                Caption = 'Receive Details';
                field("Receiving Type"; "Receiving Type")
                {
                }
                field("Receiving Code"; "Receiving Code")
                {
                }
                field("Received From"; "Received From")
                {
                }
            }
        }
        area(FactBoxes)
        {
            part("Attached Documents"; "Document Attachment Factbox")
            {
                ApplicationArea = All;
                Caption = 'Attachments';
                SubPageLink = "Table ID" = CONST(50118),
                                "No." = FIELD("Document No.");
                // Type = FIELD("Voucher Type");
            }
            systempart(PyamentTermsLinks; Links)
            {
                ApplicationArea = RecordLinks;
            }
            systempart(PyamentTermsNotes; Notes)
            {
                ApplicationArea = Notes;
            }
        }
        //g2s29Dev23
    }

    actions
    {
        area(navigation)
        {
            group("&Voucher")
            {
                Caption = '&Voucher';
                action(Dimensions)
                {
                    Caption = 'Dimensions';

                    trigger OnAction();
                    begin
                        ShowDocDim;
                    end;
                }
                action(Approvals)
                {
                    Caption = 'Approvals';

                    trigger OnAction();
                    var
                        PostedApprovalEntries: Page "Posted Approval Entries";
                    begin
                        //PostedApprovalEntries.Setfilters(DATABASE::"Posted Voucher Header","Document No.");}//CHI2018
                        PostedApprovalEntries.RUN;
                    end;
                }
            }
        }
        area(processing)
        {
            action("&Print")
            {
                Caption = '&Print';
                Ellipsis = true;
                Promoted = true;
                PromotedCategory = Process;

                trigger OnAction();
                begin
                    if Usersetup.GET(USERID) then //added to flag error message if user dos not have permission to reprint
                        if Usersetup."Reprint Receipt Documents" then begin  //SAA3.0 >>
                            VoucherHeader.SETRANGE("Voucher Type", "Voucher Type");
                            VoucherHeader.SETRANGE("Document No.", "Document No.");
                            if VoucherHeader.FINDFIRST then
                                REPORT.RUN(50089, true, false, VoucherHeader);  //SAA3.0
                        end else
                            ERROR(Text100);
                end;
            }
            action("&Navigate")
            {
                Caption = '&Navigate';
                Promoted = true;
                PromotedCategory = Process;

                trigger OnAction();
                begin
                    Navigate;
                end;
            }
        }
    }

    trigger OnOpenPage();
    begin
        // SAA 3.0 >>
        Usersetup.GET(USERID);
        if Usersetup.FilterResponsibilityCenter <> '' then begin
            FILTERGROUP(2);
            SETFILTER("Responsibility Center", Usersetup.FilterResponsibilityCenter);
            FILTERGROUP(0);
        end;
        // SAA 3.0 <<
    end;

    var
        PostedApprovalEntries: Page "Posted Approval Entries";
        VoucherHeader: Record "Posted Voucher Header";
        Usersetup: Record "User Setup";
        Text100: Label 'You do not have permission to reprint receipt vouchers';
}

