page 50306 "Cash Payment Voucher List"
{
    // version CHI6.0

    // PROJECT : CHI 6.0
    // **********************************************************************************
    // SIGN
    // **********************************************************************************
    // CHI     :  CHI 6.0 Developments
    // UNL     :  Univision Nigeria Limited
    // SAA     :  SAHEED ADIO ADEOSUN
    // **********************************************************************************
    // VER      SIGN         DATE          DESCRIPTION
    // **********************************************************************************
    // 1.0      UNL      06-Dec-11      -> Form Created to display voucher list.
    // 3.0      SAA      06-Apr-17      -> added "'%1'" to the setfilter in OnOpenForm.

    Editable = false;
    PageType = List;
    CardPageId = "Cash Payment Voucher New";
    SourceTable = "Voucher Header";
    UsageCategory = Lists;
    ApplicationArea = all;
    SourceTableView = SORTING("Voucher Type", "Document No.")
                      WHERE("Voucher Type" = CONST(CPV),
                            Status = FILTER(<> Released));

    layout
    {
        area(content)
        {
            repeater(Control1000000000)
            {
                field(Status; Status)
                {
                    ApplicationArea = all;
                }
                field("Posting Date"; "Posting Date")
                {
                    ApplicationArea = all;
                }
                field("Document No."; "Document No.")
                {
                    ApplicationArea = all;
                }
                field("Inbox Document No."; "Inbox Document No.")
                {
                    ApplicationArea = all;
                }
                field("Account Type"; "Account Type")
                {
                    ApplicationArea = all;
                }
                field("Account No."; "Account No.")
                {
                    ApplicationArea = all;
                }
                field("Account Name"; "Account Name")
                {
                    ApplicationArea = all;
                }
                field(Narration; Narration)
                {
                    ApplicationArea = all;
                }
                field(Amount; Amount)
                {
                    ApplicationArea = all;
                    Visible = false;
                }
                field("Amount (LCY)"; "Amount (LCY)")
                {
                    ApplicationArea = all;
                }
                field("Posting Doc. No."; "Posting Doc. No.")
                {
                    ApplicationArea = all;
                }
            }
        }
        area(FactBoxes)
        {
            part("Attached Documents"; "Document Attachment Factbox")
            {
                ApplicationArea = All;
                Caption = 'Attachments';
                SubPageLink = "Table ID" = CONST(50117),
                                "No." = FIELD("Document No.");
                // Type = FIELD("Voucher Type");
            }
            systempart(PyamentTermsLinks; Links)
            {
                ApplicationArea = RecordLinks;
            }
            systempart(PyamentTermsNotes; Notes)
            {
                ApplicationArea = Notes;
            }
        }
        //g2s29Dev23
    }

    actions
    {
        area(navigation)
        {
            group("&Voucher")
            {
                Caption = '&Voucher';
                action("&Show Document")
                {
                    ApplicationArea = all;
                    Caption = '&Show Document';
                    ShortCutKey = 'Shift+F5';

                    trigger OnAction();
                    begin
                        VoucherRec.RESET;
                        VoucherRec.SETRANGE("Document No.", "Document No.");
                        PAGE.RUNMODAL(PAGE::"Approved Bank Receipt Vouchers", VoucherRec);
                    end;
                }
            }
        }
    }

    var
        VoucherRec: Record "Voucher Header";
        BuildFilter: Text[250];
        RespCentFilter: Codeunit "Responsibility Center Filter";
}

