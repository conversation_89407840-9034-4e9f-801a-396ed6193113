enum 50344 CategorySub
{
    Extensible = true;

    value(1; " ")
    {
        Caption = ' ';
    }
    value(2; "1 Liter")
    {
        Caption = '1 Liter';
    }
    value(3; "Cans(Active Zest & HappyHour)")
    {
        caption = 'All Cans';
    }
    value(4; Caprisun)
    {
        caption = 'Caprisun';
    }
    value(5; "90ml")
    {
        Caption = '90ml';
    }
    value(6; "120g")
    {
        Caption = '120g';
    }
    //G2s Added>>>>>>>>>>>>>>>>>>>>>>>>>>010224
    value(7; "190g")
    {
        Caption = '190g';
    }
    value(8; "UHT")
    {
        Caption = 'UHT';
    }
    //G2s Added>>>>>>>>>>>>>>>>>>>>>>>>>>010224
    value(9; "315ml")
    {
        Caption = '315ml';
    }
    value(10; "Pet drink")
    {
        Caption = 'Pet drink';
    }

}
enum 50345 "Exclusive JNSD DistributorsSub"
{
    Extensible = true;

    value(1; " ")
    {
        Caption = ' ';
    }
    value(2; "1 Liter")
    {
        Caption = '1 Liter';
    }
    value(3; "Cans(Active Zest & HappyHour)")
    {
        caption = 'All Cans';
    }
    value(4; Caprisum)
    {
        caption = 'Caprisum';
    }
    value(5; "90ml")
    {
        Caption = '90ml';
    }
    //G2s Added>>>>>>>>>>>>>>>>>>>>>>>>>>010224
    value(6; "190g")
    {
        Caption = '190g';
    }
    //G2s Added>>>>>>>>>>>>>>>>>>>>>>>>>>010224
}
enum 50346 "Exclusive EVAP DistributorsSub"
{
    Extensible = true;

    value(1; " ")
    {
        Caption = ' ';
    }
    value(2; "120g Full Cream")
    {
        Caption = '120g Full Cream';
    }
    value(3; "120g Slim")
    {
        Caption = '120g Slim';
    }
    value(4; "50g Full Cream")
    {
        Caption = '50g Full Cream';
    }
    value(5; "50g Slim")
    {
        Caption = '50g Slime';
    }
    //G2s Added>>>>>>>>>>>>>>>>>>>>>>>>>>010224
    value(6; "190g Full Cream")
    {
        Caption = '190g Full Cream';
    }
    value(7; "190g Slim")
    {
        Caption = '190g Slim';
    }
    value(8; "UHT Milk")
    {
        Caption = 'UHT Milk';
    }
    //G2s Added>>>>>>>>>>>>>>>>>>>>>>>>>>010224
}