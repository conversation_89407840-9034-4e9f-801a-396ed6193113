page 50288 "Cash Payment Voucher New"
{
    // version CHI6.0

    // PROJECT : CHI 6.0
    // **********************************************************************************
    // SIGN
    // **********************************************************************************
    // CHI     :  CHI 6.0 Developments
    // UNL     :  Univision Nigeria Limited
    // SAA     :  SAHEED ADIO ADEOSUN
    // HO      :  Henry <PERSON>
    // **********************************************************************************
    // VER      SIGN         DATE   DESCRIPTION
    // **********************************************************************************
    // 1.0      UNL      06-Dec-11  -> Form Created to display Cash Payment Document Details.
    // 
    // 3.0      SAA      26-Dec-12  -> Added new Function "CheckHeaderLines" to the table.
    //                              -> Code added OnPush for Menu Item "Send Approval Request" to run function
    //                                 "CheckHeaderLines".
    //                   08-Mar-12  -> Added codes to "Form-OnOpenForm" to filter out Responsibility Centres in Vouchers.
    //                              -> Added the "Responsibility Centre" field to the form
    // 
    // 1.0      HO       07-Sep-12  -> Code added to "Form-OnDelereRecord()" to allow Archive of deleted Cash Payment Voucher Document No.

    DeleteAllowed = true;
    PageType = Document;
    SourceTable = "Voucher Header";
    SourceTableView = SORTING("Voucher Type", "Document No.")
                      WHERE("Voucher Type" = CONST(CPV),
                            Status = FILTER(<> Released));

    layout
    {
        area(content)
        {
            group(General)
            {
                Caption = 'General';
                field("Document No."; "Document No.")
                {
                    ApplicationArea = all;
                    Editable = false;

                    trigger OnAssistEdit();
                    begin
                        if AssistEdit(xRec) then
                            CurrPage.UPDATE;
                    end;
                }
                field("Posting Date"; "Posting Date")
                {
                    ApplicationArea = all;
                    trigger OnValidate();
                    begin
                        // SAA 3.0 >>
                        PostingDateValidate;
                        // SAA 3.0 <<
                    end;
                }
                field("Transaction Type"; "Transaction Type")
                {
                    ApplicationArea = all;

                    trigger OnValidate();
                    begin
                        if "Transaction Type" <> "Transaction Type"::Import then
                            "Import File No." := '';
                        if "Transaction Type" <> "Transaction Type"::" " then
                            TESTFIELD("Account No.", '');
                    end;
                }
                field("WHT Applicable"; "WHT Applicable")
                {
                    ApplicationArea = all;
                    trigger OnValidate()
                    var
                        genjoulrec: Record "Gen. Journal Line";
                    begin
                        IF "WHT Applicable" then begin
                            genjoulrec.Reset();
                            genjoulrec.Setrange("Journal Template Name", 'CPV');
                            genjoulrec.Setrange("Journal Batch Name", 'CPV');
                            genjoulrec.Setrange("Voucher Type", genjoulrec."Voucher Type"::CPV);
                            genjoulrec.Setrange("Document No.", "Document No.");
                            genjoulrec.Setrange("Document Type", genjoulrec."Document Type"::Payment);
                            IF genjoulrec.FindFirst() then
                                Error('You cannot insert more than one line if WHT is Applicable')
                        end Else Begin
                            genjoulrec.Reset();
                            genjoulrec.Setrange("Journal Template Name", 'CPV');
                            genjoulrec.Setrange("Journal Batch Name", 'CPV');
                            genjoulrec.Setrange("Voucher Type", genjoulrec."Voucher Type"::CPV);
                            genjoulrec.Setrange("Document No.", "Document No.");
                            genjoulrec.Setrange("Document Type", genjoulrec."Document Type"::Payment);
                            IF genjoulrec.FindFirst() then
                                repeat
                                    Clear(genjoulrec."WHT %");
                                    Clear(genjoulrec."WHT Account");
                                    Clear(genjoulrec."WHT Amount");
                                    Clear(genjoulrec."WHT Amount(LCY)");
                                    Clear(genjoulrec."WHT Group");
                                    genjoulrec.Modify();
                                until genjoulrec.Next = 0;
                        End;
                    end;

                }
                field("Import File No."; "Import File No.")
                {
                    ApplicationArea = all;
                    trigger OnValidate();
                    begin
                        if "Import File No." <> '' then
                            TESTFIELD("Transaction Type", "Transaction Type"::Import);
                    end;
                }
                field("Clearing File No."; "Clearing File No.")
                {
                    ApplicationArea = all;
                }
                field("Account No."; "Account No.")
                {
                    ApplicationArea = all;
                    Caption = 'Credit Account No.';

                    trigger OnValidate();
                    begin
                        TestTransactionType;
                    end;
                }
                field("Account Name"; "Account Name")
                {
                    ApplicationArea = all;
                }
                field(Status; Status)
                {
                    ApplicationArea = all;
                }
                field(Narration; Narration)
                {
                    ApplicationArea = all;
                }
                field("Amount (LCY)"; "Amount (LCY)")
                {
                    ApplicationArea = all;
                }
                field("Shortcut Dimension 1 Code"; "Shortcut Dimension 1 Code")
                {
                    ApplicationArea = all;
                }
                field("Shortcut Dimension 2 Code"; "Shortcut Dimension 2 Code")
                {
                    ApplicationArea = all;
                }
                field("Cash Requisition Slip No."; "Cash Requisition Slip No.")
                {
                    ApplicationArea = all;
                }
                field("Cash Account Type"; "Cash Account Type")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Currency Code"; "Currency Code")
                {
                    ApplicationArea = all;
                    Editable = false;
                    Visible = false;

                    trigger OnAssistEdit();
                    begin
                        ChangeExchangeRate.SetParameter("Currency Code", "Currency Factor", "Posting Date");
                        if ChangeExchangeRate.RUNMODAL = ACTION::OK then begin
                            VALIDATE("Currency Factor", ChangeExchangeRate.GetParameter);
                        end;
                        CLEAR(ChangeExchangeRate);
                    end;
                }
            }
            part(VoucherLines; "Cash Payment Voucher Subform")
            {
                ApplicationArea = all;
                SubPageLink = "Journal Template Name" = FIELD("Journal Template Code"),
                              "Journal Batch Name" = FIELD("Journal Batch Name"),
                              "Document No." = FIELD("Document No.");
            }
            group(Usertrail)
            {
                Caption = 'Usertrail';
                field("Created By"; "Created By")
                {
                    ApplicationArea = all;
                }
                field("Created By Name"; "Created By Name")
                {
                    ApplicationArea = all;
                }
                field("Created Date"; "Created Date")
                {
                    ApplicationArea = all;
                }
                field("Created Time"; "Created Time")
                {
                    ApplicationArea = all;
                }
                field("Modified By"; "Modified By")
                {
                    ApplicationArea = all;
                }
                field("Modified By Name"; "Modified By Name")
                {
                    ApplicationArea = all;
                }
                field("Modified Date"; "Modified Date")
                {
                    ApplicationArea = all;
                }
                field("Modified Time"; "Modified Time")
                {
                    ApplicationArea = all;
                }
            }
            group("Payment Details")
            {
                Caption = 'Payment Details';
                field(ToBeCollectedBy; ToBeCollectedBy)
                {
                    ApplicationArea = all;
                }
                field(PaymentSettlementOf; PaymentSettlementOf)
                {
                    ApplicationArea = all;
                }
                field("Payable To"; "Payable To")
                {
                    ApplicationArea = all;
                }
                field("Payable Code"; "Payable Code")
                {
                    ApplicationArea = all;
                }
                field("Payable Name"; "Payable Name")
                {
                    ApplicationArea = all;
                }
                field("Cash Payments Options"; "Cash Payments Options")
                {
                    ApplicationArea = all;
                }
                field("Cash Paid"; "Cash Paid")
                {
                    ApplicationArea = all;

                    trigger OnValidate();
                    begin
                        if Status = Status::Open then
                            ERROR(Text50200);

                        //GLsetup.GET;

                        if not "Cash Paid" then
                            ERROR(Text50201);

                        //IF (TODAY - "Posting Date") <= GLsetup."Voucher Half Life" THEN
                        //BEGIN
                        if "Cash Paid" then
                            //PermCodeUnit.CheckAutority(USERID,609);
                            if CONFIRM('Do You Want to Print this CPV Slip ?\', true) then begin
                                "Cash Paid By" := USERID;
                                "Cash Paid On" := CURRENTDATETIME;
                                //GenJounalBatchRec.GET("Journal Template Name","Journal Batch Name");
                                //GenJounalBatchRec."Cash Paid":= CashPaid;
                                //GenJounalBatchRec."Cash Paid By":= CashPaidby;
                                //GenJounalBatchRec."Date Paid":=DatePaid;
                                //GenJounalBatchRec."Amount Paid":=TotalAmount;
                                //GenJounalBatchRec.MODIFY;
                                //GenJounalBatchRecCPV.SETRANGE(GenJounalBatchRecCPV."Journal Template Name","Journal Template Name");
                                //GenJounalBatchRecCPV.SETRANGE(GenJounalBatchRecCPV.Name,"Journal Batch Name");
                                //IF GenJounalBatchRecCPV.FINDFIRST THEN
                                //REPORT.RUN(50465,FALSE,FALSE,GenJounalBatchRecCPV);
                            end else begin
                                "Cash Paid" := false;
                                "Cash Paid By" := '';
                                //"Cash Paid On":=0d:0t;
                            end

                        //END ELSE
                        //BEGIN
                        //GenJounalBatchRec.GET("Journal Template Name","Journal Batch Name");
                        //MESSAGE('This Voucher is long overdue and cannot be paid, Cick OK to delete this Voucher, Thanks');
                        //GenJounalBatchRec.GET("Journal Template Name","Journal Batch Name");
                        //GenJnlLine.SETFILTER(GenJnlLine."Journal Template Name","Journal Template Name");
                        //GenJnlLine.SETFILTER(GenJnlLine."Journal Batch Name","Journal Batch Name");
                        //IF  GenJnlLine.FIND('-')THEN
                        //REPEAT
                        //GenJnlLine.DELETE;
                        //UNTIL GenJnlLine.NEXT = 0;
                        //GenJounalBatchRec.DELETE;
                        //GenJounalBatchRec.NEXT(1);
                        //GenJnlLine.NEXT(1);
                        //END;
                    end;
                }
                field("Cash Paid By"; "Cash Paid By")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Cash Paid On"; "Cash Paid On")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("ABS(""Amount (LCY)"")"; ABS("Amount (LCY)"))
                {
                    ApplicationArea = all;
                    Caption = 'Amount Paid';
                }
                field("Reprint Approved By"; "Reprint Approved By")
                {
                    ApplicationArea = all;
                    Visible = false;
                }
                field("Reprint Approval"; "Reprint Approval")
                {
                    ApplicationArea = all;
                    Visible = false;
                }
                field("Reprint CPV Slip"; "Reprint CPV Slip")
                {
                    ApplicationArea = all;
                    Visible = false;

                    trigger OnValidate();
                    begin
                        //PermCodeUnit.CheckAutority(USERID,609);
                        if "Reprint CPV Slip" then begin
                            if "Cash Paid" then begin
                                if CONFIRM(Text50202, true) then begin
                                    "Reprinted By" := USERID;
                                    "Reprinted On" := CURRENTDATETIME;
                                    //GenJounalBatchRec.GET("Journal Template Name","Journal Batch Name");
                                    //GenJounalBatchRec."Reprinted By":= ReprintedBy;
                                    //GenJounalBatchRec."Reprinted Date":= ReprintedDate;
                                    //GenJounalBatchRec.MODIFY;
                                    //GenJounalBatchRecCPV.SETRANGE(GenJounalBatchRecCPV."Journal Template Name","Journal Template Name");
                                    //GenJounalBatchRecCPV.SETRANGE(GenJounalBatchRecCPV.Name,"Journal Batch Name");
                                    //IF GenJounalBatchRecCPV.FINDFIRST THEN
                                    //REPORT.RUN(50465,FALSE,FALSE,GenJounalBatchRecCPV);
                                end else begin
                                    "Reprint CPV Slip" := false;
                                end;
                            end else
                                ERROR(Text50205);
                        end else
                            "Reprint CPV Slip" := false;
                    end;
                }
                field("Reprinted By"; "Reprinted By")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Reprinted On"; "Reprinted On")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
            }
        }
        area(FactBoxes)
        {
            part("Attached Documents"; "Document Attachment Factbox")
            {
                ApplicationArea = All;
                Caption = 'Attachments';
                SubPageLink = "Table ID" = CONST(50117),
                                "No." = FIELD("Document No.");
                // Type = FIELD("Voucher Type");
            }
            systempart(PyamentTermsLinks; Links)
            {
                ApplicationArea = RecordLinks;
            }
            systempart(PyamentTermsNotes; Notes)
            {
                ApplicationArea = Notes;
            }
        }
        //g2s29Dev23
    }

    actions
    {
        area(navigation)
        {
            group("&Voucher")
            {
                Caption = '&Voucher';
                separator(Separator1000000067)
                {
                }
                action(Dimensions)
                {
                    ApplicationArea = all;
                    Caption = 'Dimensions';

                    trigger OnAction();
                    begin
                        ShowDocDim;
                    end;
                }
                separator(Separator1000000069)
                {
                }
                action(Approvals)
                {
                    ApplicationArea = all;
                    Caption = 'Approvals';

                    trigger OnAction();
                    begin
                        approvalmngmt.ApproveRecordApprovalRequest(RecordId());
                    end;
                }
                /*action(InsertLine)
                {
                    ApplicationArea = all;
                    Caption = 'Insert Line';
                    Promoted = true;
                    PromotedCategory = Process;
                    ShortcutKey = 'Ctrl+N';
                    trigger OnAction();
                    var
                        genjoulrec: Record "Gen. Journal Line";
                        linen: Integer;
                    begin
                        genjoulrec.RESET();
                        genjoulrec.SetRange("Journal Template Name", 'CPV');
                        genjoulrec.SetRange("Journal Batch Name", 'CPV');

                        //genjoulrec.SetRange("Document No.", "Document No.");
                        IF genjoulrec.FINDLAST THEN
                            linen := 10000 + genjoulrec."Line No."
                        else
                            linen := 10000;
                        IF "WHT Applicable" then begin
                            genjoulrec.Reset();
                            genjoulrec.Setrange("Journal Template Name", 'CPV');
                            genjoulrec.Setrange("Journal Batch Name", 'CPV');
                            genjoulrec.Setrange("Voucher Type", genjoulrec."Voucher Type"::CPV);
                            genjoulrec.Setrange("Document No.", "Document No.");
                            genjoulrec.Setrange("Document Type", genjoulrec."Document Type"::Payment);
                            IF genjoulrec.FindFirst() then
                                Error('You cannot insert more than one line if WHT is Applicable')
                        end;
                        clear(genjoulrec);
                        genjoulrec.init();
                        genjoulrec."Journal Template Name" := 'CPV';
                        genjoulrec."Journal Batch Name" := 'CPV';
                        genjoulrec."Voucher Type" := genjoulrec."Voucher Type"::CPV;
                        genjoulrec."Document No." := "Document No.";
                        genjoulrec."Document Type" := genjoulrec."Document Type"::Payment; //PJ                        
                        genjoulrec."Line No." := linen;
                        genjoulrec."Posting Date" := Today; //PJ                        
                        genjoulrec.insert(True);
                        Message('Line Inserted %1..%2', genjoulrec."Journal Template Name", genjoulrec."Journal Batch Name");
                        //insert(true);
                        CurrPage.Update();
                    end;
                }*/
            }
            group("F&unctions")
            {
                Caption = 'F&unctions';
                action("Send Approval Request")
                {
                    ApplicationArea = All;
                    Image = SendApprovalRequest;
                    Visible = Not OpenApprEntrEsists and CanrequestApprovForFlow;
                    Promoted = true;
                    PromotedIsBig = true;
                    PromotedCategory = Process;
                    PromotedOnly = true;
                    trigger OnAction()
                    var
                        GenLineLRec: Record "Gen. Journal Line";
                    begin
                        GenLineLRec.reset;
                        GenLineLRec.SetRange("Journal Template Name", "Journal Template Code");
                        GenLineLRec.SetRange("Journal Batch Name", "Journal Batch Name");
                        GenLineLRec.SetRange("Document No.", "Document No.");
                        GenLineLRec.SetRange(Quantity, 0);
                        IF GenLineLRec.findset then
                            repeat
                                error('quantity is zero in Gen Journal for Line No. %1', GenLineLRec."Line No.");
                            until GenLineLRec.next = 0;
                        TestField("Posting Date");
                        ClearValues();
                        CheckHeaderLines(Rec);
                        IF allinoneCU.CheckJournalVoucherApprovalsWorkflowEnabled(Rec) then
                            allinoneCU.OnSendJournalVoucherForApproval(Rec);
                    end;
                }
                action("Cancel Approval Request")
                {
                    ApplicationArea = All;
                    Image = CancelApprovalRequest;
                    Visible = CanCancelapprovalforrecord or CanCancelapprovalforflow;
                    Promoted = true;
                    PromotedIsBig = true;
                    PromotedCategory = Process;
                    PromotedOnly = true;
                    trigger OnAction()
                    begin
                        allinoneCU.OnCancelJournalVoucherForApproval(Rec);
                    end;
                }
                separator(Separator1000000049)
                {
                }
                action("Re&lease")
                {
                    ApplicationArea = all;
                    Caption = 'Re&lease';
                    ShortCutKey = 'Ctrl+F11';
                    Image = ReleaseDoc;
                    trigger OnAction()
                    begin
                        CheckHeaderLines(Rec);
                        TestField("Posting Date");
                        IF WorkflowManagement.CanExecuteWorkflow(Rec, allinoneCU.RunworkflowOnSendJournalVoucherforApprovalCode()) then
                            error('Workflow is enabled. You can not release manually.');

                        IF Status <> Status::Released then BEGIN
                            Status := Status::Released;
                            Modify();
                            Message('Document has been Released.');
                        end;
                    end;
                }
                action("Re&open")
                {
                    ApplicationArea = all;
                    Caption = 'Re&open';
                    Image = ReOpen;
                    trigger OnAction();
                    begin
                        RecordRest.Reset();
                        RecordRest.SetRange(ID, 50117);
                        RecordRest.SetRange("Record ID", Rec.RecordId());
                        IF RecordRest.FindFirst() THEN
                            error('This record is under in workflow process. Please cancel approval request if not required.');
                        IF Status <> Status::Open then BEGIN
                            Status := Status::Open;
                            Modify();
                            Message('Document has been Reopened.');
                        end;
                    end;
                }
                separator("---")
                {
                    Caption = '---';
                }
                action(Comments)
                {
                    ApplicationArea = all;
                    Caption = 'Comments';
                    RunObject = Page "Approval Comments";
                    RunPageLink = "Document Type" = FILTER('CPV'),
                                  "Document No." = FIELD("Document No.");
                }
                separator("-")
                {
                    Caption = '-';
                }
                action("Re-Print Cash Voucher")
                {
                    ApplicationArea = all;
                    Caption = 'Re-Print Cash Voucher';

                    trigger OnAction();
                    begin
                        VoucherHeader.RESET;
                        VoucherHeader.SETRANGE("Voucher Type", "Voucher Type");
                        VoucherHeader.SETRANGE("Document No.", "Document No.");
                        if VoucherHeader.FINDFIRST then
                            //REPORT.RUN(50440, true, false, VoucherHeader);
                            Report.Run(50495, true, false, VoucherHeader);
                    end;
                }
            }
            group("P&osting")
            {
                Caption = 'P&osting';
                action("P&ost")
                {
                    ApplicationArea = all;
                    Caption = 'P&ost';
                    ShortCutKey = 'F11';
                    Visible = false;

                    trigger OnAction();
                    begin
                        if not "Cash Paid" then
                            ERROR(Text50203);

                        VoucherPost.RUN(Rec);
                    end;
                }
                action(Preview)
                {
                    ApplicationArea = all;
                    Caption = 'Preview';
                    ShortCutKey = 'Shift+F2';

                    trigger OnAction();
                    var
                        GeneralJrnlBtchLRec: Record "Gen. Journal Batch";
                        DocumentNo: Code[20];
                        GLRegGRec: Record "G/L Register";
                        genJounlin: Record "Gen. Journal Line 2";
                        VoucherPreview: Codeunit "Voucher Preview Posting";
                    begin
                        DocumentNo := "Document No.";
                        ClearValues();
                        VoucherPreview.RUN(Rec);
                    end;
                }
                action("Post and &Print")
                {
                    ApplicationArea = all;
                    Caption = 'Post and &Print';
                    ShortCutKey = 'Shift+F11';

                    trigger OnAction();
                    begin
                        //PermCodeUnit.CheckAutority(USERID,157);
                        if not "Cash Paid" then
                            ERROR(Text50203);
                        VoucherPost.RUN(Rec);

                    end;
                }
                action("Cash Payment Voucher Test Report")
                {
                    trigger OnAction()
                    var
                        VouHeader: Record "Voucher Header";
                    BEGIN
                        VouHeader.RESET;
                        VouHeader.SETRANGE("Voucher Type", "Voucher Type");
                        VouHeader.SETRANGE("Document No.", "Document No.");
                        if VouHeader.FINDFIRST then
                            REPORT.RUN(50563, true, false, VouHeader);
                    END;
                }
                //Balu 05282021>>
                action("Open Excel")
                {
                    ApplicationArea = all;
                    Caption = 'Open Excel';
                    Image = Open;
                    trigger OnAction()
                    var
                        GlLine2: Record "Gen. Journal Line 2";
                    begin
                        GlLine2.CreateExcel(Rec);
                    end;
                }
                //Balu 05282021<<
                /*action(Preview)
                {
                    APplicationArea = All;
                    Caption = 'Preview';
                    trigger onaction()
                    var
                        GenJnlPost: Codeunit "Gen. Jnl.-Post2";
                        GenJoulne: Record "Gen. Journal Line"; 
                    BEGIN
                        GenJoulne.Reset();
                        GenJoulne.SetRange("Journal Template Name", "Journal Template Code");
                        GenJoulne.SetRange("Journal Batch Name", "Journal Batch Name");
                        GenJoulne.SetRange("Document No.", "Document No.");
                        IF GenJoulne.findset then;
                        GenJnlPost.Preview(GenJoulne);
                    END;
                }*/

            }
        }
        area(processing)
        {
            action("&Print")
            {
                ApplicationArea = all;
                Caption = '&Print';
                Ellipsis = true;
                Promoted = true;
                PromotedCategory = Process;

                trigger OnAction();
                begin
                    VoucherHeader.SETRANGE("Voucher Type", "Voucher Type");
                    VoucherHeader.SETRANGE("Document No.", "Document No.");
                    if VoucherHeader.FINDFIRST then
                        REPORT.RUN(50080, true, false, VoucherHeader);
                end;
            }
        }
    }

    trigger OnDeleteRecord(): Boolean;
    begin
        DelDocNoArchive.ArchiveNo("Document No.", 18, TODAY, TIME, USERID, DATABASE::"Voucher Header"); //HO1.0
        // B2BMS08022021>>
        TestField(Status, Status::Open);
        // B2BMS08022021<<
    end;

    trigger OnNewRecord(BelowxRec: Boolean);
    begin
        "Dim. Document Type" := "Dim. Document Type"::CPV;
        // SAA 3.0 >>
        "Responsibility Center" := UserMg.GetVoucherFilter();
        // SAA 3.0 <<
    end;

    trigger OnOpenPage();
    begin
        // SAA 3.0 >>
        //CurrForm.Cashier.VISIBLE(FALSE);

        if UserMg.GetVoucherFilter() <> '' then begin
            FILTERGROUP(2);
            SETRANGE("Responsibility Center", UserMg.GetVoucherFilter());
            FILTERGROUP(0);
        end;
        // SAA 3.0 <<
    end;

    trigger OnModifyRecord(): Boolean
    BEGIN
        TestField(Status, Status::Open);
    END;

    trigger OnAfterGetRecord()
    BEGIN
        OpenAppEntrExistsForCurrUser := approvalmngmt.HasOpenApprovalEntriesForCurrentUser(RecordId());
        OpenApprEntrEsists := approvalmngmt.HasOpenApprovalEntries(RecordId());
        CanCancelapprovalforrecord := approvalmngmt.CanCancelApprovalForRecord(RecordId());
        workflowwebhookmangt.GetCanRequestAndCanCancel(RecordId(), CanrequestApprovForFlow, CanCancelapprovalforflow);
    END;

    var
        VoucherHeader: Record "Voucher Header";
        ApprovalMgt: Codeunit 1535;
        ApprovalEntry: Record "Approval Entry";
        ReleaseVoucher: Codeunit "Voucher Release";
        ApprovalEntries: Page 658;
        ChangeExchangeRate: Page "Change Exchange Rate";
        GenJrnlLine: Record "Gen. Journal Line";
        ReportPrint: Codeunit "Test Report-Print";
        VoucherPost: Codeunit "Voucher Post";
        Vendor: Record Vendor;
        Customer: Record Customer;
        Employee: Record Employee;
        Bank: Record "Bank Account";
        Text50200: Label 'Status must be Released to pay Cash.';
        Text50201: Label 'You have already Printed the CPV Slip, you cannot Untick';
        Text50202: Label 'Do You Want to RePrint this CPV Slip ?\';
        Text50203: Label 'Cash Paid is False, hence you cannot Post.';
        Text50204: Label 'To be collected by must not be Blank';
        Text50205: Label 'You cannot Reprint while Cash Paid is False.';
        Text50206: Label 'Cash payment options must not be blank';
        Text50207: Label 'You cannot ReOpen Document while Cash Paid is True.';
        UserMgt: Codeunit "User Setup Management";
        UserMg: Codeunit "User Setup Management Ext";
        DelDocNoArchive: Codeunit "Deleted Doc. No. Archive";
        Text50000: Label 'Do wish to the Cancel the Approval Request of this Document?';
        text50001: Label 'Cash Account type must not be blank, please re validate the Account No. in the Header';
        MaintenanceRec: Record Maintenance;
        WorkflowManagement: Codeunit "Workflow Management";
        allinoneCU: codeunit IJLSubEvents;
        RecordRest: record "Restricted Record";
        approvalmngmt: Codeunit "Approvals Mgmt.";
        OpenApprEntrEsists: Boolean;
        CanrequestApprovForFlow: Boolean;
        CanCancelapprovalforrecord: Boolean;
        CanCancelapprovalforflow: Boolean;
        OpenAppEntrExistsForCurrUser: Boolean;
        workflowwebhookmangt: Codeunit "Workflow Webhook Management";

    procedure CheckHeaderLines(VoucherHeaderRec: Record "Voucher Header");
    var
        GenJnlLine: Record "Gen. Journal Line 2";//PK-GJ2
        VendorLedgerEntry: Record "Vendor Ledger Entry";
        GlAccLvar: Record "G/L Account";
    begin
        with VoucherHeaderRec do begin
            TESTFIELD(Narration);
            if "Account Type" <> "Account Type"::"Bank Account" then
                TESTFIELD("Responsibility Center");

            if ToBeCollectedBy = '' then
                ERROR(Text50204);

            if "Cash Payments Options" = 0 then
                ERROR(Text50206);

            if "Cash Account Type" = 0 then //SAA3.0
                ERROR(text50001);             //SAA3.0

            TESTFIELD("Account No.");
            TESTFIELD("Shortcut Dimension 2 Code");
            TESTFIELD("Shortcut Dimension 1 Code");
            //TESTFIELD(Narration);
            //TESTFIELD(PaymentSettlementOf);

            if "Payable To" <> "Payable To"::Others then begin
                TESTFIELD("Payable Code");
                TESTFIELD("Payable To");
            end;

            TESTFIELD("Posting Date");
            //TESTFIELD("Cash Requisition Slip No.");

            GenJnlLine.SETRANGE(GenJnlLine."Journal Template Name", "Journal Template Code");
            GenJnlLine.SETRANGE(GenJnlLine."Journal Batch Name", "Journal Batch Name");
            GenJnlLine.SETRANGE(GenJnlLine."Document No.", "Document No.");
            if GenJnlLine.FIND('-') then begin
                repeat
                    GenJnlLine.TESTFIELD("Account No.");
                    GenJnlLine.TESTFIELD("Posting Date");
                    GenJnlLine.TESTFIELD(Amount);
                    GenJnlLine.TESTFIELD("Shortcut Dimension 1 Code");
                    GenJnlLine.TESTFIELD("Shortcut Dimension 2 Code");
                    GenJnlLine.TESTFIELD("Description 2");
                    //PKONDE30>>
                    IF GenJnlLine."Account Type" = GenJnlLine."Account Type"::"G/L Account" THEN
                        IF GlAccLvar.GET(GenJnlLine."Account No.") then
                            IF ((GlAccLvar."Gen. Bus. Posting Group" <> '') or (GlAccLvar."Gen. Prod. Posting Group" <> '')) then
                                If GlAccLvar."Gen. Posting Type" = GlAccLvar."Gen. Posting Type"::" " then
                                    Error('Gen. Posting type must not be empty in gl Account %1', GenJnlLine."Account No.");
                    //PKONDE30<<
                    /*if GenJnlLine."Account Type" = GenJnlLine."Account Type"::Vendor then begin
                        Vendor.GET(GenJnlLine."Account No.");
                        GenJnlLine.TESTFIELD("External Document No.");
                        //added to mandate currency code for Import Vendors.    4/8/18  >>
                        Vendor.GET(GenJnlLine."Account No.");
                        if Vendor."Vendor Type" = Vendor."Vendor Type"::Import then
                            if Vendor."Currency Code" <> '' then begin
                                if GenJnlLine."Currency Code" <> Vendor."Currency Code" then
                                    ERROR('Currency Code must be %1 on line %2 for this vendor %3', Vendor."Currency Code", GenJnlLine."Line No.",
                                      Vendor.Name);
                            end else
                                ERROR('Currency Code must not be blank for this vendor %1', Vendor.Name); //END;  //<<
                                                                                                          // GenJnlLine.TESTFIELD("Currency Code");

                        if (Vendor."Service Group" = Vendor."Service Group"::Supplier) or
                          (Vendor."Service Group" = Vendor."Service Group"::Contractor) then begin
                            GenJnlLine.TESTFIELD("Vendor Payment Type");
                            if GenJnlLine."Vendor Payment Type" = GenJnlLine."Vendor Payment Type"::Advance then
                                GenJnlLine.TESTFIELD("LPO No.");
                            //GenJnlLine.TESTFIELD("Applies-to Doc. Type");
                            //GenJnlLine.TESTFIELD("Applies-to Doc. No.");

                            if GenJnlLine."Applies-to Doc. Type" = 0 then
                                ERROR('Applies-to Doc. Type must not be blank for this %1 Vendor', Vendor."Service Group");

                            if ((GenJnlLine."Applies-to Doc. No." = '') and (GenJnlLine."Applies-to ID" = '')) then
                                ERROR('Applies-to Doc. No. or Applies-to ID must not be blank for this %1 Vendor', Vendor."Service Group");

                        end;
                        VendorLedgerEntry.SETCURRENTKEY("Global Dimension 1 Code", "Vendor No.", "Vendor Posting Group");
                        VendorLedgerEntry.SETRANGE("Global Dimension 1 Code", GenJnlLine."Shortcut Dimension 1 Code");
                        VendorLedgerEntry.SETRANGE("Vendor No.", GenJnlLine."Account No.");
                        VendorLedgerEntry.SETRANGE("Vendor Posting Group", GenJnlLine."Posting Group");
                        VendorLedgerEntry.SETRANGE(VendorLedgerEntry.Open, true);
                        if VendorLedgerEntry.FIND('-') then
                            if not CONFIRM('YOU HAVE ADVANCES NOT ADJUSTED AGAINST THIS VENDOR. \DO YOU WANT TO PASS THE PAYMENT?', true) then
                                REPORT.RUN("Unadjusted Vendor Debit Bal."50204, true, false, VendorLedgerEntry)
                            else
                                REPORT.RUN(50204, true, false, VendorLedgerEntry);
                    end;

                    GenJnlLine."Posting Date" := "Posting Date";
                    */

                    if GenJnlLine."Account Type" = GenJnlLine."Account Type"::"Fixed Asset" then
                        GenJnlLine.TESTFIELD("FA Posting Type");
                    if GenJnlLine."FA Posting Type" = GenJnlLine."FA Posting Type"::Maintenance then begin
                        GenJnlLine.TESTFIELD("Maintenance Code");
                        if MaintenanceRec.GET(GenJnlLine."Maintenance Code") then
                            if MaintenanceRec."PMS Maintenance" then begin
                                GenJnlLine.TESTFIELD("Current Km Reading");
                                GenJnlLine.TESTFIELD("Fuel Availed");
                                GenJnlLine.TESTFIELD("Date PMS Availed");

                            end;
                    end;

                    if GenJnlLine."IC Partner Code" <> '' then
                        GenJnlLine.TESTFIELD("IC Partner G/L Acc. No.");
                    /*
                    if GenJnlLine."Staff Dimension" <> '' then
                        GenJnlLine.TESTFIELD("Shortcut Dimension 13 Code");
                        */

                    //Nyo
                    if GenJrnlLine."Account Type" = GenJrnlLine."Account Type"::"Fixed Asset" then
                        GenJnlLine.TESTFIELD("FA Posting Type");
                    if (GenJnlLine."Account Type" = GenJnlLine."Account Type"::Customer)
                      or (GenJnlLine."Bal. Account Type" = GenJnlLine."Bal. Account Type"::Customer) then
                        GenJnlLine.TESTFIELD("Responsibility Center");
                    /*if (GenJnlLine."FA Posting Type" = GenJnlLine."FA Posting Type" ::"capital work in progress") or
                       (GenJnlLine."FA Posting Type" = GenJnlLine."FA Posting Type" ::"Acquisition Cost") then begin
                      //TESTFIELD("Shortcut Dimension 4 Code");
                      GenJnlLine.TESTFIELD("Capex No.");
                      GenJnlLine.TESTFIELD("Capex Line No.");
                    end;*///CHI 9.0

                    //Nyo

                    GenJnlLine.MODIFY;
                until GenJnlLine.NEXT = 0;
            end;
        end;

    end;

    procedure PostingDateValidate();
    var
        Text50201: Label 'Posting Date must not be a future date';
    begin
        if "Posting Date" > TODAY then
            ERROR(Text50201);
    end;

    procedure TestTransactionType();
    begin
        if "Account No." <> '' then begin
            TESTFIELD("Transaction Type");
            if "Transaction Type" = "Transaction Type"::Import then begin
                TESTFIELD("Import File No.");
                //TESTFIELD("Clearing File No.");
            end;
        end;
    end;
}

