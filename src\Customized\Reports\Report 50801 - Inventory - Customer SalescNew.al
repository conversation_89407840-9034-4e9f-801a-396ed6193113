report 50801 "Inventory Customer Sales New1"
{
    // version NAVW16.00.01

    //DefaultLayout = RDLC;
    //RDLCLayout = './Inventory - Customer Sales New.rdl';
    //RDLCLayout = './713N.rdl';
    //RDLCLayout = 'CHIReports\Reports\Layout\Inventory - Customer Sales New.rdl';//PKONJU2
    Caption = 'Inventory - Customer Sales_50801';
    UsageCategory = ReportsAndAnalysis;
    ProcessingOnly = true;


    dataset
    {
        dataitem(Item; Item)
        {
            DataItemTableView = SORTING("Inventory Posting Group") where("Item Category Code" = filter('FG|SCRAP'));
            PrintOnlyIfDetail = true;
            RequestFilterFields = "No.", "No. 2", "Search Description", "Inventory Posting Group", "Date Filter", "Global Dimension 1 Filter", "Global Dimension 2 Filter", "Location Filter";
            column(FORMAT_TODAY_0_4_; FORMAT(TODAY, 0, 4))
            {
            }
            column(STRSUBSTNO_Text000_PeriodText_; STRSUBSTNO(Text000, PeriodText))
            {
            }
            column(CurrReport_PAGENO; CurrReport.PAGENO)
            {
            }
            column(COMPANYNAME; COMPANYNAME)
            {
            }
            column(USERID; USERID)
            {
            }
            column(PriceGrp; PriceGrp)
            {
            }
            column(STRSUBSTNO___1___2__Item_TABLECAPTION_ItemFilter_; STRSUBSTNO('%1: %2', Item.TABLECAPTION, ItemFilter))
            {
            }
            column(ItemFilter; ItemFilter)
            {
            }
            column(STRSUBSTNO___1___2___Value_Entry__TABLECAPTION_ItemLedgEntryFilter_; STRSUBSTNO('%1: %2', "Value Entry".TABLECAPTION, ItemLedgEntryFilter))
            {
            }
            column(ItemLedgEntryFilter; ItemLedgEntryFilter)
            {
            }
            column(Item__No__; "No.")
            {
            }
            column(Item_Description; Description)
            {
            }
            column(Item__Base_Unit_of_Measure_; "Base Unit of Measure")
            {
            }
            column(Cust_City; Cust.City)
            {
            }
            column(Value_Entry___Sales_Amount__Actual___Control1000000009; "Value Entry"."Sales Amount (Actual)" + (-"Value Entry"."Discount Amount"))
            {
            }
            column(Value_Entry___Discount_Amount__Control1000000010; -"Value Entry"."Discount Amount")
            {
            }
            column(Profit_Control1000000011; Profit)
            {
                AutoFormatType = 1;
            }
            column(ProfitPct_Control1000000012; ProfitPct)
            {
                DecimalPlaces = 1 : 1;
            }
            column(Value_Entry___Invoiced_Quantity_; -"Value Entry"."Invoiced Quantity")
            {
                DecimalPlaces = 0 : 0;
            }
            column(Item__Inventory_Posting_Group_; "Inventory Posting Group")
            {
            }
            column(Item__Inventory_Posting_Group__Control1000000003; "Inventory Posting Group")
            {
            }
            column(Value_Entry___Sales_Amount__Actual__; "Value Entry"."Sales Amount (Actual)" + (-"Value Entry"."Discount Amount"))
            {
            }
            column(Value_Entry___Discount_Amount_; -"Value Entry"."Discount Amount")
            {
            }
            column(Profit; Profit)
            {
                AutoFormatType = 1;
            }
            column(ProfitPct; ProfitPct)
            {
                DecimalPlaces = 1 : 1;
            }
            column(Invoiced_Quantity__1000000002; -"Value Entry"."Invoiced Quantity")
            {
                DecimalPlaces = 0 : 0;
            }
            column(Inventory___Customer_SalesCaption; Inventory___Customer_SalesCaptionLbl)
            {
            }
            column(CurrReport_PAGENOCaption; CurrReport_PAGENOCaptionLbl)
            {
            }
            column(Value_Entry__Source_No__Caption; Value_Entry__Source_No__CaptionLbl)
            {
            }
            column(Cust_NameCaption; Cust_NameCaptionLbl)
            {
            }
            column(Invoiced_Quantity_Caption; Invoiced_Quantity_CaptionLbl)
            {
            }
            column(Value_Entry__Sales_Amount__Actual__Caption; Value_Entry__Sales_Amount__Actual__CaptionLbl)
            {
            }
            column(Discount_Amount_Caption; Discount_Amount_CaptionLbl)
            {
            }
            column(Profit_Control31Caption; Profit_Control31CaptionLbl)
            {
            }
            column(ProfitPct_Control32Caption; ProfitPct_Control32CaptionLbl)
            {
            }
            column(Item__Inventory_Posting_Group__Control1000000003Caption; FIELDCAPTION("Inventory Posting Group"))
            {
            }
            column(Item__Base_Unit_of_Measure_Caption; FIELDCAPTION("Base Unit of Measure"))
            {
            }
            column(TotalCaption; TotalCaptionLbl)
            {
            }
            column(Item_Variant_Filter; "Variant Filter")
            {
            }
            column(Item_Location_Filter; "Location Filter")
            {
            }
            column(Item_Global_Dimension_1_Filter; "Global Dimension 1 Filter")
            {
            }
            column(Item_Global_Dimension_2_Filter; "Global Dimension 2 Filter")
            {
            }
            column(Item_Date_Filter; "Date Filter")
            {
            }
            column(USERSetupMIS; USERSetup."MIS Approval")
            {

            }
            dataitem("Value Entry"; "Value Entry")
            {
                DataItemLink = "Item No." = FIELD("No."), "Variant Code" = FIELD("Variant Filter"), "Location Code" = FIELD("Location Filter"), "Global Dimension 1 Code" = FIELD("Global Dimension 1 Filter"), "Global Dimension 2 Code" = FIELD("Global Dimension 2 Filter"), "Posting Date" = FIELD("Date Filter");
                DataItemTableView = SORTING("Source Type", "Source No.", "Item No.") WHERE("Source Type" = CONST(Customer), "Document Type" = const("Sales Invoice"));
                RequestFilterFields = "Source No.", "Source Posting Group";
                column(Value_Entry__Source_No__; "Source No.")
                {
                }
                column(Cust_Name; Cust.Name)
                {
                }
                column(Invoiced_Quantity_; -"Invoiced Quantity")
                {
                    DecimalPlaces = 0 : 0;
                }
                column(Value_Entry__Sales_Amount__Actual__; "Sales Amount (Actual)" + (-"Discount Amount"))
                {
                }
                column(Discount_Amount_; -"Discount Amount")
                {
                    AutoFormatType = 1;
                }
                column(Profit_Control31; Profit)
                {
                    AutoFormatType = 1;
                }
                column(ProfitPct_Control32; ProfitPct)
                {
                    DecimalPlaces = 1 : 1;
                }
                column(Item_Description_Control33; Item.Description)
                {
                }
                column(Invoiced_Quantity__Control34; -"Invoiced Quantity")
                {
                    DecimalPlaces = 0 : 0;
                }
                column(Value_Entry__Sales_Amount__Actual___Control35; "Sales Amount (Actual)" + (-"Discount Amount"))
                {
                }
                column(Discount_Amount__Control36; -"Discount Amount")
                {
                }
                column(Profit_Control37; Profit)
                {
                    AutoFormatType = 1;
                }
                column(ProfitPct_Control38; ProfitPct)
                {
                    DecimalPlaces = 1 : 1;
                }
                column(Item__No__1000000001; Item."No.")
                {
                }
                column(Value_Entry_Entry_No_; "Entry No.")
                {
                }
                column(Value_Entry_Item_No_; "Item No.")
                {
                }
                column(Value_Entry_Variant_Code; "Variant Code")
                {
                }
                column(Value_Entry_Location_Code; "Location Code")
                {
                }
                column(Value_Entry_Global_Dimension_1_Code; "Global Dimension 1 Code")
                {
                }
                column(Value_Entry_Global_Dimension_2_Code; "Global Dimension 2 Code")
                {
                }
                column(Value_Entry_Posting_Date; "Posting Date")
                {
                }

                trigger OnAfterGetRecord();
                begin

                    //Clear(Profit);
                    IF "Expected Cost" THEN
                        CurrReport.SKIP;


                    Cust1.GET("Source No.");
                    IF PriceGrp <> '' THEN
                        IF Cust1."Customer Disc. Group" <> PriceGrp THEN
                            CurrReport.SKIP;

                    Profit += "Sales Amount (Actual)" + (-"Discount Amount") + "Cost Amount (Actual)" + "Cost Amount (Non-Invtbl.)";
                    salesAmountactual += ("Sales Amount (Actual)" + (-"Discount Amount"));
                    IF "Source No." <> '' THEN
                        IF NOT Cust.GET("Source No.") THEN
                            CLEAR(Cust);

                    IF ShowSecFrt THEN BEGIN
                        FOR i := 1 TO 17 DO BEGIN
                            IF "Value Entry"."Global Dimension 1 Code" = Accloc[i] THEN BEGIN
                                AcclocQty[i] += "Value Entry"."Invoiced Quantity";
                                AcclocAmt[i] += ("Value Entry"."Sales Amount (Actual)" + (-"Value Entry"."Discount Amount"));
                                IF ItemRec.GET("Item No.") THEN
                                    AcclocQtyLtr[i] += "Invoiced Quantity" * ItemRec."Conversion Unit";
                            END;
                        END;
                    END;
                    //Value Entry, GroupHeader(1) - OnPreSection()
                    RuomQty := 0;


                    //Value Entry, GroupFooter(2) - OnPreSection()
                    //CalcProfitPct;
                    IF "Source No." <> '' THEN
                        IF NOT Cust.GET("Source No.") THEN
                            CLEAR(Cust);

                    /*IF ShowSecFrt THEN
                        CurrReport.SHOWOUTPUT(FALSE);*///Balu

                    /*IF (NOT showCust) THEN   // show cust to display customers when select SAA>>
                        CurrReport.SHOWOUTPUT(FALSE);*///Balu

                    IF ItemRec.GET("Item No.") THEN BEGIN
                        RuomQty := -"Invoiced Quantity" * ItemRec."Conversion Unit";
                        ConvUnit := ItemRec."Conversion Unit";
                    END;


                    /*IF PrintExcel AND NOT ShowSecFrt THEN BEGIN
                        RowNo += 1;
                        EnterCell(RowNo, 1, FORMAT("Source No."), FALSE, FALSE, FALSE);
                        EnterCell(RowNo, 2, FORMAT(Cust.Name), FALSE, FALSE, FALSE);
                        EnterCell(RowNo, 3, FORMAT(-"Invoiced Quantity"), FALSE, FALSE, FALSE);
                        EnterCell(RowNo, 4, FORMAT("Sales Amount (Actual)"), FALSE, FALSE, FALSE);
                        EnterCell(RowNo, 5, FORMAT(-"Discount Amount"), FALSE, FALSE, FALSE);
                        EnterCell(RowNo, 6, FORMAT(Profit), FALSE, FALSE, FALSE);
                        EnterCell(RowNo, 7, FORMAT(ProfitPct), FALSE, FALSE, FALSE);
                        EnterCell(RowNo, 9, FORMAT("Global Dimension 1 Code"), FALSE, FALSE, FALSE);
                        // EnterCell(RowNo,10,FORMAT(RuomQty),FALSE,FALSE,FALSE);
                        EnterCell(RowNo, 11, FORMAT(ConvUnit), TRUE, FALSE, FALSE);
                        IF DefDim.GET(27, "No.", 'SEGMENT') THEN
                            EnterCell(RowNo, 12, DefDim."Dimension Value Code", FALSE, FALSE, FALSE);

                    END;*///B2BP1.0
                    InvQty += "Value Entry"."Invoiced Quantity";
                    SalAmnt += ("Value Entry"."Sales Amount (Actual)" + (-"Value Entry"."Discount Amount"));
                    DsctAmnt += "Value Entry"."Discount Amount";
                    InvQty1 += "Invoiced Quantity";
                    SalAmnt1 += ("Sales Amount (Actual)" + (-"Discount Amount"));
                    DsctAmnt1 += "Discount Amount";


                end;

                trigger OnPreDataItem();
                begin
                    /* CurrReport.CREATETOTALS("Invoiced Quantity", "Sales Amount (Actual)", "Discount Amount", Profit,
                      "Value Entry"."Invoiced Quantity", RuomQty, TRuomQty, GRuomQty);*/
                    USERSetup.GET(UserId);


                end;

                trigger OnPostDataItem()
                begin
                    Clear(ConvUnit);
                    Clear(RuomQty);
                    // Value Entry, Footer(3) - OnPreSection()
                    /*IF ShowSecFrt THEN
                        CurrReport.SHOWOUTPUT(FALSE);*///Balu

                    //NYO request by Mr. Ita 29/03/2017
                    /*IF SHOWINVEND THEN
                        CurrReport.SHOWOUTPUT(FALSE);*///Balu
                    //NYO request by Mr. Ita 29/03/2017

                    CalcProfitPct2();
                    IF ItemRec.GET("Item No.") THEN BEGIN
                        RuomQty := -InvQty1 * ItemRec."Conversion Unit";
                        ConvUnit := ItemRec."Conversion Unit";
                    END;

                    TRuomQty := TRuomQty + RuomQty;

                    IF PrintExcel THEN BEGIN
                        RowNo += 1;
                        EnterCell(RowNo, 1, FORMAT(Item."No."), FALSE, FALSE, FALSE);
                        EnterCell(RowNo, 2, FORMAT(Item.Description), FALSE, FALSE, FALSE);
                        EnterCell(RowNo, 3, FORMAT(-InvQty1), FALSE, FALSE, FALSE);
                        EnterCell(RowNo, 4, FORMAT(SalAmnt1), FALSE, FALSE, FALSE);
                        EnterCell(RowNo, 5, FORMAT(-DsctAmnt1), FALSE, FALSE, FALSE);
                        EnterCell(RowNo, 6, FORMAT(Profit), FALSE, FALSE, FALSE);
                        EnterCell(RowNo, 7, FORMAT(ProfitPct), FALSE, FALSE, FALSE);
                        EnterCell(RowNo, 10, FORMAT(RuomQty), FALSE, FALSE, FALSE);
                        EnterCell(RowNo, 11, FORMAT(ConvUnit), FALSE, FALSE, FALSE);
                        IF DefDim.GET(27, "No.", 'SEGMENT') THEN
                            EnterCell(RowNo, 12, DefDim."Dimension Value Code", FALSE, FALSE, FALSE);

                    END;
                    Clear(salesAmountactual);

                    Clear(Profit);
                end;
            }
            trigger OnAfterGetRecord()
            begin
                Clear(InvQty1);
                Clear(SalAmnt1);
                Clear(DsctAmnt1);
                //Item, Body(6) - OnPreSection()
                /*IF (NOT showCust) OR SHOWINVEND THEN   // show cust to display customers when select SAA>>
                    CurrReport.SHOWOUTPUT(FALSE);*///Balu
                //SAA3.0 <<
                //NYO>>
                /*IF ShowSecFrt THEN
                    CurrReport.SHOWOUTPUT(FALSE);*///Balu
                //NYO<<
                /*IF PrintExcel THEN BEGIN
                    RowNo += 1;
                    EnterCell(RowNo, 1, FORMAT("No."), TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 2, FORMAT(Description), TRUE, FALSE, FALSE);
                    RowNo += 1;
                    EnterCell(RowNo, 2, FORMAT('Base Unit of Measure' + "Base Unit of Measure"), TRUE, FALSE, FALSE);
                    RowNo += 1;
                    EnterCell(RowNo, 3, FORMAT(Cust.City), TRUE, FALSE, FALSE);
                END;*///Balu


                //Item, GroupFooter(7) - OnPreSection()
                //CurrReport.SHOWOUTPUT := CurrReport.TOTALSCAUSEDBY = Item.FIELDNO("Inventory Posting Group");

                /*IF ShowSecFrt THEN
                    CurrReport.SHOWOUTPUT(FALSE);*///Balu


                GRuomQty := GRuomQty + TRuomQty;


                //IF NOT SHOWINVEND THEN
                //CurrReport.SHOWOUTPUT(FALSE);

                /*IF (-"Value Entry"."Invoiced Quantity" = 0) AND ("Value Entry"."Sales Amount (Actual)" = 0) AND (-"Value Entry"."Discount Amount" = 0)
                THEN
                    CurrReport.SHOWOUTPUT := FALSE;*///Balu

                IF PrintExcel AND (NOT showonlyitemtotals) THEN BEGIN
                    RowNo += 1;
                    EnterCell(RowNo, 1, FORMAT("Inventory Posting Group"), FALSE, FALSE, FALSE);
                    EnterCell(RowNo, 3, FORMAT(-"Value Entry"."Invoiced Quantity"), FALSE, FALSE, FALSE);
                    EnterCell(RowNo, 4, FORMAT(("Value Entry"."Sales Amount (Actual)" + (-"Value Entry"."Discount Amount"))), FALSE, FALSE, FALSE);
                    EnterCell(RowNo, 5, FORMAT(-"Value Entry"."Discount Amount"), FALSE, FALSE, FALSE);
                    EnterCell(RowNo, 6, FORMAT(Profit), FALSE, FALSE, FALSE);
                    EnterCell(RowNo, 7, FORMAT(ProfitPct), FALSE, FALSE, FALSE);
                    EnterCell(RowNo, 8, FORMAT("Inventory Posting Group"), FALSE, FALSE, FALSE);
                    EnterCell(RowNo, 9, FORMAT("Global Dimension 1 Code"), FALSE, FALSE, FALSE);
                    //EnterCell(RowNo,10,FORMAT(TRuomQty),FALSE,FALSE,FALSE);
                    EnterCell(RowNo, 11, FORMAT("Conversion Unit"), FALSE, FALSE, FALSE);
                    IF DefDim.GET(27, "No.", 'SEGMENT') THEN
                        EnterCell(RowNo, 12, DefDim."Dimension Value Code", FALSE, FALSE, FALSE);
                END;



            end;

            trigger OnPostDataItem();
            begin
                //Item, Footer(8) - OnPreSection()
                CalcProfitPct2;
                /*IF (-"Value Entry"."Invoiced Quantity" = 0) AND ("Value Entry"."Sales Amount (Actual)" = 0) AND (-"Value Entry"."Discount Amount" = 0)
                THEN
                    CurrReport.SHOWOUTPUT := FALSE;*///Balu

                IF PrintExcel AND NOT ShowSecFrt THEN BEGIN
                    RowNo += 1;
                    EnterCell(RowNo, 1, ('Total'), TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 3, FORMAT(-InvQty), TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 4, FORMAT(SalAmnt), TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 5, FORMAT(-DsctAmnt), TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 6, FORMAT(Profit), TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 7, FORMAT(ProfitPct), TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 10, FORMAT(RuomQty), FALSE, FALSE, FALSE);
                    EnterCell(RowNo, 11, FORMAT("Conversion Unit"), FALSE, FALSE, FALSE);

                END;

                IF PrintExcel AND ShowSecFrt THEN BEGIN
                    FOR i := 1 TO 17 DO BEGIN
                        //Current Year
                        AnalysisViewEntry.RESET;
                        AnalysisViewEntry.SETRANGE(AnalysisViewEntry."Analysis View Code", 'MISANALYSE');
                        AnalysisViewEntry.SETRANGE(AnalysisViewEntry."Account No.", AnalysisAcc);
                        AnalysisViewEntry.SETRANGE(AnalysisViewEntry."Dimension 1 Value Code", Accloc[i]);
                        AnalysisViewEntry.SETRANGE(AnalysisViewEntry."Posting Date", StartDate, EndDate);
                        IF AnalysisViewEntry.FINDSET THEN BEGIN
                            REPEAT
                                AcclocLocalFrt[i] += (-AnalysisViewEntry.Amount);
                            UNTIL AnalysisViewEntry.NEXT = 0;
                        END;
                        //Previous Year
                        AnalysisViewEntry.RESET;
                        AnalysisViewEntry.SETRANGE(AnalysisViewEntry."Analysis View Code", 'MISANALYSE');
                        AnalysisViewEntry.SETRANGE(AnalysisViewEntry."Account No.", AnalysisAcc);
                        AnalysisViewEntry.SETRANGE(AnalysisViewEntry."Dimension 1 Value Code", Accloc[i]);
                        AnalysisViewEntry.SETRANGE(AnalysisViewEntry."Posting Date", PrevStartDate, PrevEndDate);
                        IF AnalysisViewEntry.FINDSET THEN BEGIN
                            REPEAT
                                PrevAcclocLocalFrt[i] += (-AnalysisViewEntry.Amount);
                            UNTIL AnalysisViewEntry.NEXT = 0;
                        END;

                        ValueEntry.RESET;
                        ValueEntry.SETCURRENTKEY(
                        "Source Type", "Source No.", "Global Dimension 1 Code", "Global Dimension 2 Code",
                        "Item No.", "Posting Date", "Entry Type", Adjustment);
                        ValueEntry.SETRANGE(ValueEntry."Source Type", ValueEntry."Source Type"::Customer);
                        ValueEntry.SETRANGE(ValueEntry."Posting Date", PrevStartDate, PrevEndDate);
                        ValueEntry.SETRANGE(ValueEntry."Global Dimension 1 Code", Accloc[i]);
                        IF ValueEntry.FINDSET THEN BEGIN
                            REPEAT
                                PrevAcclocAmt[i] += (ValueEntry."Sales Amount (Actual)" + (-ValueEntry."Discount Amount"));
                                PrevAcclocQty[i] += ValueEntry."Invoiced Quantity";
                                IF ItemRec.GET(ValueEntry."Item No.") THEN
                                    PrevAcclocQtyLtr[i] += ValueEntry."Invoiced Quantity" * ItemRec."Conversion Unit";
                            UNTIL ValueEntry.NEXT = 0;
                        END;

                        GLBudgetEntry.SETRANGE(GLBudgetEntry."Global Dimension 1 Code", Accloc[i]);
                        GLBudgetEntry.SETRANGE(GLBudgetEntry."G/L Account No.", AnalysisAcc);
                        GLBudgetEntry.SETRANGE(GLBudgetEntry."Budget Name", GLBudgetName);
                        GLBudgetEntry.SETRANGE(GLBudgetEntry.Date, StartDate, EndDate);
                        IF GLBudgetEntry.FINDSET THEN BEGIN
                            REPEAT
                                GLBudgetAmount[i] += GLBudgetEntry.Amount;
                            UNTIL GLBudgetEntry.NEXT = 0;
                        END;

                        RowNo += 1;
                        EnterCell(RowNo, 1, FORMAT(Accloc[i]), FALSE, FALSE, FALSE);
                        EnterCell(RowNo, 2, FORMAT(ABS(ROUND(AcclocQty[i], 0.01))), FALSE, FALSE, FALSE);
                        //EnterCell(RowNo,3,FORMAT(ABS(ROUND(AcclocQtyLtr[i],0.01))),FALSE,FALSE,FALSE);
                        EnterCell(RowNo, 3, FORMAT(ABS(ROUND(AcclocAmt[i], 0.01))), FALSE, FALSE, FALSE);
                        EnterCell(RowNo, 4, FORMAT(ABS(ROUND(AcclocLocalFrt[i], 0.01))), FALSE, FALSE, FALSE);
                        IF (AcclocLocalFrt[i] <> 0) AND (AcclocQty[i] <> 0) THEN
                            EnterCell(RowNo, 5, FORMAT(ABS(ROUND((AcclocLocalFrt[i] / AcclocQty[i]), 0.01))), FALSE, FALSE, FALSE)
                        ELSE
                            EnterCell(RowNo, 5, '0', FALSE, FALSE, FALSE);
                        //IF (AcclocLocalFrt[i]<>0) AND (AcclocQtyLtr[i] <>0) THEN
                        //EnterCell(RowNo,7,FORMAT(ABS(ROUND((AcclocLocalFrt[i]/AcclocQtyLtr[i]),0.01))),FALSE,FALSE,FALSE)
                        //ELSE
                        //EnterCell(RowNo,7,'0',FALSE,FALSE,FALSE);
                        IF (AcclocLocalFrt[i] <> 0) AND (AcclocAmt[i] <> 0) THEN
                            EnterCell(RowNo, 6, FORMAT(ABS((ROUND((AcclocLocalFrt[i] / AcclocAmt[i]), 0.001) * 100))), FALSE, FALSE, FALSE)
                        ELSE
                            EnterCell(RowNo, 6, '0', FALSE, FALSE, FALSE);
                        //EnterCell(RowNo,9,FORMAT(PrevAcclocQty[i]),FALSE,FALSE,FALSE);
                        //EnterCell(RowNo,10,FORMAT(PrevAcclocQtyLtr[i]),FALSE,FALSE,FALSE);
                        //EnterCell(RowNo,11,FORMAT(PrevAcclocAmt[i]),FALSE,FALSE,FALSE);
                        //EnterCell(RowNo,8,FORMAT(PrevAcclocLocalFrt[i]),FALSE,FALSE,FALSE);
                        IF (PrevAcclocLocalFrt[i] <> 0) AND (PrevAcclocQty[i] <> 0) THEN
                            EnterCell(RowNo, 7, FORMAT(ABS(ROUND((PrevAcclocLocalFrt[i] / PrevAcclocQty[i]), 0.01))), FALSE, FALSE, FALSE)
                        ELSE
                            EnterCell(RowNo, 7, '0', FALSE, FALSE, FALSE);
                        //IF (PrevAcclocLocalFrt[i]<>0) AND (PrevAcclocQtyLtr[i] <>0) THEN
                        //EnterCell(RowNo,10,FORMAT(ABS(ROUND((PrevAcclocLocalFrt[i]/PrevAcclocQtyLtr[i]),0.01))),FALSE,FALSE,FALSE)
                        //ELSE
                        //EnterCell(RowNo,10,'0',FALSE,FALSE,FALSE);
                        IF (PrevAcclocLocalFrt[i] <> 0) AND (PrevAcclocAmt[i] <> 0) THEN
                            EnterCell(RowNo, 8, FORMAT(ABS((ROUND((PrevAcclocLocalFrt[i] / PrevAcclocAmt[i]), 0.001) * 100))), FALSE, FALSE, FALSE)
                        ELSE
                            EnterCell(RowNo, 8, '0', FALSE, FALSE, FALSE);
                        EnterCell(RowNo, 9, FORMAT(GLBudgetAmount[i]), FALSE, FALSE, FALSE);
                        EnterCell(RowNo, 10, FORMAT(PrevAcclocLocalFrt[i] - AcclocLocalFrt[i]), FALSE, FALSE, FALSE);
                        EnterCell(RowNo, 11, FORMAT(AcclocLocalFrt[i] - GLBudgetAmount[i]), FALSE, FALSE, FALSE);
                    END;
                END;
            end;

            trigger OnPreDataItem();
            begin
                /*CurrReport.CREATETOTALS("Value Entry"."Sales Amount (Actual)", "Value Entry"."Discount Amount", Profit,
                 "Value Entry"."Invoiced Quantity", RuomQty, TRuomQty, GRuomQty); //SAA3.0*/

                IF PrintExcel THEN BEGIN
                    TempExcelBuffer.DELETEALL;
                    CLEAR(TempExcelBuffer);
                    RowNo := 1;
                    EnterCell(RowNo, 1, 'Inventory- Customer Sales', TRUE, FALSE, FALSE);
                    // EnterCell(RowNo,15,FORMAT(TODAY,0,4),TRUE,FALSE,FALSE);
                    RowNo += 1;
                    EnterCell(RowNo, 1, FORMAT(UPPERCASE(COMPANYNAME)), TRUE, FALSE, FALSE);
                    //EnterCell(RowNo,15,FORMAT(USERID),TRUE,FALSE,FALSE);
                    RowNo += 1;
                    EnterCell(RowNo, 1, FORMAT(ItemFilter), TRUE, FALSE, FALSE);
                    RowNo += 1;
                    EnterCell(RowNo, 1, FORMAT(ItemLedgEntryFilter), TRUE, FALSE, FALSE);
                    IF NOT ShowSecFrt THEN BEGIN
                        RowNo += 1;
                        EnterCell(RowNo, 1, 'Item/Cust No.', TRUE, FALSE, FALSE);
                        EnterCell(RowNo, 2, 'Name', TRUE, FALSE, FALSE);
                        EnterCell(RowNo, 3, 'Invoiced Quantity', TRUE, FALSE, FALSE);
                        EnterCell(RowNo, 4, 'Amount', TRUE, FALSE, FALSE);
                        EnterCell(RowNo, 5, 'Discount Amount', TRUE, FALSE, FALSE);
                        EnterCell(RowNo, 6, 'Profit', TRUE, FALSE, FALSE);
                        EnterCell(RowNo, 7, 'Profit %', TRUE, FALSE, FALSE);
                        EnterCell(RowNo, 8, 'Inventory Posting Group', TRUE, FALSE, FALSE);
                        EnterCell(RowNo, 9, 'Accounting Location', TRUE, FALSE, FALSE);
                        EnterCell(RowNo, 10, 'Qty in Ltrs', TRUE, FALSE, FALSE);
                        EnterCell(RowNo, 11, 'Conversion Unit', TRUE, FALSE, FALSE);
                        EnterCell(RowNo, 12, 'Segment', TRUE, FALSE, FALSE);
                    END ELSE BEGIN
                        RowNo += 1;
                        EnterCell(RowNo, 1, 'Accounting Location', TRUE, FALSE, FALSE);
                        EnterCell(RowNo, 2, 'Current Qty in Trays', TRUE, FALSE, FALSE);
                        // EnterCell(RowNo,3,'Current Qty in Ltr',TRUE,FALSE,FALSE);
                        EnterCell(RowNo, 3, 'Current Sales Amount', TRUE, FALSE, FALSE);
                        EnterCell(RowNo, 4, 'Current Local Freight', TRUE, FALSE, FALSE);
                        EnterCell(RowNo, 5, 'Current Freight Per Tray in LCY', TRUE, FALSE, FALSE);
                        //EnterCell(RowNo,7,'Current Per Ltr in LCY',TRUE,FALSE,FALSE);
                        EnterCell(RowNo, 6, 'Current % of Sales', TRUE, FALSE, FALSE);
                        // EnterCell(RowNo,9,'Prev. Mnth Qty in Trays',TRUE,FALSE,FALSE);
                        //EnterCell(RowNo,10,'Prev. Mnth Qty in Ltr',TRUE,FALSE,FALSE);
                        //EnterCell(RowNo,11,'Prev. Mnth Sales Amount',TRUE,FALSE,FALSE);
                        //EnterCell(RowNo,12,'Prev. Mnth Local Freight',TRUE,FALSE,FALSE);
                        EnterCell(RowNo, 7, 'Prev. Mnth Freight Per Tray in LCY', TRUE, FALSE, FALSE);
                        //EnterCell(RowNo,8,'Prev. Mnth Per Ltr in LCY',TRUE,FALSE,FALSE);
                        EnterCell(RowNo, 8, 'Prev. Mnth % of Sales', TRUE, FALSE, FALSE);
                        EnterCell(RowNo, 9, 'Budgeted Amount', FALSE, FALSE, FALSE);
                        EnterCell(RowNo, 10, 'Variant on Local Freight', FALSE, FALSE, FALSE);
                        EnterCell(RowNo, 11, 'Variant on Budgeted Freight', FALSE, FALSE, FALSE);
                    END;
                END;
            end;
        }
    }

    requestpage
    {

        layout
        {
            area(Content)
            {
                field(showdetails; showdetail)
                {

                }
                field(ShowbyInventorypostingGrp; SHOWINVEND)
                {

                }
                field(showCustomers; showCust)

                {

                }
                field(showonlyitemamountsinExcel; showonlyitemtotals)
                {

                }
                field(CopytoExcel; PrintExcel)
                {

                }
                field(ShowSecondaryFreightBranch; ShowSecFrt)
                {

                }
                field(AnalysisGlAccount; AnalysisAcc)
                {
                    TableRelation = "G/L Account"."No.";

                }
                field(BudgetName; GLBudgetName)
                {
                    TableRelation = "G/L Account".Name;
                }
                field(CustDiscountGrp; PriceGrp)
                {
                    TableRelation = "Customer Discount Group".Code;
                }

            }
        }

        actions
        {
        }
    }

    labels
    {
    }

    trigger OnPreReport();
    begin
        ItemFilter := Item.GETFILTERS;
        ItemLedgEntryFilter := "Value Entry".GETFILTERS;
        PeriodText := "Value Entry".GETFILTER("Posting Date");

        i := 1;
        DimensionValue.SETRANGE(DimensionValue."Dimension Code", 'ACCLOC');
        DimensionValue.SETRANGE(DimensionValue."Branch Online", TRUE);
        DimensionValue.SETRANGE(DimensionValue.Blocked, FALSE);
        IF DimensionValue.FINDSET THEN BEGIN
            REPEAT
                Accloc[i] := DimensionValue.Code;
                i += 1;
            UNTIL DimensionValue.NEXT = 0;
        END;

        StartDate := Item.GETRANGEMIN(Item."Date Filter");
        EndDate := Item.GETRANGEMAX(Item."Date Filter");

        PrevEndDate := CALCDATE('-1D', StartDate);
        PrevStartDate := CALCDATE('-1M', StartDate);

        IF ShowSecFrt AND (AnalysisAcc = '') THEN
            ERROR(Text001);

        IF ShowSecFrt AND (GLBudgetName = '') THEN
            ERROR(Text002);
    end;

    trigger OnPostReport()
    begin
        IF PrintExcel THEN BEGIN
            TempExcelBuffer.CreateNewBook('Inv Cust Sales');
            TempExcelBuffer.WriteSheet('Inv Cust Sales', CompanyName(), UserId());
            TempExcelBuffer.CloseBook();
            TempExcelBuffer.OpenExcel();
        END;
    end;

    var
        Text000: Label 'Period: %1';
        Cust: Record customer;
        Custh: Record customer;
        Profit: Decimal;
        ProfitPct: Decimal;
        PeriodText: Text[30];
        ItemFilter: Text[1000];
        ItemLedgEntryFilter: Text[1000];
        showdetail: Boolean;
        Usersetup: Record "User Setup";
        SHOWINVEND: Boolean;
        PrintExcel: Boolean;
        RowNo: Integer;
        ColumnNo: Integer;
        TempExcelBuffer: Record "Excel Buffer" temporary;
        RuomQty: Decimal;
        TRuomQty: Decimal;
        GRuomQty: Decimal;
        GTqty: Decimal;
        ItemRec: Record item;
        ConvUnit: Decimal;
        DefDim: Record "Default Dimension";
        DimensionValue: Record "Dimension Value";
        Accloc: array[50] of Code[20];
        AcclocAmt: array[50] of Decimal;
        AcclocQty: array[50] of Decimal;
        AcclocQtyLtr: array[50] of Decimal;
        AcclocLocalFrt: array[50] of Decimal;
        i: Integer;
        AnalysisViewEntry: Record "Analysis View Entry";
        AnalysisAcc: Code[20];
        ShowSecFrt: Boolean;
        EndDate: Date;
        StartDate: Date;
        PrevEndDate: Date;
        PrevStartDate: Date;
        ValueEntry: Record "Value Entry";
        PrevAcclocAmt: array[50] of Decimal;
        PrevAcclocQty: array[50] of Decimal;
        PrevAcclocQtyLtr: array[50] of Decimal;
        PrevAcclocLocalFrt: array[50] of Decimal;
        Text001: Label '"Analysis Account must not be blank "';
        GLBudgetName: Code[10];
        GLBudgetEntry: Record "G/L Budget Entry";
        GLBudgetAmount: array[50] of Decimal;
        Text002: Label '"Budget Name must not be blank "';
        showCust: Boolean;
        showonlyitemtotals: Boolean;
        PriceGrp: Code[20];
        Cust1: Record Customer;
        Inventory___Customer_SalesCaptionLbl: Label 'Inventory - Customer Sales';
        CurrReport_PAGENOCaptionLbl: Label 'Page';
        Value_Entry__Source_No__CaptionLbl: Label 'Item/Cust. No.';
        Cust_NameCaptionLbl: Label 'Name';
        Invoiced_Quantity_CaptionLbl: Label 'Invoiced Quantity';
        Value_Entry__Sales_Amount__Actual__CaptionLbl: Label 'Amount';
        Discount_Amount_CaptionLbl: Label 'Discount Amount';
        Profit_Control31CaptionLbl: Label 'Profit';
        ProfitPct_Control32CaptionLbl: Label 'Profit %';
        TotalCaptionLbl: Label 'Total';
        InvQty: Decimal;
        SalAmnt: Decimal;
        DsctAmnt: Decimal;
        InvQty1: Decimal;
        SalAmnt1: Decimal;
        DsctAmnt1: Decimal;
        salesAmountactual: Decimal;


    local procedure CalcProfitPct();
    begin
        WITH "Value Entry" DO BEGIN
            IF "Sales Amount (Actual)" <> 0 THEN
                ProfitPct := ROUND(Profit / ("Sales Amount (Actual)" + (-"Discount Amount")) * 100, 0.1)
            ELSE
                ProfitPct := 0;
        END;

        //SAA3.0 >>
        Usersetup.GET(USERID);
        IF NOT Usersetup."MIS Approval" THEN BEGIN
            Profit := 0;
            ProfitPct := 0;
        END;
        //SAA3.0 <<
    end;

    local procedure CalcProfitPct2();
    begin

        IF salesAmountactual <> 0 THEN
            ProfitPct := ROUND(Profit / salesAmountactual * 100, 0.1)
        ELSE
            ProfitPct := 0;

        //SAA3.0 >>
        Usersetup.GET(USERID);
        IF NOT Usersetup."MIS Approval" THEN BEGIN
            Profit := 0;
            ProfitPct := 0;
        END;
        //SAA3.0 <<
    end;

    procedure EnterCell(RowNo: Integer; ColumnNo: Integer; CellValue: Text[500]; Bold: Boolean; Italic: Boolean; Underline: Boolean);
    begin
        TempExcelBuffer.INIT;
        TempExcelBuffer.VALIDATE("Row No.", RowNo);
        TempExcelBuffer.VALIDATE("Column No.", ColumnNo);
        TempExcelBuffer."Cell Value as Text" := CellValue;
        TempExcelBuffer.Formula := '';
        TempExcelBuffer.Bold := Bold;
        TempExcelBuffer.Italic := Italic;
        TempExcelBuffer.Underline := Underline;
        TempExcelBuffer.INSERT;
    end;
}
