table 50798 "Item Barcodes Unit of Measure"
{
    Caption = 'Item Barcodes Unit of Measure';
    DataClassification = ToBeClassified;

    fields
    {
        field(1; Barcode; Text[100])
        {
            Caption = 'Barcode';
        }
        field(2; "Item No."; Code[50])
        {
            Caption = 'Item No.';
        }
        field(3; "Unit Of Measure"; Code[10])
        {
            Caption = 'Unit Of Measure';
            TableRelation = "Unit of Measure";
        }
        field(4; "Clearwox Product ID"; Text[20])
        {
            Caption = 'Clearwox Product ID';
        }
    }
    keys
    {
        key(PK; Barcode)
        {
            Clustered = true;
        }
    }
}
