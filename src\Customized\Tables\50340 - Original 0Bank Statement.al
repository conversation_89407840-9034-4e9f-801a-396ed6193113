table 50340 "Original Bank Statement"
{
    // version NAVW16.00,BNKRECON

    Caption = 'Original Bank Statement';
    LookupPageID = "Bank Upload Sheet";

    fields
    {
        field(1; "Code"; Code[10])
        {
            DataClassification = CustomerContent;
            Caption = 'Code';
            NotBlank = true;
        }
        field(2; Text; Text[50])
        {
            Caption = 'Text';
            DataClassification = CustomerContent;
        }
        field(65000; "Matching Status"; Option)
        {
            DataClassification = CustomerContent;
            OptionCaption = 'Exact,Probable,Not Matching,Cancel,Manually Matched'; // G2S 7708-CAS-01421-Z6M7V9
            OptionMembers = Exact,Probable,"Not Matching",Cancel,"Manually Matched"; // G2S 7708-CAS-01421-Z6M7V9
        }
        field(65001; "Bank Posting Date"; Date)
        {
            DataClassification = CustomerContent;
        }
        field(65002; "Bank Doc. No."; Code[50])
        {
            DataClassification = CustomerContent;
        }
        field(65003; "Bank Narration"; Text[200])
        {
            DataClassification = CustomerContent;
        }
        field(65004; "Bank Debit Amount"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(65005; "Bank Credit Amount"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(65006; "Bank Deposit Slip No."; Code[20])
        {
            DataClassification = CustomerContent;
        }
        field(65007; "Line No."; Integer)
        {
            DataClassification = CustomerContent;
        }
        field(65008; "Statement No."; Code[20])
        {
            DataClassification = CustomerContent;
        }
        field(65009; Matching; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(65010; "Matched Line No."; Integer)
        {
            DataClassification = CustomerContent;
        }
        field(65011; "Mapped with BLE"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(65012; "Mapped with BLE Entry No."; Integer)
        {
            DataClassification = CustomerContent;
        }
        field(65013; "Manually Mapped"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(65014; "Manually Mapped By"; Code[50])
        {
            DataClassification = CustomerContent;
        }
        field(65015; "Manually Mapped on"; DateTime)
        {
            DataClassification = CustomerContent;
        }
        field(65016; "MatchLine No"; Integer)
        {
            DataClassification = CustomerContent;
        }
        field(65017; "Cancel No"; Integer)
        {
            DataClassification = CustomerContent;
        }
        field(65018; "Outstanding Entry"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(65019; "Outstanding Entry Line"; Integer)
        {
            DataClassification = CustomerContent;
        }
        field(65020; Select; Boolean)
        {
            DataClassification = CustomerContent;
        }
    }

    keys
    {
        key(Key1; "Code", "Statement No.", "Line No.")
        {
            SumIndexFields = "Bank Debit Amount", "Bank Credit Amount";
        }
        key(Key2; "Bank Debit Amount", "Bank Credit Amount")
        {
        }
        key(Key3; "MatchLine No")
        {
        }
        key(Key4; "Cancel No")
        {
        }
        key(Key5; "Bank Credit Amount", "Bank Debit Amount")
        {
        }
        key(Key6; "Bank Posting Date")
        {
        }
        key(Key7; "Bank Narration")
        {
        }
    }

    fieldgroups
    {
        fieldgroup(DropDown; "Code", Text)
        {
        }
    }
}

