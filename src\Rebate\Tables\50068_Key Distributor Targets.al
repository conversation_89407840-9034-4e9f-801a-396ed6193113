table 50068 "Key Distributor Targets" //PKON22JA12-3-CR220010 //PKON22JA26 whole object //B2BSPON22JU20
//B2BSPONJUL22
{
    // version KD

    DataClassification = CustomerContent;
    fields
    {
        field(1; "Target Period"; Code[10])
        {
            TableRelation = "Rebate Period Codes".Code;
            DataClassification = CustomerContent;

            trigger OnValidate();
            begin
                Rebateperiod.SETFILTER(Code, "Target Period");
                if Rebateperiod.FINDFIRST then begin
                    "Period Start Date" := Rebateperiod."Start Date";
                    "Period End Date" := Rebateperiod."End Date";
                end;
            end;
        }
        field(2; "Customer No."; Code[20])
        {
            TableRelation = Customer;
            DataClassification = CustomerContent;

            trigger OnValidate();
            begin
                if Cust.GET("Customer No.") then
                    "Customer Name" := Cust.Name
                else
                    "Customer Name" := '';

                /*CustomerDiscount.RESET;
                CustomerDiscount.SETFILTER("Customer No.","Customer No.");
                CustomerDiscount.SETRANGE(Status,CustomerDiscount.Status::Released);
                CustomerDiscount.SETFILTER(CustomerDiscount."Sales Discount Group",'KEYDISTRIB');
                IF NOT CustomerDiscount.FINDSET THEN
                  ERROR('The Customer %1 is not a Key distributor.',"Customer No.");
                */

            end;
        }
        field(3; "Monthly Big Target"; Decimal)
        {
            DecimalPlaces = 0 : 3;
            DataClassification = CustomerContent;
        }
        field(4; "Monthly Small Target"; Decimal)
        {
            DecimalPlaces = 0 : 3;
            DataClassification = CustomerContent;
        }
        field(5; "Monthly Target Value"; Decimal)
        {
            DecimalPlaces = 0 : 3;
            DataClassification = CustomerContent;
        }
        field(6; "Target By"; Code[50])
        {
            TableRelation = "User Setup"."User ID";
            DataClassification = CustomerContent;
        }
        field(7; "Imported Date"; Date)
        {
            DataClassification = CustomerContent;
        }
        field(8; "Customer Name"; Text[80])
        {
            DataClassification = CustomerContent;
        }
        field(9; "Monthly Total Big Ach. Value"; Decimal)
        {
            DecimalPlaces = 0 : 3;
            DataClassification = CustomerContent;
        }
        field(10; "Monthly Total Smal Ach. Value"; Decimal)
        {
            DecimalPlaces = 0 : 3;
            DataClassification = CustomerContent;
        }
        field(11; "Monthly Total Ach. Value"; Decimal)
        {
            DecimalPlaces = 0 : 3;

            FieldClass = FlowField;
            Editable = false;
            CalcFormula = sum(KDRebateData."Amount Invoiced" where(Code = filter(1), "Rebate Period Code" = field("Target Period"), "Customer Code" = field("Customer No.")));
        }
        field(12; "Monthly All Inv. Rebate"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(13; "Big Volume Rebate"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(14; "Small Volume Rebate"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(15; "Period Start Date"; Date)
        {
            DataClassification = CustomerContent;
        }
        field(16; "Period End Date"; Date)
        {
            DataClassification = CustomerContent;
        }
        field(17; Processed; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(18; "Processed Date"; Date)
        {
            DataClassification = CustomerContent;
        }
        field(19; "Quarterly Target Value"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(20; "Quarterly Achievement"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(21; "Quarterly Rebate"; Decimal)
        {
            DataClassification = CustomerContent;
            Caption = 'Quarterly Discount';
        }



        field(22; "Resposibility Center"; Code[20])
        {
            TableRelation = "Responsibility Center";
            DataClassification = CustomerContent;
        }
        field(24; "Discount group"; Code[20])
        {
            CalcFormula = Lookup("Customer Sales Price/Discount"."Sales Discount Group" WHERE("Customer No." = FIELD("Customer No."),
                                                                                               Status = CONST(Released)));
            Editable = false;
            FieldClass = FlowField;
        }
        field(25; "DMS Usage Days"; Decimal)
        {
            Description = 'RED Lite Score';
            Caption = 'DMS Usage %';
            DataClassification = CustomerContent;
        }
        field(26; "Average OOS %"; Decimal)
        {
            Description = 'DMS N SFA';
            DataClassification = CustomerContent;
        }
        field(27; "Focus Brand Target"; Decimal)
        {
            Description = 'Redistrib. Veh. Run Days';
            DataClassification = CustomerContent;
        }
        field(28; "DMS Usage Rebate"; Decimal)
        {
            Description = 'RED Lite Rebate';
            DataClassification = CustomerContent;
        }
        field(29; "Average OOS Rebate"; Decimal)
        {
            Description = 'DMS N SFA Rebate';
            DataClassification = CustomerContent;
        }
        field(30; "Focus Brand Rebate"; Decimal)
        {
            Description = 'Redistrib. Veh. Run Rebate';
            DataClassification = CustomerContent;
        }
        field(31; "Total Rebate"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(32; "Credit Memo Reference"; Code[50])
        {
            DataClassification = CustomerContent;
        }
        field(33; "Credit Memo Created By"; Code[50])
        {
            DataClassification = CustomerContent;
        }
        field(34; "Global Dimension 1 Code"; Code[20])
        {
            CaptionClass = '1,1,1';
            Caption = 'Global Dimension 1 Code';
            TableRelation = "Dimension Value".Code WHERE("Global Dimension No." = CONST(1));
            DataClassification = CustomerContent;
        }
        field(35; "Focus Brand Achievment"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(36; "Focus Brand"; Option)
        {
            OptionCaption = 'Mixed KD,Evap Exclusive KD,JNSD Exclusive KD';//PKONKD
            OptionMembers = "Mixed KD","Evap Exclusive KD","JNSD Exclusive KD";//PKONKD
            DataClassification = CustomerContent;
        }
        field(37; "Flat Discount"; Decimal)//PKONKD
        {
            DataClassification = CustomerContent;
        }
        field(38; "Disc On Category A"; Decimal)//PKONKD
        {
            DataClassification = CustomerContent;
        }
        field(39; "Disc On Category B"; Decimal)//PKONKD
        {
            DataClassification = CustomerContent;
        }
        field(40; "Mixed Focus Brand Target(Evap)"; Decimal)//PKONKD
        {
            Caption = 'Mixed Focus Brand Target(Mid Size)';//BaluonJan20 2022
            DataClassification = CustomerContent;
        }
        field(41; "Evap Excl. 60G Target"; Decimal)//PKONKD
        {
            DataClassification = CustomerContent;
            //Caption = 'Evap Excl. 50G &120G ML Target';//CR220036-PKON22MA21
            Caption = '120/110 ML (A) Target.';//CR220036-PKON22MA21
        }
        field(42; "JNSD Capr Target"; Decimal)//PKONKD
        {
            DataClassification = CustomerContent;
            //Caption = 'JNSD Mid Size Target';
            //Caption = 'JNSD Capr Size Target';//CR220036-PKON22MA21
            Caption = 'Mid Size (A) Target';//CR220036-PKON22MA21
        }
        field(43; "Deduction On Qtr. Disc"; Decimal)//PKONKD
        {
            DataClassification = CustomerContent;
        }
        field(44; "Target For Category A"; Decimal)//PKONKD
        {
            DataClassification = CustomerContent;
        }
        field(45; "Target For Category B"; Decimal)//PKONKD
        {
            DataClassification = CustomerContent;
        }
        field(46; "Mixed Focus Brand Target(Capr)"; Decimal)//PKONKD
        {
            DataClassification = CustomerContent;
            caption = 'Mixed Focus Brand Target(All Can & Capri)';//PKONOC20
        }
        field(125; "YOGHURT 90ML & All Evap Target"; Decimal)//PKONKD
        {
            DataClassification = CustomerContent;
        }
        field(47; "Mixed Focus Brand Disc(Capr)"; Decimal)//PKONKD
        {
            DataClassification = CustomerContent;
            caption = 'All CANS & Capr Disc';//PKONOC20
        }
        field(123; "YOGHURT 90ML & All Evap Disc"; Decimal)//PKONKD
        {
            DataClassification = CustomerContent;
        }
        field(48; "Mixed Focus Brand Disc(Evap)"; Decimal)//PKONKD
        {
            DataClassification = CustomerContent;
            Caption = 'Mixed Focus Brand Disc(Mid Size)';
        }
        field(49; "Evap Excl. 60G Disc"; Decimal)//PKONKD
        {
            DataClassification = CustomerContent;
            //Caption = 'Evap Excl. 50G & 120G  ML Disc';//CR220036-PKON22MA21
            Caption = '120/110 ML (A) Disc.';//CR220036-PKON22MA21
        }
        field(52; "Evap Excl. 120G Target"; Decimal)//PKONKD
        {
            DataClassification = CustomerContent;
            //Caption = 'Evap Excl. 50G &120G SLIM Target';//CR220036-PKON22MA21
            Caption = '60/55 ML (B) Target';//CR220036-PKON22MA21
        }
        field(53; "Evap Excl. 120G Disc"; Decimal)//PKONKD
        {
            DataClassification = CustomerContent;
            //Caption = 'Evap Excl. 50G & 120G  SLIM Disc';//CR220036-PKON22MA21
            Caption = '60/55 ML (A) Disc';//CR220036-PKON22MA21
        }
        field(54; "JNSD Mid Target"; Decimal)//PKONKD
        {
            DataClassification = CustomerContent;
            //Caption = 'JNSD Capr Target';//Baluonjan20 2022
            //Caption = 'JNSD Mid Size Target';
            Caption = 'Can & Caprson & Fina (B) Target';//CR220036-PKON22MA21

        }
        field(126; "YOGHURT 90ML Target"; Decimal)//PKONKD
        {
            DataClassification = CustomerContent;

        }
        field(55; "JNSD Mid Disc"; Decimal)//PKONKD
        {
            DataClassification = CustomerContent;
            //Caption = 'JNSD all can capr disc';//Baluonjan20 2022
            //Caption = 'JNSD Mid Size Disc';//CR220036-PKON22MA21
            Caption = 'Can & Caprson & Fina (B) Disc';//CR220036-PKON22MA21
        }
        field(124; "YOGHURT 90ML Disc"; Decimal)//PKONKD
        {
            DataClassification = CustomerContent;
        }
        field(56; "JNSD Capr Disc"; Decimal)//PKONKD
        {
            DataClassification = CustomerContent;
            //Caption = 'JNSD Capr Size Disc';//CR220036-PKON22MA21
            Caption = 'Mid Size (A) Disc';//CR220036-PKON22MA21
        }
        field(57; "Achieve On Category A"; Decimal)//PKONKD
        {
            DecimalPlaces = 0 : 3;

            FieldClass = FlowField;
            Editable = false;
            CalcFormula = sum(KDRebateData."Amount Invoiced" where(Code = filter(2), "Rebate Period Code" = field("Target Period"), "Customer Code" = field("Customer No.")));
        }
        field(58; "Achieve On Category B"; Decimal)//PKONKD
        {
            DecimalPlaces = 0 : 3;

            FieldClass = FlowField;
            Editable = false;
            CalcFormula = sum(KDRebateData."Amount Invoiced" where(Code = filter(3), "Rebate Period Code" = field("Target Period"), "Customer Code" = field("Customer No.")));
        }
        field(59; "Achieve On Mixed Evap"; Decimal)//PKONKD
        {
            DecimalPlaces = 0 : 3;
            Caption = 'Achieve On MID- SIZE (ALL 180 ML , 315 & 500ML)';
            FieldClass = FlowField;
            Editable = false;
            CalcFormula = sum(KDRebateData."Amount Invoiced" where(Code = filter(4), "Rebate Period Code" = field("Target Period"), "Customer Code" = field("Customer No.")));
        }
        field(60; "Achieve On Mixed Capr"; Decimal)//PKONKD
        {
            DecimalPlaces = 0 : 3;
            Caption = 'Achieve On All CANS & Capr';//Baluonjan20 2022
            FieldClass = FlowField;
            Editable = false;
            CalcFormula = sum(KDRebateData."Amount Invoiced" where(Code = filter(5), "Rebate Period Code" = field("Target Period"), "Customer Code" = field("Customer No.")));
        }
        field(121; "Achi On YOGH 90ML & EVAP"; Decimal)//PKONKD
        {
            DecimalPlaces = 0 : 3;
            Caption = 'Achieve On YOGHURT 90ML & All EVAP';
            FieldClass = FlowField;
            Editable = false;
            CalcFormula = sum(KDRebateData."Amount Invoiced" where(Code = filter(21), "Rebate Period Code" = field("Target Period"), "Customer Code" = field("Customer No.")));
        }
        field(61; "Achieve On Evap 60G"; Decimal)//PKONKD
        {
            DecimalPlaces = 0 : 3;
            //Caption = 'Achieve On 50G &  120 Ml';//CR220036-PKON22MA21
            Caption = 'Achieve On 120/110 ML (A)';//CR220036-PKON22MA21
            FieldClass = FlowField;
            Editable = false;
            CalcFormula = sum(KDRebateData."Amount Invoiced" where(Code = filter(6), "Rebate Period Code" = field("Target Period"), "Customer Code" = field("Customer No.")));
        }

        field(62; "Achieve On Evap 120G"; Decimal)//PKONKD
        {
            DecimalPlaces = 0 : 3;
            //Caption = 'Achieve On  50G & 120G SLIM';//CR220036-PKON22MA21
            Caption = 'Achieve On 60/55 ML (B)';//CR220036-PKON22MA21
            FieldClass = FlowField;
            Editable = false;
            CalcFormula = sum(KDRebateData."Amount Invoiced" where(Code = filter(7), "Rebate Period Code" = field("Target Period"), "Customer Code" = field("Customer No.")));
        }

        //G2s >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>020224 Added field for Report
        field(133; "Achieve On ExclEvap 190Gfc"; Decimal)//PKONKD
        {
            DecimalPlaces = 0 : 3;
            //Caption = 'Achieve On  50G & 120G SLIM';//CR220036-PKON22MA21
            Caption = 'Achieve On ExclEvap 190gfc';//CR220036-PKON22MA21
            FieldClass = FlowField;
            Editable = false;
            CalcFormula = sum(KDRebateData."Amount Invoiced" where(Code = filter(7), "Rebate Period Code" = field("Target Period"), "Customer Code" = field("Customer No.")));
        }

        field(134; "Achieve On EvapJSND 190G"; Decimal)//PKONKD
        {
            DecimalPlaces = 0 : 3;
            //Caption = 'Achieve On  50G & 120G SLIM';//CR220036-PKON22MA21
            Caption = 'Achieve On EvapJSND 190g';//CR220036-PKON22MA21
            FieldClass = FlowField;
            Editable = false;
            CalcFormula = sum(KDRebateData."Amount Invoiced" where(Code = filter(7), "Rebate Period Code" = field("Target Period"), "Customer Code" = field("Customer No.")));
        }
        //G2s >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>020224 Added field for Report

        field(63; "Achieve On JSND Capr"; Decimal)//PKONKD
        {
            DecimalPlaces = 0 : 3;
            //Caption = 'Achieve On JNSD Mid Size';
            //Caption = 'Achieve On JNSD Capr Size';//CR220036-PKON22MA21
            Caption = 'Achieve On Mid Size (A)';//CR220036-PKON22MA21
            FieldClass = FlowField;
            Editable = false;
            CalcFormula = sum(KDRebateData."Amount Invoiced" where(Code = filter(8), "Rebate Period Code" = field("Target Period"), "Customer Code" = field("Customer No.")));//CR220036-PKON22MA21iod"), "Customer Code" = field("Customer No.")));
        }
        field(64; "Achieve On JSND Mid"; Decimal)//PKONKD
        {
            DecimalPlaces = 0 : 3;
            //Caption = 'Achieve On All CANS & Capr';//Baluonjan20 2022
            //Caption = 'Achie On Mid Size';//CR220036-PKON22MA21
            Caption = 'Achieve On Can & Caprson & Fina (B)';//CR220036-PKON22MA21
            FieldClass = FlowField;
            Editable = false;
            CalcFormula = sum(KDRebateData."Amount Invoiced" where(Code = filter(9), "Rebate Period Code" = field("Target Period"), "Customer Code" = field("Customer No.")));//CR220036-PKON22MA21
        }
        field(122; "Achieve On YOGHURT 90ML"; Decimal)//PKONKD
        {
            DecimalPlaces = 0 : 3;
            Caption = 'Achieve On YOGHURT 90ML';
            FieldClass = FlowField;
            Editable = false;
            CalcFormula = sum(KDRebateData."Amount Invoiced" where(Code = filter(22), "Rebate Period Code" = field("Target Period"), "Customer Code" = field("Customer No.")));
        }
        field(65; "Qtrly Achievement"; Decimal)//PKONKD
        {
            DecimalPlaces = 0 : 3;

            FieldClass = FlowField;
            Editable = false;
            CalcFormula = sum(KDRebateData."Amount Invoiced" where(Code = filter(10), "Rebate Period Code" = field("Target Period"), "Customer Code" = field("Customer No.")));
        }

        field(66; "d"; Decimal)//PKONKD
        {
            //DecimalPlaces = 0 : 3;
            DataClassification = CustomerContent;
        }
        field(67; "Evap & JSND JuiceTarget - 1Ltr"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(68; "Evap & JSND JuiceTarget - Cans"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(69; "Evap&JSND JuiTarget- Caprisun"; Decimal)
        {
            DataClassification = CustomerContent;
        }

        field(90; "Evap & JSND Juice Ach-1lit"; Decimal)
        {

            FieldClass = FlowField;
            Editable = false;
            CalcFormula = sum(KDRebateData."Amount Invoiced" where(Code = filter(31), "Rebate Period Code" = field("Target Period"), "Customer Code" = field("Customer No.")));
        }
        field(91; "Evap & JSND Juice Ach-cans"; Decimal)
        {

            FieldClass = FlowField;
            Editable = false;
            CalcFormula = sum(KDRebateData."Amount Invoiced" where(Code = filter(32), "Rebate Period Code" = field("Target Period"), "Customer Code" = field("Customer No.")));
        }
        field(92; "Evap & JSND Juice Ach caprisun"; Decimal)
        {

            FieldClass = FlowField;
            Editable = false;
            CalcFormula = sum(KDRebateData."Amount Invoiced" where(Code = filter(33), "Rebate Period Code" = field("Target Period"), "Customer Code" = field("Customer No.")));
        }
        field(71; "Evap & JSND Juice Dis-1Ltr"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(93; "Evap & JSND Juice Dis-cans"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(94; "Evap & JSND Juice Dis-caprisun"; Decimal)
        {
            DataClassification = CustomerContent;
        }


        field(73; "Evap&JSND Yoghurt Target-1lit"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(74; "Evap&JSND Yoghurt Target-90ml"; Decimal)
        {
            DataClassification = CustomerContent;
        }



        field(75; "Evap & JSND Yoghurt Ache-1 Lit"; Decimal)
        {
            FieldClass = FlowField;
            Editable = false;
            CalcFormula = sum(KDRebateData."Amount Invoiced" where(Code = filter(34), "Rebate Period Code" = field("Target Period"), "Customer Code" = field("Customer No.")));
        }
        field(95; "Evap & JSND Yoghurt Ache-90 ML"; Decimal)
        {
            FieldClass = FlowField;
            Editable = false;
            CalcFormula = sum(KDRebateData."Amount Invoiced" where(Code = filter(35), "Rebate Period Code" = field("Target Period"), "Customer Code" = field("Customer No.")));
        }
        field(76; "Evap & JSND Yoghurt Disc-1lit"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(96; "Evap & JSND Yoghurt Disc-90ml"; Decimal)
        {
            DataClassification = CustomerContent;
        }

        field(78; "Evap&JSND HollandiaTarget-120g"; Decimal)
        {
            DataClassification = CustomerContent;
        }



        field(79; "Evap&JSND Hollandia Ache-120g"; Decimal)
        {
            FieldClass = FlowField;
            Editable = false;
            CalcFormula = sum(KDRebateData."Amount Invoiced" where(Code = filter(36), "Rebate Period Code" = field("Target Period"), "Customer Code" = field("Customer No.")));
        }
        field(97; "Evap & JSND Hollan Disc-120g"; Decimal)
        {
            DataClassification = CustomerContent;
        }


        field(80; "Excl JSND Juice Targe 1-lit"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(98; "Excl JSND JuiceTarget -cans"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(99; "ExclJSNDJuiceTarget -capricun"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(81; "ExclJSND Juice Achiv-1lit"; Decimal)
        {
            FieldClass = FlowField;
            Editable = false;
            CalcFormula = sum(KDRebateData."Amount Invoiced" where(Code = filter(38), "Rebate Period Code" = field("Target Period"), "Customer Code" = field("Customer No.")));
        }
        field(100; "ExclJSNDJuice Achiv-cans"; Decimal)
        {
            FieldClass = FlowField;
            Editable = false;
            CalcFormula = sum(KDRebateData."Amount Invoiced" where(Code = filter(39), "Rebate Period Code" = field("Target Period"), "Customer Code" = field("Customer No.")));
        }
        field(101; "Excl JSND Juice Achie-caprisun"; Decimal)
        {
            FieldClass = FlowField;
            Editable = false;
            CalcFormula = sum(KDRebateData."Amount Invoiced" where(Code = filter(40), "Rebate Period Code" = field("Target Period"), "Customer Code" = field("Customer No.")));
        }
        field(82; "Excl JSND Juice Discount -1lit"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(102; "Excl JSND Juice Disc-cans"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(103; "Excl JSND Juice Disc-caprisun"; Decimal)
        {
            DataClassification = CustomerContent;
        }

        field(83; "Excl JSND Yoghurt Target-1lit"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(104; "Excl JSND Yoghurt Target-90ml"; Decimal)
        {
            DataClassification = CustomerContent;
        }


        field(84; "Excl JSND Yoghurt Achiv-1lit"; Decimal)
        {
            FieldClass = FlowField;
            Editable = false;
            CalcFormula = sum(KDRebateData."Amount Invoiced" where(Code = filter(41), "Rebate Period Code" = field("Target Period"), "Customer Code" = field("Customer No.")));
        }
        field(105; "Excl JSND Yoghurt Achiv-90ml"; Decimal)
        {
            FieldClass = FlowField;
            Editable = false;
            CalcFormula = sum(KDRebateData."Amount Invoiced" where(Code = filter(42), "Rebate Period Code" = field("Target Period"), "Customer Code" = field("Customer No.")));
        }

        field(85; "Excl JSND Yoghurt Disc-1lit"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(106; "Excl JSND Yoghurt Disc-90ml"; Decimal)
        {
            DataClassification = CustomerContent;
        }

        field(86; "ExclEVAP Holla Target-120g Fc"; Decimal)
        {
            DataClassification = CustomerContent;
        }

        //New ExclEvap 190g ColumnsAdded by G2S >>>>>>>>>>>>>>>>>>>>>290124
        field(119; "ExclEVAP Holla Target-190g Fc"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(120; "ExclEVAP Holla Disc-190gFc"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(129; "ExclEVAP Holl Achv -190gFc"; Decimal)
        {
            FieldClass = FlowField;
            Editable = false;
            CalcFormula = sum(KDRebateData."Amount Invoiced" where(Code = filter(43), "Rebate Period Code" = field("Target Period"), "Customer Code" = field("Customer No.")));
        }
        //>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>290124


        //New EvapJSND 190g ColumnsAdded by G2S >>>>>>>>>>>>>>>>>>>>>290124
        field(130; "EvapJSND Hollandia Target-190g"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(131; "EvapJSND Hollandia Disc-190g"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(132; "EvapJSND Hollandia Achiv-190g"; Decimal)
        {
            FieldClass = FlowField;
            Editable = false;
            CalcFormula = sum(KDRebateData."Amount Invoiced" where(Code = filter(37), "Rebate Period Code" = field("Target Period"), "Customer Code" = field("Customer No.")));
        }
        //>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>290124

        //New VanMOVETarget, DMSUsage & StockNorm ColumnsAdded by G2S >>>>>>>>>>>>>>>>>>>>>290124
        field(135; "Van Move Target"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(136; "Van Move disc"; Decimal)
        {
            DataClassification = CustomerContent;
        }

        field(137; "Van Move Achiev"; Decimal)
        {
            FieldClass = FlowField;
            Editable = false;
            CalcFormula = sum(KDRebateData."Amount Invoiced" where(Code = filter(42), "Rebate Period Code" = field("Target Period"), "Customer Code" = field("Customer No.")));
        }

        field(138; "DMS_UsageDays"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(139; "DMS_Usage Disc"; Decimal)
        {
            DataClassification = CustomerContent;
        }

        field(140; "DMS_UsageDays Achiev"; Decimal)
        {
            FieldClass = FlowField;
            Editable = false;
            CalcFormula = sum(KDRebateData."Amount Invoiced" where(Code = filter(42), "Rebate Period Code" = field("Target Period"), "Customer Code" = field("Customer No.")));
        }

        field(141; "StockNorm Target"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(142; "StockNorm Disc"; Decimal)
        {
            DataClassification = CustomerContent;
        }

        field(143; "StockNorm Achiev"; Decimal)
        {
            FieldClass = FlowField;
            Editable = false;
            CalcFormula = sum(KDRebateData."Amount Invoiced" where(Code = filter(42), "Rebate Period Code" = field("Target Period"), "Customer Code" = field("Customer No.")));
        }

        field(144; "Sec. SalesValue"; Decimal)
        {
            DataClassification = CustomerContent;
        }

        field(145; "Working Cap Target"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(146; "Working Cap Achieve"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(147; "Working Cap Disc"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        //Added new cans G2S 120224>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>
        // field(148; "Evp&JSND ActvZEST 330Can Targt"; Decimal)
        // {
        //     DataClassification = CustomerContent;
        // }
        // field(149; "Evp&JSND ActvZEST 330Can Disc"; Decimal)
        // {
        //     DataClassification = CustomerContent;
        // }
        // field(150; "Evp&JSND ActvZEST 330Can Achiv"; Decimal)
        // {

        //     FieldClass = FlowField;
        //     Editable = false;
        //     CalcFormula = sum(KDRebateData."Amount Invoiced" where(Code = filter(32), "Rebate Period Code" = field("Target Period"), "Customer Code" = field("Customer No.")));
        // }

        // field(151; "Evap&JSND Pincolda330 canTargt"; Decimal)
        // {
        //     DataClassification = CustomerContent;
        // }
        // field(152; "Evap&JSND Pincolda330 can Disc"; Decimal)
        // {
        //     DataClassification = CustomerContent;
        // }
        // field(153; "Evap&JSND Pincolda330 can Achv"; Decimal)
        // {

        //     FieldClass = FlowField;
        //     Editable = false;
        //     CalcFormula = sum(KDRebateData."Amount Invoiced" where(Code = filter(32), "Rebate Period Code" = field("Target Period"), "Customer Code" = field("Customer No.")));
        // }

        // field(151; "Evap&JSND IceTEALem330 canTargt"; Decimal)
        // {
        //     DataClassification = CustomerContent;
        // }
        // field(152; "Evap&JSND Pincolda330 can Disc"; Decimal)
        // {
        //     DataClassification = CustomerContent;
        // }
        // field(153; "Evap&JSND Pincolda330 can Achv"; Decimal)
        // {

        //     FieldClass = FlowField;
        //     Editable = false;
        //     CalcFormula = sum(KDRebateData."Amount Invoiced" where(Code = filter(32), "Rebate Period Code" = field("Target Period"), "Customer Code" = field("Customer No.")));
        // }

        //<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<90124


        field(107; "ExclEVAP Holla Target-120gslim"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(108; "ExclEVAP Holla Target-50g Fc"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(109; "ExclEVAP Holla Target-50gslim"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(110; "ExclEVAP Holl Achv -120gFc"; Decimal)
        {
            FieldClass = FlowField;
            Editable = false;
            CalcFormula = sum(KDRebateData."Amount Invoiced" where(Code = filter(44), "Rebate Period Code" = field("Target Period"), "Customer Code" = field("Customer No.")));
        }


        field(111; "ExclEVAP Holla Achiv-120gslim"; Decimal)
        {
            FieldClass = FlowField;
            Editable = false;
            CalcFormula = sum(KDRebateData."Amount Invoiced" where(Code = filter(45), "Rebate Period Code" = field("Target Period"), "Customer Code" = field("Customer No.")));
        }
        field(112; "ExclEVAP Holla Achiv-50Fc"; Decimal)
        {
            FieldClass = FlowField;
            Editable = false;
            CalcFormula = sum(KDRebateData."Amount Invoiced" where(Code = filter(46), "Rebate Period Code" = field("Target Period"), "Customer Code" = field("Customer No.")));
        }


        field(87; "ExclEVAP Holla Achiv-50gslim"; Decimal)
        {
            FieldClass = FlowField;
            Editable = false;
            CalcFormula = sum(KDRebateData."Amount Invoiced" where(Code = filter(47), "Rebate Period Code" = field("Target Period"), "Customer Code" = field("Customer No.")));
        }
        field(88; "Excl EVAP Hollan Disc-120gFc"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(113; "Excl EVAP Hollan Disc-120gslim"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(114; "Excl EVAP Hollan Disc-50gFc"; Decimal)
        {
            DataClassification = CustomerContent;

        }
        field(115; "Excl EVAP Hollan Disc-50gslim"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(116; "Qtrly Rebate Base Amnt"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(117; "Shrinakge Variance"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(118; "Shrinkage Base Amount"; Decimal)
        {
            FieldClass = FlowField;
            Editable = false;
            CalcFormula = sum(KDRebateData."Amount Invoiced with Out Vat" where(Code = filter(1), "Rebate Period Code" = field("Target Period"), "Customer Code" = field("Customer No.")));
        }

        //New ExclEvap UTHMILK ColumnsAdded by G2S >>>>>>>>>>>>>>>>>>>>>070525 CAS-01419-R1P2D2
        field(148; "ExclEVAP Holla Target-UHT MILK"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(149; "ExclEVAP Holla Disc-UHT MILK"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(150; "ExclEVAP Holl Achv -UHT MILK"; Decimal)
        {
            FieldClass = FlowField;
            Editable = false;
            CalcFormula = sum(KDRebateData."Amount Invoiced" where(Code = filter(48), "Rebate Period Code" = field("Target Period"), "Customer Code" = field("Customer No.")));
        }
        //>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>70525 G2S CAS-01419-R1P2D2


        //New EvapJSND UHTMILK ColumnsAdded by G2S >>>>>>>>>>>>>>>>>>>>>70525 G2S CAS-01419-R1P2D2
        field(151; "EvapJSND Holla Target-UHT"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(152; "EvapJSND Holla Disc-UHT"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(153; "EvapJSND Holla Achiv-UHT"; Decimal)
        {
            FieldClass = FlowField;
            Editable = false;
            CalcFormula = sum(KDRebateData."Amount Invoiced" where(Code = filter(49), "Rebate Period Code" = field("Target Period"), "Customer Code" = field("Customer No.")));
        }
        // >>>>>>>>>>>>>>>>>>>>>070525 G2S CAS-01419-R1P2D2
        //G2S>>>>>>>>>>>>>>>>#9593_CAS-01425-M8J6P2>>>>>>>>>>>>>>>>>>>>>>>>>>>>>030625 
        field(154; "EvapJSND Juice Target-315ml"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(155; "EvapJSND Juice Achiv-315ml"; Decimal)
        {
            FieldClass = FlowField;
            Editable = false;
            CalcFormula = sum(KDRebateData."Amount Invoiced" where(Code = filter(50), "Rebate Period Code" = field("Target Period"), "Customer Code" = field("Customer No.")));

        }
        field(156; "EvapJSND Juice Disc-315ml"; Decimal)
        {
            DataClassification = CustomerContent;
        }


        field(157; "EvapJSND Juice Target-Pet"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(158; "EvapJSND Juice Achiv-Pet"; Decimal)
        {
            FieldClass = FlowField;
            Editable = false;
            CalcFormula = sum(KDRebateData."Amount Invoiced" where(Code = filter(51), "Rebate Period Code" = field("Target Period"), "Customer Code" = field("Customer No.")));
        }
        field(159; "EvapJSND Juice Disc-Pet"; Decimal)
        {
            DataClassification = CustomerContent;
        }


        field(160; "EvapJSND Yoghurt Target-315ml"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(161; "EvapJSND Yoghurt Achiv-315ml"; Decimal)
        {
            FieldClass = FlowField;
            Editable = false;
            CalcFormula = sum(KDRebateData."Amount Invoiced" where(Code = filter(52), "Rebate Period Code" = field("Target Period"), "Customer Code" = field("Customer No.")));

        }
        field(162; "EvapJSND Yoghurt Disc-315ml"; Decimal)
        {
            DataClassification = CustomerContent;
        }
        //G2S>>>>>>>>>>>>>>>>#9593_CAS-01425-M8J6P2>>>>>>>>>>>>>>>>>>>>>>>>>>>>>030625 

    }

    keys
    {
        key(Key1; "Target Period", "Customer No.", "Resposibility Center")
        {
        }
        key(Key2; "Customer No.", "Monthly Big Target")
        {
        }
        key(Key3; "Customer No.")
        {
        }
    }

    fieldgroups
    {
    }

    var
        Item: Record Item;
        SNRsetup: Record "Sales & Receivables Setup";
        Rebateperiod: Record "Rebate Period Codes";
        Cust: Record Customer;
        ValueEntry: Record "Value Entry";
        CustomerDiscount: Record "Customer Sales Price/Discount";
}

