tableextension 50019 SalesLineTabExt91 extends "Sales Line"
{
    fields
    {


        modify("Location Code")
        {
            trigger OnAfterValidate()
            var
                LocationLrec: Record Location;
            begin
                //Balu 05122021 >>
                if LocationLrec.Get("Location Code") then
                    LocationLrec.TestField(Blocked, false);
                //Balu 05122021<<
            end;
        }
        modify(Quantity)
        {
            trigger OnBeforeValidate()
            var
                ItemLedgerLRec: record "Item Ledger Entry";
                SalesRcvble: Record "Sales & Receivables Setup";
                SalesInv: Record "Sales Line";
                AvilQty: Decimal;
            begin
                clear(AvilQty);
                SalesRcvble.Get();
                /*IF SalesRcvble."Preventive Negative Quantity" THEN BEGIN
                    if (Type = Type::Item) AND ("Document Type" = "Document Type"::Order) and (Quantity <> 0) then begin
                        ItemLedgerLRec.Reset();
                        ItemLedgerLRec.SetRange(Open, true);
                        ItemLedgerLRec.SetRange("Item No.", "No.");
                        ItemLedgerLRec.SetRange("Location Code", "Location Code");
                        if ItemLedgerLRec.FindSet() then begin
                            ItemLedgerLRec.CalcSums("Remaining Quantity");
                            AvilQty := ItemLedgerLRec."Remaining Quantity";
                        end;

                        SalesInv.reset;
                        SalesInv.setrange(Type, SalesInv.Type::Item);
                        SalesInv.SetRange("No.", "No.");
                        SalesInv.SetRange("Location Code", "Location Code");
                        SalesInv.SetFilter("Outstanding Qty. (Base)", '<>%1', 0);
                        IF SalesInv.FINDSET THEN begin
                            SalesInv.CalcSums("Outstanding Qty. (Base)");
                            AvilQty -= SalesInv."Outstanding Qty. (Base)";
                        end;

                        if "Quantity (Base)" > AvilQty then
                            error('Stock is Not Available for This Item. Avilable Qty is %1 and requirement is %2 ', AvilQty, "Quantity (Base)");
                    end;
                end;*/
            end;

            trigger OnAfterValidate()//PKONJ3
            var
                ItemLRec: Record item;
                SH: Record "Sales Header";
                currency: Record Currency;

            begin
                IF ItemLRec.Get("No.") then
                    "Wt. of the Qty Loading in Tons" := (Quantity * ItemLRec."Gross Weight" * "Qty. per Unit of Measure") / 1000;
                //Rebate Issues >>>>
                if sh.get("Document Type", "Document No.") then begin
                    if sh."Fixed Rebate" <> 0 then begin
                        validate("Fixed Rebate Code", sh."Fixed Rebate Code");
                        Validate("Fixed Rebate", sh."Fixed Rebate");
                        validate("Fixed Rebate Amount",
                        //Round("Line Amount" * (sh."Fixed Rebate" / 100), Currency."Amount Rounding Precision"));
                        Round((Quantity * "Unit Price") * (sh."Fixed Rebate" / 100), Currency."Amount Rounding Precision"));
                        //validate("Fixed Rebate Amount to Inv.", "Fixed Rebate Amount");


                    end;
                end;
                if ("Fixed Rebate Amount" <> 0) and (Quantity <> 0) then
                    Validate("Line Discount Amount", "Fixed Rebate Amount") else begin
                    "Line Discount Amount" := 0;
                    "Line Discount %" := 0;
                end;

                //rebate issuess <<<<
            end;

        }
        /*  modify("Inv. Disc. Amount to Invoice")
         {
             trigger OnAfterValidate()
             var
                 myInt: Integer;
             begin
                 "Inv. Disc. Amount to Invoice" += "Rebate Discount";
             end;
         } */
        modify("Qty. to Invoice")
        {
            trigger OnAfterValidate()
            var
                Currency: Record Currency;
            begin

                if "Qty. to Invoice" = 0 then BEGIN
                    validate("Fixed Rebate Amount to Inv.", 0); //"Fixed Rebate Amount");
                    validate("Rebate Disc. Amount to Inv.", 0); //"Rebate Discount");
                END else begin

                    if "Fixed Rebate Amount" <> 0 then begin
                        "Fixed Rebate Amount to Inv." :=
                         Round(
                        "Fixed Rebate Amount" * "Qty. to Invoice" / Quantity,
                        Currency."Amount Rounding Precision");
                        //Validate("Line Discount Amount", "Fixed Rebate Amount");
                    end;



                    if //(AmtafterRebate <> 0) AND
                     ("Fixed Rebate Amount to Inv." <> 0) then begin
                        Validate(AmtafterRebate, ROUND(((Quantity * "Unit Price")) * "Qty. to Invoice" / Quantity,
                     Currency."Amount Rounding Precision") - "Fixed Rebate Amount to Inv.");

                    end;
                    /* else
                        Validate(AmtafterRebate, ROUND(Amount * "Qty. to Invoice" / Quantity,
                     Currency."Amount Rounding Precision")); */
                    // Validate("Inv. Discount Amount", "Fixed Rebate Amount"); //disocunt usage 22/07/24
                    //disocunt usage 22/07/24


                    if "Rebate Discount" <> 0 then
                        CalcRebDiscToInvoice();
                end;
            end;
        }
        field(50000; "Order Status"; Enum PurcLineOrdrStatus)
        {
            DataClassification = CustomerContent;
        }
        field(50001; "Actual Order Qty."; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50005; "Gift Item Quantity"; Decimal)
        {
            DataClassification = CustomerContent;
            Editable = false;
        }
        field(50006; "Promo Qty. Util."; Decimal)
        {
            DataClassification = CustomerContent;
        }
        field(50004; "Gift Item"; Boolean)
        {
            DataClassification = CustomerContent;
            Editable = false;
        }
        field(50002; "Promo. No."; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "Promo Schedule"."No." WHERE(Status = CONST(Released));
        }
        field(50003; "Promo. Line No."; Integer)
        {
            DataClassification = CustomerContent;
            TableRelation = "Promo Schedule Line"."Line No." WHERE("Document No." = FIELD("Promo. No."), Active = CONST(true));
        }
        field(50012; "POS Window"; Boolean)
        {
            DataClassification = CustomerContent;
        }
        field(50008; "Shortcut Dimension 3 Code"; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "Dimension Value".Code WHERE("Global Dimension No." = CONST(3));
        }
        field(50009; "Promo. Offer Line No."; Integer)
        {
            DataClassification = CustomerContent;
            Editable = false;
        }
        field(50010; "GL Unit Price"; Decimal)
        {
            DataClassification = CustomerContent;
            trigger OnValidate()
            begin
                IF Type = Type::"G/L Account" THEN
                    VALIDATE("Unit Price", "GL Unit Price")
                ELSE
                    ERROR('Type must be G/L Account type');
            end;
        }
        field(50011; "Lot No."; Code[10])
        {
            DataClassification = CustomerContent;
            trigger OnLookup()
            begin
                OpenItemTrackingLines;
            end;
        }
        field(50013; "Cust. Discount Code"; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "Item Sales Disc. Qty."."No." WHERE(Status = CONST(Released));
        }
        field(50014; "Btach No."; code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50015; "Date of MAnufacture"; Date)
        {
            DataClassification = CustomerContent;
        }
        field(50016; "Batch Timing"; Date)
        {
            DataClassification = CustomerContent;
        }
        field(50017; "QA Reason Code"; code[20])
        {
            DataClassification = CustomerContent;
        }
        field(50018; "S Order No."; code[20])
        {
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Sales Shipment Line"."Order No." where("Document No." = field("Shipment No.")));
        }
        field(50019; "S Line No."; Integer)
        {
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Sales Shipment Line"."Order Line No." where("Document No." = field("Shipment No."), "Line No." = field("Shipment Line No.")));
        }
        field(50025; "Posted Loading Slip No."; code[20])
        {
            DataClassification = CustomerContent;
            //TableRelation = "Posted Loading Slip Line"."Document No." where("Customer No." = field("Sell-to Customer No."), "Item No." = field("No."));
            //b2bpksalecorr9 below trigger
            trigger OnValidate()
            var
                PSLS: Record "Posted Loading Slip Line";
                whrsrecptline: Record "Posted Gate Entry Line";
                PSinv: Record "Sales Invoice Line";
                PostNRGPOut: Record "Posted Gate Entry Line";
            begin
                //B2B Balu On April 22
                IF "Document Type" <> "Document Type"::"Credit Memo" THEN begin
                    //b2bpksalecorr9 below 
                    IF "Posted Loading Slip No." <> '' then begin
                        PostNRGPOut.Reset();
                        PostNRGPOut.SetRange("Source Type", PostNRGPOut."Source Type"::"Posted Loading Slip");
                        PostNRGPOut.SetRange("Source No.", "Posted Loading Slip No.");
                        IF Not PostNRGPOut.FindFirst() THEN
                            Error('Gate pass not posted for this Posted loading slip %1', "Posted Loading Slip No.");
                        PSinv.Reset();
                        PSinv.SetRange("Posted Loading Slip No.", "Posted Loading Slip No.");
                        PSinv.SetRange("Posted Loading Slip Line No.", "Posted Loading Slip Line No.");
                        PSinv.SetFilter(Quantity, '<>%1', 0);
                        IF PSinv.FindFirst() then
                            Error('Already Invoice is created for this Gate pass %1', PSinv."Document No.");
                        PSLS.Reset();
                        PSLS.SetRange("Document No.", "Posted Loading Slip No.");
                        PSLS.SetRange("Item No.", "No.");
                        if PSLS.FindFirst() then begin
                            whrsrecptline.Reset();
                            whrsrecptline.SetRange("Source Type", whrsrecptline."Source Type"::"Posted Loading Slip");
                            whrsrecptline.SetRange("Source No.", "Posted Loading Slip No.");//b2bpksalecorr10
                            if NOT whrsrecptline.FindFirst() then
                                Error('Warehouse shipment not posted for this Posted loading slip %1', "Posted Loading Slip No.");
                            //"Posted Loading Slip Line No." := PSLS."Line No.";
                            // Validate("Qty. to Invoice", PSLS."Qty. Loading");
                        end
                    end else begin
                        clear("Posted Loading Slip Line No.");
                        validate("Qty. to Invoice", 0);
                    end;
                end;
            end;

            trigger Onlookup()
            var
                PostedLoaddSlipLine: Record "Posted Loading Slip Line";
                WarehoseShpLin: record "Posted Whse. Shipment Line";
                PosSaleInv: Record "Sales Invoice Line";
            begin
                if "Document Type" <> "Document Type"::"Credit Memo" then begin
                    //b2bpksalecorr13
                    WarehoseShpLin.Reset();
                    WarehoseShpLin.SetRange("Source No.", "Document No.");
                    WarehoseShpLin.SetRange("Source Line No.", "Line No.");
                    if WarehoseShpLin.FindSet() then
                        repeat
                            // Message('All whr. Lines %1...%2', WarehoseShpLin."No.", WarehoseShpLin."Line No.");
                            PosSaleInv.Reset();
                            PosSaleInv.SetRange("Posted Loading Slip No.", WarehoseShpLin."Posted Loading Slip No.");
                            PosSaleInv.SetRange("Posted Loading Slip Line No.", WarehoseShpLin."Posted Loading Slip Line No.");
                            PosSaleInv.Setfilter(Quantity, '<>%1', 0);
                            IF Not PosSaleInv.FindFirst() then begin
                                WarehoseShpLin.Mark(true);
                                //Message('Not Invoiced whr. Lines %1...%2', WarehoseShpLin."No.", WarehoseShpLin."Line No.");
                            end;
                        until WarehoseShpLin.Next() = 0;
                    WarehoseShpLin.MarkedOnly(true);
                    if page.RunModal(0, WarehoseShpLin) = Action::LookupOK then begin
                        "Posted Loading Slip No." := WarehoseShpLin."Posted Loading Slip No.";
                        "Posted Loading Slip Line No." := WarehoseShpLin."Posted Loading Slip Line No.";
                        Validate("Posted Loading Slip No.");
                        Validate("Qty. to Invoice", WarehoseShpLin.Quantity);
                    end;
                end else begin
                    PostedLoaddSlipLine.Reset();
                    PostedLoaddSlipLine.SetRange("Customer No.", "Sell-to Customer No.");
                    if PostedLoaddSlipLine.FindFirst() then begin
                        if page.RunModal(0, PostedLoaddSlipLine) = Action::LookupOK then begin
                            Validate("Posted Loading Slip No.", PostedLoaddSlipLine."Document No.");
                            "Posted Loading Slip Line No." := PostedLoaddSlipLine."Line No.";
                        end;
                    end;
                end;
            end;
        }
        field(50026; "Posted Loading Slip Line No."; Integer)
        {
            DataClassification = CustomerContent;
            Editable = false;
            //b2bpksalecorr9 end

        }

        modify("No.")
        {
            trigger OnBeforeValidate()
            Var
                saleHdr: record "Sales Header";
            begin
                /* if NOT "Gift Item" then begin
                     SaleHdr.RESET;
                     SaleHdr.SetRange("Document Type", SaleHdr."Document Type"::Order);
                     SaleHdr.SetRange("No.", "Document No.");
                     SaleHdr.SetFilter("Quote No.", '<>%1', '');
                     IF SaleHdr.FindFirst() then
                         Error('You can not insert lines manually. This Order is created from sales quote %1', SaleHdr."Quote No.");
                 end;*/
            end;

            trigger OnAfterValidate()//PKONJ3
            var
                ItemLRec: Record item;
                SaleliLrec: Record "Sales Line";
            begin
                "No2." := "No.";
                IF ItemLRec.Get("No.") then
                    "Wt. of the Qty Loading in Tons" := (Quantity * ItemLRec."Gross Weight" * "Qty. per Unit of Measure") / 1000;
                "POS Window" := true;//FIX15Jun2021

                if ("Fixed Rebate Amount" <> 0) and (Quantity <> 0) then
                    Validate("Line Discount Amount", "Fixed Rebate Amount") else
                    Validate(quantity, 0);
                //>>>> G2S 08/04/2024 CAS-01414-Q6Q7Q8
                IF REC."Document Type" = Rec."Document Type"::"Return Order" THEN BEGIN
                    IF REC.Type = Rec.Type::Item THEN
                        Rec."Description 2" := Rec.Description;
                END;
                //>>>> G2S 08/04/2024 CAS-01414-Q6Q7Q8

            end;
        }
        field(50030; "Item Category"; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = "Item Category".Code;
            //b2bpksalecorr12
            trigger OnValidate()
            var
                myInt: Integer;
            begin
                TestField(Type, Type::Item);
            end;
        }
        field(50031; "Category Wise Items"; Code[20])
        {
            DataClassification = CustomerContent;
            TableRelation = Item."No." where("Item Category Code" = field("Item Category"), blocked = filter(false), "Approval Status" = const(Released));
            //b2bpksalecorr12
            trigger OnValidate()
            var
                myInt: Integer;
                ItemCatg: Code[50];
                ItemCatgWite: Code[50];
                shrec: Record "Sales Header";
                currency: Record Currency;
            begin
                ItemCatg := "Item Category";
                ItemCatgWite := "Category Wise Items";
                TestField(Type, Type::Item);
                Validate("No.", "Category Wise Items");
                "Item Category" := ItemCatg;
                "Category Wise Items" := ItemCatgWite;
                //Rebate Issues >>>>
                if shrec.get("Document Type", "Document No.") then begin
                    if shrec."Fixed Rebate" <> 0 then begin
                        validate("Fixed Rebate Code", shrec."Fixed Rebate Code");
                        Validate("Fixed Rebate", shrec."Fixed Rebate");
                        validate("Fixed Rebate Amount", Round((quantity * "unit price") * (shrec."Fixed Rebate" / 100), Currency."Amount Rounding Precision"));
                        //validate("Fixed Rebate Amount to Inv.", "Fixed Rebate Amount");
                    end;
                end;
                if ("Fixed Rebate Amount" <> 0) and (Quantity <> 0) then
                    Validate("Line Discount Amount", "Fixed Rebate Amount") else
                    Validate(Quantity, 0);
                //rebate issuess <<<<
            end;
        }
        field(65004; "Assorted Mix"; Boolean)
        {
            Description = 'GJ_CHIPOS_RKD_141013';
        }
        field(65005; "Assorted Quantity"; Decimal)
        {
            Description = 'GJ_CHIPOS_RKD_141013';

            trigger OnValidate();
            var
                IUOM: Record "Item Unit of Measure";
            begin
                if "Assorted Mix" then begin
                    IUOM.RESET;
                    IUOM.SETRANGE("Item No.", "No.");
                    //IUOM.SETRANGE("Mix Lot UOM (base)", true);
                    if IUOM.FINDFIRST then
                        VALIDATE(Quantity, ("Assorted Quantity" * IUOM."Qty. per Unit of Measure"));
                    VALIDATE("Unit Price", "Unit Price");
                end else
                    ERROR('Assorted Mix should be true');
            end;
        }
        field(65006; "Retail Product Code"; Code[20])
        {
            Description = 'GJ_CHIPOS_RKD_141013';
            // TableRelation = "Item Category".Code WHERE(Code = FIELD("Item Category Code"));
            TableRelation = Item."No." where("Item Category Code" = const('FG')); //Commneted to avoid Base Error"Product group"  
        }
        field(65007; "Loc. wise Inventory"; Decimal)
        {
            CalcFormula = Sum("Item Ledger Entry"."Remaining Quantity" WHERE("Item No." = FIELD("No."),
                                                                              "Location Code" = FIELD("Location Code")));
            Description = 'GJ_CHIPOS_RKD_141013';
            Editable = false;
            FieldClass = FlowField;
        }
        field(65008; "No2."; Code[20])
        {
            TableRelation = Item."No." where("Item Category Code" = const('FG'));
            trigger Onvalidate()
            begin
                Validate("No.", "No2.");
            end;
        }
        field(65009; "Wt. of the Qty Loading in Tons"; Decimal)//PKONJ3
        {
            Editable = false;
            Caption = 'Wt. Of the Quantity In Tons';
        }

        field(60010; "Rebate Discount"; Decimal)
        {
            DataClassification = CustomerContent;
            Editable = false;
            //G2s Added 220224
            //rebate issue
            trigger OnValidate()
            var
                myInt: Integer;
                currency: Record currency;
            begin
                if ("Rebate Discount" <> 0) then begin
                    if AmtafterRebate <> 0 then
                        Validate(AmtafterRebate, (AmtafterRebate - "Rebate Discount")) else
                        // Validate(AmtafterRebate, (amount - "Rebate Discount"));
                        if Amount <> 0 then
                            Validate(AmtafterRebate, (amount - "Rebate Discount"));
                    Validate("Line Discount Amount", "Line Discount Amount" + "Rebate Discount"); //disocunt usage 22/07/24
                end; //else 
            end;

        }
        //rebate issue 10/05/24 >>>
        field(60011; "Rebate Disc. Amount to Inv."; Decimal)
        {
            DataClassification = CustomerContent;
            AutoFormatExpression = "Currency Code";
            AutoFormatType = 1;
            Caption = 'Rebate Disc. Amount to Inv.';
            Editable = false;
            trigger OnValidate()
            var
                myInt: Integer;
                currency: Record currency;
            begin
                if ("Rebate Disc. Amount to Inv." <> 0) then begin
                    if AmtafterRebate <> 0 then
                        Validate(AmtafterRebate, (AmtafterRebate - "Rebate Disc. Amount to Inv.")) else
                        // Validate(AmtafterRebate, (amount - "Rebate Discount"));
                        Validate(AmtafterRebate, (amount - "Rebate Disc. Amount to Inv."))
                end; //else 
            end;


        }
        field(60012; "Fixed Rebate Code"; Code[10])
        {
            DataClassification = CustomerContent;

            Caption = 'Fixed Rebate Code';
            Editable = false;
            trigger OnValidate()
            var
                myInt: Integer;
            begin

            end;
        }
        field(60013; "Fixed Rebate"; Decimal)
        {
            DataClassification = CustomerContent;

            Caption = 'Fixed Rebate';
            Editable = false;

        }
        field(60014; "Fixed Rebate Amount"; Decimal)
        {
            DataClassification = CustomerContent;

            Caption = 'Fixed Rebate Amount';
            Editable = false;
            trigger OnValidate()
            var
                myInt: Integer;
                currency: Record currency;
            begin


                if ("Fixed Rebate Amount" <> 0) then begin
                    Validate(AmtafterRebate, ((Quantity * "Unit Price") - "Fixed Rebate Amount"))

                end else
                    Validate(AmtafterRebate, 0);
                //disocunt usage 22/07/24
                // if "VAT %" <> 0 then
                //  Validate(AmtafterRebateIncVAT, (round(AmtafterRebate + (AmtafterRebate * ("VAT %" / 100)), currency."Amount Rounding Precision")));
                // end;
            end;


        }
        field(60015; "Fixed Rebate Amount to Inv."; Decimal)
        {
            DataClassification = CustomerContent;
            AutoFormatExpression = "Currency Code";
            AutoFormatType = 1;
            Caption = 'Fixed Rebate Amount to Inv.';
            Editable = false;
            trigger OnValidate()
            var
                myInt: Integer;
                currency: Record currency;
            begin


                if ("Fixed Rebate Amount to Inv." <> 0) then //begin
                    Validate(AmtafterRebate, ((Quantity * "Unit Price") - "Fixed Rebate Amount to Inv.")) else
                    Validate(AmtafterRebate, 0);
                // if "VAT %" <> 0 then
                //  Validate(AmtafterRebateIncVAT, (round(AmtafterRebate + (AmtafterRebate * ("VAT %" / 100)), currency."Amount Rounding Precision")));
                // end;
            end;

        }
        field(60016; "AmtafterRebate"; Decimal)
        {
            DataClassification = CustomerContent;
            AutoFormatExpression = "Currency Code";
            AutoFormatType = 1;
            Caption = 'Amount after Rebate';
            Editable = false;
            trigger OnValidate()
            var
                myInt: Integer;
                currency: Record currency;
            begin
                if ("VAT %" <> 0) and (AmtafterRebate <> 0) then
                    Validate(AmtafterRebateIncVAT, (round(AmtafterRebate + (AmtafterRebate * ("VAT %" / 100)), currency."Amount Rounding Precision"))) else
                    Validate(AmtafterRebateIncVAT, AmtafterRebate);
            end;

        }
        field(60017; "AmtafterRebateIncVAT"; Decimal)
        {
            DataClassification = CustomerContent;
            AutoFormatExpression = "Currency Code";
            AutoFormatType = 1;
            Caption = 'Amount after Rebate Inc. VAT';
            Editable = false;
        }
        // <<<


    }
    procedure SelectGiftItemFlavour()
    var
        PromoSchItemGifts: record "Promo. Schedule Gift Items";
        //PromoSchItemGiftsForm: record "Gift Item Variant Selections";
        ItemGiftQuantity: Decimal;
        GiftItem: boolean;
        PromoQtyUtil: Decimal;
        Text50000: label 'This is not a Gift Item Line, please select a line with a gift item.';
        GenBPGroup: Code[20];
    begin
        IF NOT "Gift Item" THEN
            ERROR(Text50000);

        ItemGiftQuantity := 0;
        //GiftItem:=FALSE;
        PromoQtyUtil := 0;
        CLEAR(GenBPGroup);
        GenBPGroup := "Gen. Bus. Posting Group";
        ItemGiftQuantity := "Gift Item Quantity";
        GiftItem := "Gift Item";
        PromoQtyUtil := "Promo Qty. Util.";
        SuspendStatusCheck := TRUE;
        PromoSchItemGifts.RESET;
        PromoSchItemGifts.SETRANGE("Promo. Schd. Document No.", "Promo. No.");
        PromoSchItemGifts.SETRANGE("Promo. Schd. Line No.", "Promo. Line No.");
        IF PAGE.RUNMODAL(PAGE::"Gift Item Variants", PromoSchItemGifts, PromoSchItemGifts."Gift Item No.") =
          Action::LookupOK THEN BEGIN
            VALIDATE("No.", PromoSchItemGifts."Gift Item No.");
            VALIDATE("Unit Price", 0);
            "Promo. No." := PromoSchItemGifts."Promo. Schd. Document No.";
            "Promo. Line No." := PromoSchItemGifts."Promo. Schd. Line No.";
            VALIDATE("Gift Item Quantity", ItemGiftQuantity);
            VALIDATE(Quantity, ItemGiftQuantity);
            VALIDATE("Gift Item", TRUE);
            VALIDATE("Promo Qty. Util.", PromoQtyUtil);
            VALIDATE("Promo. Offer Line No.", PromoSchItemGifts."Line No.");
            VALIDATE(Quantity);
            VALIDATE("Gen. Bus. Posting Group", GenBPGroup);
            COMMIT;
        END;
    end;

    procedure SelectGiftItemFlavourLines(var SalesLine: Record "Sales Line") //PKONAU3
    var
        PromoSchItemGifts: record "Promo. Schedule Gift Items";
        //PromoSchItemGiftsForm: record "Gift Item Variant Selections";
        ItemGiftQuantity: Decimal;
        GiftItem: boolean;
        PromoQtyUtil: Decimal;
        Text50000: label 'This is not a Gift Item Line, please select a line with a gift item.';
        GenBPGroup: Code[20];
        Valid: Codeunit Validations;
    begin
        IF NOT SalesLine."Gift Item" THEN
            ERROR(Text50000);
        Commit();//PKONAU4
        ItemGiftQuantity := 0;
        //GiftItem:=FALSE;
        PromoQtyUtil := 0;
        CLEAR(GenBPGroup);
        GenBPGroup := SalesLine."Gen. Bus. Posting Group";
        ItemGiftQuantity := SalesLine."Gift Item Quantity";
        GiftItem := SalesLine."Gift Item";
        PromoQtyUtil := SalesLine."Promo Qty. Util.";
        SuspendStatusCheck := TRUE;
        PromoSchItemGifts.RESET;
        PromoSchItemGifts.SETRANGE("Promo. Schd. Document No.", SalesLine."Promo. No.");
        PromoSchItemGifts.SETRANGE("Promo. Schd. Line No.", SalesLine."Promo. Line No.");
        IF PAGE.RUNMODAL(PAGE::"Gift Item Variants", PromoSchItemGifts, PromoSchItemGifts."Gift Item No.") =
          Action::LookupOK THEN BEGIN
            SalesLine.VALIDATE("No.", PromoSchItemGifts."Gift Item No.");
            SalesLine.VALIDATE("Unit Price", 0);
            SalesLine."Promo. No." := PromoSchItemGifts."Promo. Schd. Document No.";
            SalesLine."Promo. Line No." := PromoSchItemGifts."Promo. Schd. Line No.";
            SalesLine.VALIDATE("Gift Item Quantity", ItemGiftQuantity);
            SalesLine.VALIDATE(Quantity, ItemGiftQuantity);
            SalesLine.VALIDATE("Gift Item", TRUE);
            SalesLine.VALIDATE("Promo Qty. Util.", PromoQtyUtil);
            SalesLine.VALIDATE("Promo. Offer Line No.", PromoSchItemGifts."Line No.");
            SalesLine.VALIDATE(Quantity);
            SalesLine.VALIDATE("Gen. Bus. Posting Group", GenBPGroup);
            IF Valid.CheckQtyValidationsTrueOrFalse(SalesLine) THEN
                error('There is no sufficient stock for the selected item. Pls recalculate and select different Gift Item.')
            //SalesLine.Modify();//PKONAU4
            //COMMIT;//PKONAU4
        END;
    end;

    procedure CancelSalesOrderLine()
    var
        SaleHeaderLRec: Record "Sales Header";
        CompletelyCancelled: Boolean;
        SalesLineLRec: record "Sales Line";
        ArchiveManagement: Codeunit ArchiveManagement;
        Text50201: Label 'There is nothing to cancel.';
        Text50202: Label 'Do you want to cancel the sales order line?';
        Text50203: Label 'This sales order Line has been already Shipped, you can only Short Close the sales line.';
    begin
        IF NOT ("Order Status" = "Order Status"::Cancelled) THEN BEGIN
            IF ("No." = '') OR (Quantity = 0) THEN
                ERROR(Text50201);

            IF "Quantity Shipped" = 0 THEN BEGIN
                IF NOT CONFIRM(Text50202, FALSE) THEN
                    EXIT;
                SaleHeaderLRec.GET("Document Type", "Document No.");
                ArchiveManagement.ArchSalesDocumentNoConfirm(SaleHeaderLRec);
                "Actual Order Qty." := Quantity;
                VALIDATE(Quantity, 0);
                "Order Status" := "Order Status"::Cancelled;
                MODIFY;

                CLEAR(CompletelyCancelled);
                SalesLineLRec.RESET;
                SalesLineLRec.SETRANGE("Document Type", "Document Type");
                SalesLineLRec.SETRANGE("Document No.", "Document No.");
                SalesLineLRec.SETFILTER("Order Status", '=%1', "Order Status"::" ");
                IF NOT SalesLineLRec.FINDFIRST THEN
                    CompletelyCancelled := TRUE;

                IF NOT CompletelyCancelled THEN
                    SaleHeaderLRec."Order Status" := SaleHeaderLRec."Order Status"::"Partially Cancelled"
                ELSE
                    SaleHeaderLRec."Order Status" := SaleHeaderLRec."Order Status"::Cancelled;

                SaleHeaderLRec.MODIFY;

            END ELSE
                ERROR(Text50203);
        END;
    end;

    procedure ShortCloseSalesOrderLine()
    var
        SaleHeaderLRec: Record "Sales Header";
        CompletelyShortclosed: Boolean;
        SalesLineLRec: Record "Sales Line";
        ArchiveManagement: Codeunit ArchiveManagement;
        Text50204: Label 'There is nothing to Short Close.';
        Text50205: Label 'Do you want to short close the sales order line?';
        Text50206: Label 'You can only short close sales line with shipped quantity.';
    begin
        IF NOT ("Order Status" = "Order Status"::"Short Closed") THEN BEGIN
            IF ("No." = '') THEN
                ERROR(Text50204);

            IF "Quantity Shipped" <> 0 THEN BEGIN
                IF NOT CONFIRM(Text50205, FALSE) THEN
                    EXIT;
                SaleHeaderLRec.GET("Document Type", "Document No.");
                ArchiveManagement.ArchSalesDocumentNoConfirm(SaleHeaderLRec);
                "Actual Order Qty." := Quantity;
                VALIDATE(Quantity, "Quantity Shipped");
                "Order Status" := "Order Status"::"Short Closed";
                MODIFY;

                CLEAR(CompletelyShortclosed);
                SalesLineLRec.RESET;
                SalesLineLRec.SETRANGE("Document Type", "Document Type");
                SalesLineLRec.SETRANGE("Document No.", "Document No.");
                SalesLineLRec.SETFILTER("Order Status", '=%1', "Order Status"::" ");
                IF NOT SalesLineLRec.FINDFIRST THEN
                    CompletelyShortclosed := TRUE;

                IF NOT CompletelyShortclosed THEN
                    SaleHeaderLRec."Order Status" := SaleHeaderLRec."Order Status"::"Partially Short Closed"
                ELSE
                    SaleHeaderLRec."Order Status" := SaleHeaderLRec."Order Status"::"Short Closed";

                SaleHeaderLRec.MODIFY;
            END ELSE
                ERROR(Text50206);
        END;
    end;

    // Trigger OnBeforeDelete() // >>>>>>G2S 14/01/25 CAS-01388-C9P4S5
    // BEGIN
    //     IF REC."Document Type" = Rec."Document Type"::Order THEN
    //         ERROR('This Line cannot be deleted');
    // END; //>>>>>G2S 14/01/25 CAS-01388-C9P4S5

    trigger OnBeforeDelete()
    var
        salesLine: Record "Sales Line";
    begin
        salesLine.SetRange("Document No.", rec."Document No.");
        if salesLine.FindFirst() then begin
            if salesLine.RebateVariableLineexits() then
                salesline.ReturnRebateLineBalance();
        end;
    end;

    trigger OnInsert()
    var
        SaleHdr: Record "Sales Header";
    begin
        /*
        if NOT "Gift Item" then begin
            SaleHdr.RESET;
            SaleHdr.SetRange("Document Type", SaleHdr."Document Type"::Order);
            SaleHdr.SetRange("No.", "Document No.");
            SaleHdr.SetFilter("Quote No.", '<>%1', '');
            IF SaleHdr.FindFirst() then
                Error('You can not insert lines manually. This Order is created from sales quote %1', SaleHdr."Quote No.");
        end;*/
    end;

    procedure SetItemTracking()
    var
        SalesLine: Record "Sales Line";
        Item: Record Item;
        ReservRec: Record "Reservation Entry";
        QtyVal: Decimal;
        ILERem: Decimal;
        ILERec: Record "Item Ledger Entry";
        ILERec2: Record "Item Ledger Entry";
        TILERem: Decimal;
        ReservQtyVal: Decimal;
        ReservRec2: Record "Reservation Entry";
    begin

        IF Item.GET("No.") THEN
            IF Item."Item Tracking Code" <> '' THEN BEGIN
                //GJ_CHIPOS_RKD_121013 >>>
                IF "POS Window" THEN BEGIN
                    //IF Quantity <> xRec.Quantity THEN BEGIN
                    ReservRec.RESET;
                    ReservRec.SETRANGE("Item No.", "No.");
                    ReservRec.SETRANGE("Source Type", 37);
                    ReservRec.SETRANGE("Source Subtype", "Document Type");
                    ReservRec.SETRANGE("Source ID", "Document No.");
                    ReservRec.SETRANGE("Source Ref. No.", "Line No.");
                    IF ReservRec.FINDFIRST THEN
                        REPEAT
                            ReservRec.DELETE;
                        UNTIL ReservRec.NEXT = 0;
                    //END;
                END;
                CLEAR(QtyVal);
                //GJ_CHIPOS_RKD_121013 <<<
                //GJ_CHIPOS_RKD_121013 <<<

                QtyVal := Quantity * "Qty. per Unit of Measure";
                IF "POS Window" THEN BEGIN
                    IF ((Quantity <> 0)) THEN BEGIN

                        CLEAR(ILERem);
                        ILERec.RESET;
                        ILERec.SETCURRENTKEY("Expiration Date");
                        ILERec.SETRANGE("Item No.", "No.");
                        ILERec.SETRANGE("Location Code", "Location Code");
                        ILERec.SETFILTER("Remaining Quantity", '<>%1', 0);
                        ILERec.SETRANGE(Open, TRUE);
                        ILERec.SETRANGE(Positive, TRUE);
                        ILERec.SETFILTER("Lot No.", '<>%1', '');
                        IF ILERec.FINDFIRST THEN BEGIN
                            REPEAT
                                //IF CheckIfQCApproved(ILERec."Lot No.") THEN BEGIN //Check for approved lots
                                CLEAR(TILERem);
                                ILERec2.RESET;
                                ILERec2.SETRANGE("Item No.", "No.");
                                ILERec2.SETRANGE("Location Code", "Location Code");
                                ILERec2.SETFILTER("Remaining Quantity", '<>%1', 0);
                                ILERec2.SETRANGE(Open, TRUE);
                                ILERec2.SETRANGE(Positive, TRUE);
                                ILERec2.SETFILTER("Lot No.", ILERec."Lot No.");
                                IF ILERec2.FINDFIRST THEN
                                    REPEAT
                                        TILERem += ILERec2."Remaining Quantity";
                                    UNTIL ILERec2.NEXT = 0;
                                ILERec.CALCFIELDS("Reserved Quantity");
                                IF TILERem > ILERec."Reserved Quantity" THEN BEGIN
                                    //ILERem := ILERec."Remaining Quantity";
                                    IF QtyVal > 0 THEN BEGIN
                                        /*
                                        CLEAR(ReservQtyVal);
                                        ReservRec2.RESET;
                                        ReservRec2.SETRANGE("Item No.", "No.");
                                        ReservRec2.SETRANGE("Location Code", "Location Code");
                                        ReservRec2.SETFILTER("Quantity (Base)", '<%1', 0);
                                        ReservRec2.SETRANGE("Lot No.", ILERec."Lot No.");
                                        IF ReservRec2.FINDFIRST THEN
                                            REPEAT
                                                ReservQtyVal := ABS(ReservRec2."Quantity (Base)");
                                                IF ReservQtyVal >= 0 THEN BEGIN}*/
                                        IF (TILERem - ILERec."Reserved Quantity") > 0 THEN BEGIN
                                            //IF (ILERec."Remaining Quantity" - ReservQtyVal) >0 THEN BEGIN
                                            //ILERec.NEXT
                                            //ELSE BEGIN
                                            //IF (ILERec."Remaining Quantity" - ReservQtyVal) < QtyVal THEN
                                            IF (TILERem - ILERec."Reserved Quantity") < QtyVal THEN BEGIN
                                                /*{
                                                  IF USERID = 'gaurav' THEN
                                                                                         MESSAGE('Qty UOM LOOP 1 := %1, Act Qty := %2', "Qty. per Unit of Measure", (QtyVal * "Qty. per Unit of Measure"));
                                                  }*/
                                                ReservRec.INIT;
                                                ReservRec2.RESET;
                                                IF ReservRec2.FINDLAST THEN;
                                                ReservRec.VALIDATE("Entry No.", ReservRec2."Entry No." + 1);
                                                ReservRec.VALIDATE(Positive, FALSE);
                                                ReservRec.VALIDATE("Item No.", "No.");
                                                ReservRec.VALIDATE("Location Code", "Location Code");
                                                ReservRec.VALIDATE("Quantity (Base)", -((TILERem - ILERec."Reserved Quantity")));
                                                ReservRec.VALIDATE("Reservation Status", ReservRec."Reservation Status"::Prospect);
                                                ReservRec.VALIDATE("Creation Date", TODAY);
                                                ReservRec.VALIDATE("Source Type", 37);
                                                ReservRec.VALIDATE("Source Subtype", "Document Type");
                                                ReservRec.VALIDATE("Source ID", "Document No.");
                                                ReservRec.VALIDATE("Source Ref. No.", "Line No.");
                                                ReservRec.VALIDATE("Shipment Date", TODAY);
                                                ReservRec.VALIDATE("Created By", USERID);
                                                ReservRec.VALIDATE("Qty. per Unit of Measure", "Qty. per Unit of Measure");
                                                ReservRec.VALIDATE(Quantity, -((TILERem - ILERec."Reserved Quantity")));
                                                ReservRec.VALIDATE("Planning Flexibility", ReservRec."Planning Flexibility"::Unlimited);
                                                ReservRec.VALIDATE("Qty. to Handle (Base)", -((TILERem - ILERec."Reserved Quantity")));
                                                ReservRec.VALIDATE("Qty. to Invoice (Base)", -((TILERem - ILERec."Reserved Quantity")));
                                                ReservRec.VALIDATE("Lot No.", ILERec."Lot No.");
                                                ReservRec.VALIDATE("Item Tracking", ReservRec."Item Tracking"::"Lot No.");
                                                ReservRec.INSERT;
                                                QtyVal := QtyVal - ((TILERem - ILERec."Reserved Quantity"));
                                            END ELSE
                                                //IF (ILERec."Remaining Quantity" - ReservQtyVal) > QtyVal THEN BEGIN
                                                IF (TILERem - ILERec."Reserved Quantity") >= QtyVal THEN BEGIN
                                                    /*
                                                      IF USERID = 'gaurav' THEN
                                                                             MESSAGE('Qty UOM LOOP 2 := %1, Act Qty := %2 , Rem := %3, QtyVal := %4', "Qty. per Unit of Measure",
                                                                             (QtyVal * "Qty. per Unit of Measure"),
                                                                             (ILERec."Remaining Quantity" - ReservQtyVal), QtyVal);
                                                      */
                                                    ReservRec.INIT;
                                                    ReservRec2.RESET;
                                                    IF ReservRec2.FINDLAST THEN;
                                                    ReservRec.VALIDATE("Entry No.", ReservRec2."Entry No." + 1);
                                                    ReservRec.VALIDATE(Positive, FALSE);
                                                    ReservRec.VALIDATE("Item No.", "No.");
                                                    ReservRec.VALIDATE("Location Code", "Location Code");
                                                    ReservRec.VALIDATE("Quantity (Base)", -(QtyVal));
                                                    ReservRec.VALIDATE("Reservation Status", ReservRec."Reservation Status"::Prospect);
                                                    ReservRec.VALIDATE("Creation Date", TODAY);
                                                    ReservRec.VALIDATE("Source Type", 37);
                                                    ReservRec.VALIDATE("Source Subtype", "Document Type");
                                                    ReservRec.VALIDATE("Source ID", "Document No.");
                                                    ReservRec.VALIDATE("Source Ref. No.", "Line No.");
                                                    ReservRec.VALIDATE("Shipment Date", TODAY);
                                                    ReservRec.VALIDATE("Created By", USERID);
                                                    ReservRec.VALIDATE("Qty. per Unit of Measure", "Qty. per Unit of Measure");
                                                    ReservRec.VALIDATE(Quantity, -(QtyVal));
                                                    ReservRec.VALIDATE("Planning Flexibility", ReservRec."Planning Flexibility"::Unlimited);
                                                    ReservRec.VALIDATE("Qty. to Handle (Base)", -(QtyVal));
                                                    ReservRec.VALIDATE("Qty. to Invoice (Base)", -(QtyVal));
                                                    ReservRec.VALIDATE("Lot No.", ILERec."Lot No.");
                                                    ReservRec.VALIDATE("Item Tracking", ReservRec."Item Tracking"::"Lot No.");
                                                    ReservRec.INSERT;
                                                    QtyVal := 0;
                                                END;
                                        END;
                                    END ELSE BEGIN
                                        //

                                        IF QtyVal >= TILERem THEN BEGIN
                                            ILERec.NEXT;
                                            /*{
                                            IF USERID = 'gaurav' THEN
                                            MESSAGE('Qty UOM LOOP 3 := %1, Act Qty := %2',"Qty. per Unit of Measure",(QtyVal*"Qty. per Unit of Measure"));
                                            }*/
                                            ReservRec.INIT;
                                            ReservRec2.RESET;
                                            IF ReservRec2.FINDLAST THEN;
                                            ReservRec.VALIDATE("Entry No.", ReservRec2."Entry No." + 1);
                                            ReservRec.VALIDATE(Positive, FALSE);
                                            ReservRec.VALIDATE("Item No.", "No.");
                                            ReservRec.VALIDATE("Location Code", "Location Code");
                                            ReservRec.VALIDATE("Quantity (Base)", -(TILERem * "Qty. per Unit of Measure"));
                                            ReservRec.VALIDATE("Reservation Status", ReservRec."Reservation Status"::Prospect);
                                            ReservRec.VALIDATE("Creation Date", TODAY);
                                            ReservRec.VALIDATE("Source Type", 37);
                                            ReservRec.VALIDATE("Source Subtype", "Document Type");
                                            ReservRec.VALIDATE("Source ID", "Document No.");
                                            ReservRec.VALIDATE("Source Ref. No.", "Line No.");
                                            ReservRec.VALIDATE("Shipment Date", TODAY);
                                            ReservRec.VALIDATE("Created By", USERID);
                                            ReservRec.VALIDATE("Qty. per Unit of Measure", "Qty. per Unit of Measure");
                                            ReservRec.VALIDATE(Quantity, -(TILERem * "Qty. per Unit of Measure"));
                                            ReservRec.VALIDATE("Planning Flexibility", ReservRec."Planning Flexibility"::Unlimited);
                                            ReservRec.VALIDATE("Qty. to Handle (Base)", -(TILERem * "Qty. per Unit of Measure"));
                                            ReservRec.VALIDATE("Qty. to Invoice (Base)", -(TILERem * "Qty. per Unit of Measure"));
                                            ReservRec.VALIDATE("Lot No.", ILERec."Lot No.");
                                            ReservRec.VALIDATE("Item Tracking", ReservRec."Item Tracking"::"Lot No.");
                                            ReservRec.INSERT;
                                            QtyVal := QtyVal - (TILERem * "Qty. per Unit of Measure");
                                        END ELSE BEGIN
                                            IF QtyVal <= TILERem THEN BEGIN
                                                IF QtyVal > 0 THEN BEGIN

                                                    ReservRec.INIT;
                                                    ReservRec2.RESET;
                                                    IF ReservRec2.FINDLAST THEN;
                                                    ReservRec.VALIDATE("Entry No.", ReservRec2."Entry No." + 1);
                                                    ReservRec.VALIDATE(Positive, FALSE);
                                                    ReservRec.VALIDATE("Item No.", "No.");
                                                    ReservRec.VALIDATE("Location Code", "Location Code");
                                                    ReservRec.VALIDATE("Quantity (Base)", -((QtyVal * "Qty. per Unit of Measure")));
                                                    ReservRec.VALIDATE("Reservation Status", ReservRec."Reservation Status"::Prospect);
                                                    ReservRec.VALIDATE("Creation Date", TODAY);
                                                    ReservRec.VALIDATE("Source Type", 37);
                                                    ReservRec.VALIDATE("Source Subtype", "Document Type");
                                                    ReservRec.VALIDATE("Source ID", "Document No.");
                                                    ReservRec.VALIDATE("Source Ref. No.", "Line No.");
                                                    ReservRec.VALIDATE("Shipment Date", TODAY);
                                                    ReservRec.VALIDATE("Created By", USERID);
                                                    ReservRec.VALIDATE("Qty. per Unit of Measure", "Qty. per Unit of Measure");
                                                    ReservRec.VALIDATE(Quantity, -(QtyVal * "Qty. per Unit of Measure"));
                                                    ReservRec.VALIDATE("Planning Flexibility", ReservRec."Planning Flexibility"::Unlimited);
                                                    ReservRec.VALIDATE("Qty. to Handle (Base)", -(QtyVal * "Qty. per Unit of Measure"));
                                                    ReservRec.VALIDATE("Qty. to Invoice (Base)", -(QtyVal * "Qty. per Unit of Measure"));
                                                    ReservRec.VALIDATE("Lot No.", ILERec."Lot No.");
                                                    ReservRec.VALIDATE("Item Tracking", ReservRec."Item Tracking"::"Lot No.");
                                                    ReservRec.INSERT;
                                                    QtyVal := 0;
                                                END;
                                            END;
                                        END;
                                        //END;
                                        //UNTIL ReservRec2.NEXT = 0;
                                    END;
                                END;
                            //END ELSE
                            //  ERROR('This Item %1-%2 has no Approved QC verified Lots'); //End of checking for approved lots
                            UNTIL ILERec.NEXT = 0;
                        END;
                    END;
                END;
            end;
    end;
    //B2B Balu On April 23>>
    procedure BinReclassJnlLine()
    var

        BinReclassJnl2: Record "Bin Reclassfication Jnl";
        BinReclassJnlLines: Page "Bin Reclassfication Jnl";

        TransferLine: Record "Transfer Line";
        WhseShipmenLine: Record "Warehouse Shipment Line";
    begin
        Clear(BinReclassJnlLines);
        BinReclassJnl2.SetRange("Document Type", BinReclassJnl2."Document Type"::Sale);
        BinReclassJnl2.SetRange("Document No.", "Document No.");
        BinReclassJnl2.SetRange("Document Line No.", "Line No.");

        BinReclassJnlLines.SetSourceValues(BinReclassJnl2."Document Type"::Sale, Rec, TransferLine, WhseShipmenLine);
        BinReclassJnlLines.SetTableView(BinReclassJnl2);
        BinReclassJnlLines.RunModal;
    end;

    procedure BinReclassfication()
    var
        ItemJnlLine2: Record "Item Journal Line";
        ItemJnlBatch: Record "Item Journal Batch";
        NoSeriesMgt: Codeunit NoSeriesManagement;
        ILELotRec: Record "Item Ledger Entry";
        BinRec: Record Bin;
        AvaliableQty: Decimal;
        LineQtyBase: Decimal;
        ReqQtyBase: Decimal;
        TotQtyBase: Decimal;
        BinQty: Decimal;

        ItemJnlBatchErr: Label 'you must specify No. series in journal template name % Journal bacth name %2';
        ItemJnlReclassMsg: Label 'There is nothing to process';
        ItemJnlMsg: Label 'Bin Reclassfication successfully';
        ItemJnlConfirm: Label 'Do you want to process reclassfication?';
        ReservEntry: Record "Reservation Entry";
        ItemJoulineLrec: Record "Item Journal Line";
    begin
        Testfield("Qty. to Ship (Base)");
        Testfield(Type, type::Item);
        TestField("No.");
        Testfield("Bin Code");
        //B2BPKON260521>>
        BinReclassJnl.Reset;
        //BinReclassJnl.Setrange("Document Type", BinReclassJnl."Document Type"::Whsship);
        BinReclassJnl.Setrange("Document No.", Rec."Document No.");
        BinReclassJnl.Setrange("Document Line No.", Rec."Line No.");
        BinReclassJnl.Setrange("Bin Code", Rec."Bin Code");
        BinReclassJnl.SetFilter(Quantity, '>%1', 0);
        BinReclassJnl.Setfilter(Reclassfication, '%1', false);
        if BinReclassJnl.FindFIRST then
            Error('From Bin and To Bin Should not be same');
        //B2BPKON260521<<
        if Not Confirm(ItemJnlConfirm, False) then
            Exit;
        Clear(ItemJnlPostBatch);
        Clear(LineQtyBase);
        Clear(ReqQtyBase);
        Clear(TotQtyBase);
        clear(NextLineNo);
        CLEAR(NoSeriesMgt);
        clear(InsertedLine);
        clear(LotFilterText);
        ReservEntry.RESET;
        ReservEntry.SETCURRENTKEY(
        "Source ID", "Source Ref. No.", "Source Type", "Source Subtype", "Source Batch Name", "Source Prod. Order Line");
        ReservEntry.SETRANGE("Source ID", Rec."Document No.");
        ReservEntry.SETRANGE("Source Ref. No.", Rec."Line No.");
        ReservEntry.SETRANGE("Source Type", DATABASE::"Sales Line");
        ReservEntry.SETRANGE("Source Subtype", 1);
        IF ReservEntry.FINDSET THEN
            ReservEntry.DELETEALL(TRUE);
        LineQtyBase := "Qty. to Ship (Base)";
        Customer.get("Sell-to Customer No.");

        ItemJnlLine2.Reset;
        ItemJnlLine2.SETRANGE("Journal Template Name", 'RECLASS');
        ItemJnlLine2.SETRANGE("Journal Batch Name", 'DEFAULT');
        if ItemJnlLine2.FindFirst() then
            ItemJnlLine2.DeleteAll();

        ItemJnlBatch.GET('RECLASS', 'DEFAULT');
        IF ItemJnlBatch."No. Series" = '' THEN
            Error(ItemJnlBatchErr);
        NextDocNo := NoSeriesMgt.GetNextNo(ItemJnlBatch."No. Series", workdate, FALSE);
        ILELotRec.Reset;
        ILELotRec.SetCurrentKey("Item No.", "Location Code", "Expiration Date");
        if Customer."Batch Assign" = Customer."Batch Assign"::LEFO then
            ILELotRec.SetAscending("Expiration Date", false);
        ILELotRec.SETRANGE("Location Code", "Location Code");
        ILELotRec.SETRANGE("Item No.", "No.");
        ILELotRec.SETRANGE("Variant Code", "Variant Code");
        ILELotRec.SETfilter(open, '%1', true);
        ilelotrec.SetFilter("Lot No.", '<>%1', '');
        IF ILELotRec.Findset THEN begin
            repeat

                Clear(AvaliableQty);
                AvaliableQty := ILELotRec."Remaining Quantity";
                if LineQtyBase <= AvaliableQty then begin
                    ReqQtyBase := LineQtyBase;
                    LineQtyBase := 0;

                    InsertReclassJnl(ILELotRec, ReqQtyBase);
                end else begin
                    ReqQtyBase := AvaliableQty;
                    LineQtyBase -= AvaliableQty;
                    InsertReclassJnl(ILELotRec, ReqQtyBase);
                end;
                if LotFilterText <> '' then
                    LotFilterText += '|' + ILELotRec."Lot No."
                else
                    LotFilterText := ILELotRec."Lot No.";


            until (ILELotRec.next = 0) or (LineQtyBase = 0);
            if insertedline then begin
                CLEAR(ItemJoulineLrec);
                ItemJoulineLrec.RESET;
                ItemJoulineLrec.SETRANGE("Journal Template Name", 'RECLASS');
                ItemJoulineLrec.SETRANGE("Journal Batch Name", 'DEFAULT');
                if ItemJoulineLrec.FindSET() then
                    ItemJnlPostBatch.RUN(ItemJoulineLrec);//PKONAU25
                //ItemJnlPostBatch.RUN(ItemJnlLine);//PKONAU25
                //AssignItemTracking();

            end else
                Message(ItemJnlReclassMsg);

        end;

    end;

    procedure AssignItemTracking()
    var
        ItemLedgerEntry: Record "Item Ledger Entry";
        ReservationEntry: record "Reservation Entry";
        ReservationEntry2: Record "Reservation Entry";
        EntryNum: Integer;
        AssignQty: Decimal;
        LineQty: Decimal;


        LotNumErr: Label 'Item Ledger entries not exist in item lot combination';

        LineInserted: Boolean;
        TrackingAssigned: Label 'Tacking Assigned successfully.';
    Begin

        Testfield("Qty. to Ship (Base)");
        Customer.get(Rec."Sell-to Customer No.");
        LineQty := "Qty. to Ship (Base)";



        if LotFilterText = '' then
            Error(LotNumErr);
        ReservationEntry2.RESET;
        IF ReservationEntry2.FINDLAST THEN
            EntryNum := ReservationEntry2."Entry No." + 1
        ELSE
            EntryNum := 1;
        ItemLedgerEntry.RESET;
        ItemLedgerEntry.SETCURRENTKEY("Item No.", "Location Code", "Expiration Date");
        if Customer."Batch Assign" = Customer."Batch Assign"::LEFO then
            ItemLedgerEntry.SetAscending("Expiration Date", false);
        ItemLedgerEntry.SETRANGE("Item No.", Rec."No.");
        ItemLedgerEntry.SETRANGE("Location Code", Rec."Location Code");
        ItemLedgerEntry.SETRANGE("Variant Code", Rec."Variant Code");
        ItemLedgerEntry.SETFILTER(Open, '%1', TRUE);
        ItemLedgerEntry.SETFILTER("Lot No.", LotFilterText);
        IF ItemLedgerEntry.FINDSET THEN BEGIN

            REPEAT

                IF LineQty <= (ItemLedgerEntry."Remaining Quantity") THEN BEGIN
                    AssignQty := LineQty;
                    LineQty := 0;
                END ELSE BEGIN
                    AssignQty := (ItemLedgerEntry."Remaining Quantity");
                    LineQty -= AssignQty;
                END;


                ReservationEntry.INIT;
                ReservationEntry."Entry No." := EntryNum;
                ReservationEntry.VALIDATE(Positive, FALSE);
                ReservationEntry.VALIDATE("Item No.", Rec."No.");
                ReservationEntry.VALIDATE("Location Code", Rec."Location Code");
                ReservationEntry.VALIDATE("Quantity (Base)", -AssignQty);
                ReservationEntry.VALIDATE(Quantity, -ROUND(AssignQty / Rec."Qty. per Unit of Measure"));
                ReservationEntry.VALIDATE("Reservation Status", ReservationEntry."Reservation Status"::Surplus);
                ReservationEntry.VALIDATE("Creation Date", WorkDate());
                ReservationEntry.VALIDATE("Source Type", DATABASE::"Sales Line");
                ReservationEntry.VALIDATE("Source Subtype", 1);
                ReservationEntry.VALIDATE("Source ID", rec."Document No.");

                ReservationEntry.VALIDATE("Source Ref. No.", Rec."Line No.");
                ReservationEntry.VALIDATE("Shipment Date", WorkDate());
                ReservationEntry.VALIDATE("Suppressed Action Msg.", FALSE);
                ReservationEntry.VALIDATE("Planning Flexibility", ReservationEntry."Planning Flexibility"::Unlimited);
                ReservationEntry.VALIDATE("Expiration Date", ItemLedgerEntry."Expiration Date");
                ReservationEntry.VALIDATE("Variant code", ItemLedgerEntry."Variant Code");
                ReservationEntry.VALIDATE("Lot No.", ItemLedgerEntry."Lot No.");
                ReservationEntry."Created By" := USERID;
                ReservationEntry."Item Tracking" := ReservationEntry."Item Tracking"::"Lot No.";

                ReservationEntry.VALIDATE(Correction, FALSE);
                ReservationEntry.INSERT;
                EntryNum += 1;
                LineInserted := true;



            UNTIL (ItemLedgerEntry.NEXT = 0) OR (LineQty = 0);

        END;

        if LineInserted then
            Message(TrackingAssigned)
    END;


    procedure InsertReclassJnl(var ItemLedgerEntryParRec: Record "Item Ledger Entry"; ReqQtyBasePar: decimal)
    var

        ItemUOM: Record "Item Unit of Measure";
        ReclassQtyBase: Decimal;
    begin
        clear(ReclassQtyBase);
        BinReclassJnl.Reset;
        BinReclassJnl.Setrange("Document Type", BinReclassJnl."Document Type"::Sale);
        BinReclassJnl.Setrange("Document No.", Rec."Document No.");
        BinReclassJnl.Setrange("Document Line No.", Rec."Line No.");
        BinReclassJnl.SetFilter(Quantity, '>%1', 0);
        BinReclassJnl.Setfilter(Reclassfication, '%1', false);
        if BinReclassJnl.FindSet then begin
            repeat
                Clear(ReclassQtyBase);
                if ReqQtyBasePar < (BinReclassJnl."Qty.(Base)" - BinReclassJnl."Qty.Reclassfied Base") then begin
                    ReclassQtyBase := ReqQtyBasePar;
                    ReqQtyBasePar := 0;
                    BinReclassJnl."Qty.Reclassfied Base" += ReclassQtyBase;
                    BinReclassJnl.Modify;
                end else begin
                    ReclassQtyBase := (BinReclassJnl."Qty.(Base)" - BinReclassJnl."Qty.Reclassfied Base");
                    ReqQtyBasePar -= ReclassQtyBase;
                    BinReclassJnl."Qty.Reclassfied Base" += ReclassQtyBase;
                    BinReclassJnl.Reclassfication := true;
                    BinReclassJnl.Modify;

                end;
                NextLineNo := NextLineNo + 10000;
                ItemUOM.Get(Rec."No.", Rec."Unit of Measure Code");
                ItemJnlLine.INIT();
                ItemJnlLine."Journal Template Name" := 'RECLASS';
                ItemJnlLine."Journal Batch Name" := 'DEFAULT';
                ItemJnlLine."Posting Date" := WORKDATE();
                ItemJnlLine."Document Date" := WORKDATE();
                ItemJnlLine."Document No." := NextDocNo;
                ItemJnlLine."Line No." := NextLineNo;
                ItemJnlLine."Entry Type" := ItemJnlLine."Entry Type"::Transfer;
                ItemJnlLine.VALIDATE("Item No.", ItemLedgerEntryParRec."Item No.");

                ItemJnlLine.VALIDATE("Variant Code", ItemLedgerEntryParRec."Variant Code");
                ItemJnlLine.VALIDATE("Location Code", ItemLedgerEntryParRec."Location Code");
                ItemJnlLine.VALIDATE("Unit of Measure Code", REC."Unit of Measure Code");
                ItemJnlLine.validate(Quantity, ROUND(ReclassQtyBase / ItemUOM."Qty. per Unit of Measure", 0.00001));
                //ItemJnlLine.validate("Quantity (Base)", ReclassQtyBase);


                ItemJnlLine."Bin Code" := BinReclassJnl."Bin Code";
                ItemJnlLine."New Bin Code" := Rec."Bin Code";
                ItemJnlLine."Warranty Date" := ItemLedgerEntryParRec."Warranty Date";
                ItemJnlLine."Expiration Date" := ItemLedgerEntryParRec."Expiration Date";
                ItemJnlLine.INSERT();
                CreateDime(ItemJnlLine, ItemLedgerEntryParRec);//PKONAU12
                ItemJnlLine.Modify();//PKONAU12
                UpdateResEntry(ItemJnlLine, ItemLedgerEntryParRec."Lot No.");


                InsertedLine := true;

            until (BinReclassJnl.Next() = 0) or (ReqQtyBasePar = 0);
        end;


    END;

    procedure CreateDime(Var IJLDim: Record "Item Journal Line"; ILEQt: Record "Item Ledger Entry")
    var
        TrackingSpecLv: Record "Tracking Specification";
        ReservationEntry: Record "Reservation Entry";
    begin
        IJLDim.Validate("Dimension Set ID", ILEQt."Dimension Set ID");
        IJLDim."Shortcut Dimension 1 Code" := ILEQt."Global Dimension 1 Code";
        IJLDim."Shortcut Dimension 2 Code" := ILEQt."Global Dimension 2 Code";
        IJLDim.Validate("New Dimension Set ID", ILEQt."Dimension Set ID");
        IJLDim."New Shortcut Dimension 1 Code" := ILEQt."Global Dimension 1 Code";
        IJLDim."New Shortcut Dimension 2 Code" := ILEQt."Global Dimension 2 Code";
    end;

    procedure UpdateResEntry(VAR ItemJournalLineParRec2: Record "Item Journal Line"; LotCodePar: Code[20]);
    Var
        ReservationEntry: Record "Reservation Entry";
        ReservationEntry2: Record "Reservation Entry";
        EntryNum: Integer;

    begin
        IF ReservationEntry2.FINDlast() THEN
            EntryNum := ReservationEntry2."Entry No."
        ELSE
            EntryNum := 0;
        ReservationEntry.INIT();
        ReservationEntry."Entry No." := EntryNum + 1;
        ReservationEntry.VALIDATE(Positive, FALSE);
        ReservationEntry.VALIDATE("Item No.", ItemJournalLineParRec2."Item No.");
        ReservationEntry.VALIDATE("Location Code", ItemJournalLineParRec2."Location Code");
        ReservationEntry.VALIDATE("variant Code", ItemJournalLineParRec2."Variant Code");

        ReservationEntry.VALIDATE("Quantity (Base)", -ItemJournalLineParRec2."Quantity (Base)");
        ReservationEntry.VALIDATE(Quantity, -ItemJournalLineParRec2.Quantity);
        ReservationEntry.VALIDATE("Reservation Status", ReservationEntry."Reservation Status"::Prospect);
        ReservationEntry.VALIDATE("Creation Date", ItemJournalLineParRec2."Posting Date");
        ReservationEntry.VALIDATE("Source Type", DATABASE::"Item Journal Line");
        ReservationEntry.VALIDATE("Source Subtype", 4);
        ReservationEntry.VALIDATE("Source ID", ItemJournalLineParRec2."Journal Template Name");
        ReservationEntry.VALIDATE("Source Batch Name", ItemJournalLineParRec2."Journal Batch Name");
        ReservationEntry.VALIDATE("Source Ref. No.", ItemJournalLineParRec2."Line No.");
        ReservationEntry.VALIDATE("Shipment Date", ItemJournalLineParRec2."Posting Date");

        ReservationEntry.VALIDATE("Suppressed Action Msg.", FALSE);
        ReservationEntry.VALIDATE("Planning Flexibility", ReservationEntry."Planning Flexibility"::Unlimited);

        ReservationEntry.VALIDATE("Lot No.", LotCodePar);
        ReservationEntry.VALIDATE("New Lot No.", LotCodePar);
        ReservationEntry.VALIDATE("Expiration Date", ItemJournalLineParRec2."Expiration Date");
        //PKONJ25.2
        IF ItemJournalLineParRec2."Expiration Date" <> 0D THEN
            ReservationEntry.VALIDATE("New Expiration Date", ItemJournalLineParRec2."Expiration Date")
        else
            ReservationEntry.VALIDATE("New Expiration Date", CalcDate('6M', Today));
        //PKONJ25.2
        ReservationEntry.VALIDATE(Correction, FALSE);
        ReservationEntry.INSERT();
    end;

    procedure SendBinRecMai()
    var
        InvSetup: record "Inventory Setup";
        ItemLedgerEntry: record "Item Ledger Entry";
        BinReclassJnl2: Record "Bin Reclassfication Jnl";
        SMTPMailSetup: Record "SMTP Mail Setup";
        SMTPMail: Codeunit "SMTP Mail";
        SenderAddr: Text;
        RecepientAddr: List of [Text];
        SubjectTxt: text;
    begin
        IF InvSetup.get() And (InvSetup."Bin Reclass Mail Alert ID" <> '') then begin
            SMTPMailSetup.get();
            SenderAddr := SMTPMailSetup."User ID";
            BinReclassJnl2.RESET();
            BinReclassJnl2.SetRange("Document No.", "Document No.");
            BinReclassJnl2.SetRange("Document Line No.", "Line No.");
            IF BinReclassJnl2.FINDFIRST THEN begin
                RecepientAddr.Add(InvSetup."Bin Reclass Mail Alert ID");
                if InvSetup."Bin Reclass Mail Alert ID2" <> '' then
                    RecepientAddr.Add(InvSetup."Bin Reclass Mail Alert ID2");
                SubjectTxt := 'Bin Reclassification Posted For  -' + FORMAT(BinReclassJnl2."Document No.") + '-' + FORMAT(BinReclassJnl2."Line No.") + ' On ' + FORMAT(WorkDate());
                SMTPMAil.CreateMessage('CHI Despatches Team', SenderAddr, RecepientAddr, SubjectTxt, '', true);
                SMTPMail.AppendBody(CreateEmailBodyVend(BinReclassJnl2));
                SMTPMAil.send;
            end;
        end;
    end;

    Local procedure CreateEmailBodyVend(BinReclassJnl2Lpa: Record "Bin Reclassfication Jnl") EmailBodyText: Text
    var
        BinReclassJnl2: Record "Bin Reclassfication Jnl";
    begin
        BinReclassJnl2.Reset();
        BinReclassJnl2.SetRange("Document No.", BinReclassJnl2Lpa."Document No.");
        BinReclassJnl2.SetRange("Line No.", BinReclassJnl2Lpa."Line No.");
        IF BinReclassJnl2.FindSet() then BEGIN
            EmailBodyText += '<tr>';
            EmailBodyText += StrSubstNo('<td>%1</td>', 'Document No.-' + BinReclassJnl2."Document No.");
            EmailBodyText += '<tr>';
            EmailBodyText += StrSubstNo('<td>%1</td>', 'Line No.-' + FORMAT(BinReclassJnl2Lpa."Line No."));
            EmailBodyText += '<tr>';
            EmailBodyText += StrSubstNo('<td>%1</td>', 'Date-' + FORMAT(WorkDate()));
            EmailBodyText += '<tr>';
            EmailBodyText += StrSubstNo('<td>%1</td>', 'UserID-' + USERID);
            EmailBodyText += '<tr>';
            EmailBodyText += '</tr>';
            EmailBodyText += '<table border="1">';
            EmailBodyText += '<tr>';
            EmailBodyText += StrSubstNo('<td>%1</td>', 'Item No.');
            EmailBodyText += StrSubstNo('<td>%1</td>', 'Variant Code');
            EmailBodyText += StrSubstNo('<td>%1</td>', 'Location Code');
            EmailBodyText += StrSubstNo('<td>%1</td>', 'From Bin Code');
            EmailBodyText += StrSubstNo('<td>%1</td>', 'To Bin Code');
            EmailBodyText += StrSubstNo('<td>%1</td>', 'Quantity');
            EmailBodyText += '</tr>';
            repeat
                EmailBodyText += '<tr>';
                EmailBodyText += StrSubstNo('<td>%1</td>', BinReclassJnl2."Item No.");
                EmailBodyText += StrSubstNo('<td>%1</td>', BinReclassJnl2."Variant Code");
                EmailBodyText += StrSubstNo('<td>%1</td>', BinReclassJnl2."Location Code");
                EmailBodyText += StrSubstNo('<td>%1</td>', BinReclassJnl2."Bin Code");
                EmailBodyText += StrSubstNo('<td>%1</td>', "Bin Code");
                EmailBodyText += StrSubstNo('<td>%1</td>', BinReclassJnl2.Quantity);
                EmailBodyText += '</tr>';
            until BinReclassJnl2.Next = 0;
        end;
        EmailBodyText += '</table>';
        exit(EmailBodyText);
    end;



    //B2B Balu On April 23<<
    //B2BMSOnAug11>>
    procedure OpenItemTrackingLinescopy()
    var
        Job: Record Job;
        IsHandled: Boolean;
    begin
        IsHandled := false;
        OnBeforeOpenItemTrackingLines(Rec, IsHandled);
        if IsHandled then
            exit;

        TestField(Type, Type::Item);
        TestField("No.");
        TestField("Quantity (Base)");
        if "Job Contract Entry No." <> 0 then
            Error(Text048, TableCaption, Job.TableCaption);

        IsHandled := false;
        OnBeforeCallItemTracking(Rec, IsHandled);
        if not IsHandled then
            Codeunit50.CallItemTracking1(Rec);
    end;

    //rebate issue 10/05/24  >>.
    procedure CalcRebDiscToInvoice()
    var
        OldRebDiscAmtToInv: Decimal;
        currency: Record Currency;
    begin
        //GetSalesHeader();
        OldRebDiscAmtToInv := "Rebate Disc. Amount to Inv.";
        /*   if Quantity = 0 then
              "Rebate Disc. Amount to Inv." := 0;
          else */
        "Rebate Disc. Amount to Inv." :=
        Round(
          "Rebate discount" * "Qty. to Invoice" / Quantity,
          Currency."Amount Rounding Precision");

        if// (AmtafterRebate <> 0) AND 
        ("Rebate Disc. Amount to Inv." <> 0) then //begin
            Validate(AmtafterRebate, (AmtafterRebate - "Rebate Disc. Amount to Inv.")) else
            Validate(AmtafterRebate, 0);

        /*  if OldRebDiscAmtToInv <> "Rebate Disc. Amount to Inv." then begin
             "Amount Including VAT" := "Amount Including VAT" - "VAT Difference";
             "VAT Difference" := 0;
         end; */
        //NotifyOnMissingSetup(FieldNo("Inv. Discount Amount"));
    end;
    //end <<<

    Procedure ReturnRebateLineBalance()
    var
        RebateSalesLine: Record "Sales Line";
        rebatebalance, Totalrebate : Decimal;
        currencyrec: Record currency;
        rebatevariable: record "Rebate Records";
    begin
        rebatebalance := 0;
        Totalrebate := 0;
        RebateSalesLine.SetCurrentKey("Document No.", "Line No.");
        RebateSalesLine.setrange("document No.", Rec."Document No.");
        RebateSaleSLine.setrange("line No.", Rec."line No.");
        RebateSalesLine.SetFilter("Rebate Discount", '>%1', 0);
        //RebateSalesLine.CalcSums("Rebate Discount", Quantity, "Outstanding Quantity");
        if RebateSalesLine.FindSet() then
                //repeat
                // if RebateSalesLine."Rebate Discount" <> 0 then 
                begin
            if RebateSalesLine."Quantity Invoiced" <> 0 then begin
                if RebateSalesLine."Quantity Invoiced" <> RebateSalesLine.Quantity then begin
                    if RebateSalesLine."Outstanding Quantity" <> 0 then
                        rebatebalance := round(RebateSalesLine."Rebate Discount" *
                        (RebateSalesLine."Outstanding Quantity") / RebateSalesLine.quantity,
                        Currencyrec."Amount Rounding Precision") else
                        rebatebalance := round(RebateSalesLine."Rebate Discount" *
                        (RebateSalesLine.Quantity) / RebateSalesLine.quantity,
                        Currencyrec."Amount Rounding Precision");
                    Totalrebate += rebatebalance;
                end;
            end else
                Totalrebate := RebateSalesLine."Rebate Discount";

        end;
        // until RebateSalesLine.Next() = 0;
        if Totalrebate <> 0 then begin
            rebatevariable.reset;
            rebatevariable.setrange("Customer No.", rec."Sell-to Customer No.");
            if rebatevariable.FindLast() then begin
                rebatevariable."Balance Rebate Amount" += Totalrebate;
                If Totalrebate <> rebatevariable."Rebate Amount" then begin
                    rebatevariable."Rebate Discount Status" := rebatevariable."Rebate Discount Status"::Partial;
                    rebatevariable."No. of Transaction in a Month" += 1;
                end else
                    rebatevariable."Rebate Discount Status" := rebatevariable."Rebate Discount Status"::Full;
                rebatevariable.Modify();
            end;
        end;
    end;
    //check if rebate variable exists
    procedure RebateVariablelineexits(): Boolean
    var
        sLine: Record "Sales Line";
    begin

        SLine.setrange("document No.", Rec."document No.");
        SLine.setrange("line No.", Rec."line No.");
        SLine.SetFilter("Rebate Discount", '>%1', 0);
        // SLine.CalcSums("Rebate Discount");
        if sline.FindFirst() then
            exit(true) else
            exit(false);
    end;


    [IntegrationEvent(false, false)]
    local procedure OnBeforeOpenItemTrackingLines(SalesLine: Record "Sales Line"; var IsHandled: Boolean)
    begin
    end;

    [IntegrationEvent(false, false)]
    local procedure OnBeforeCallItemTracking(var SalesLine: Record "Sales Line"; var IsHandled: Boolean)
    begin
    end;
    //B2BMSOnAug11


    var
        Customer: Record Customer;
        ItemJnlPostBatch: Codeunit "Item Jnl.-Post Batch";
        NextLineNo: Integer;
        InsertedLine: Boolean;
        LotFilterText: Text;
        NextDocNo: code[20];
        ItemJnlLine: Record "Item Journal Line";
        BinReclassJnl: Record "Bin Reclassfication Jnl";
        //B2BMSOnAug11>>
        Text048: Label 'You cannot use item tracking on a %1 created from a %2.';
        Codeunit50: Codeunit Codeunit50;
    //B2BMSOnAug11<<



}