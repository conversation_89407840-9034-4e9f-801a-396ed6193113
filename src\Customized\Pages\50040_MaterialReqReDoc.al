page 50040 "Material Requisit Doc-Released"
{
    DeleteAllowed = false;
    Editable = true;
    InsertAllowed = false;
    PageType = Document;
    SourceTable = MRSHeader;
    SourceTableView = SORTING("MRS No.")
                      ORDER(Ascending)
                      WHERE(Status = FILTER(Released), Closed = filter(false), "Prod. Order Ref. No." = filter(= ''), "Maintenance Job Card No." = filter(= ''));
    UsageCategory = Documents;
    ApplicationArea = all;

    layout
    {
        area(content)
        {
            group(General)
            {
                Caption = 'General';
                field("MRS No."; "MRS No.")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Manual MRS No."; "Manual MRS No.")
                {
                    ApplicationArea = all;
                    Editable = false;
                    Visible = false;
                }
                field("Shortcut Dimension 1 Code"; "Shortcut Dimension 1 Code")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Shortcut Dimension 2 Code"; "Shortcut Dimension 2 Code")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field(Comment; Comment)
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Materials Issued To"; "Materials Issued To")
                {
                    ApplicationArea = all;
                }
                field("Staff Name"; "Staff Name")
                {
                    ApplicationArea = all;
                }
                field("Document Date"; "Document Date")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Issued Date"; "Issued Date")
                {
                    ApplicationArea = all;
                    //Editable = false;
                }
                field("Expected Delivery Date"; "Expected Delivery Date")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("MRS Type"; "MRS Type")
                {
                    ApplicationArea = all;
                }
                field("Production Batch No."; "Production Batch No.")
                {
                    ApplicationArea = all;
                    Visible = false;
                }
                field("Total Amout(LCY)"; "Total Amout(LCY)")
                {
                    ApplicationArea = all;//B2BPKON200521
                }
                field(Status; Status)
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Purchase Type"; "Purchase Type")
                {
                    ApplicationArea = all;
                    Editable = false;
                }

            }
            part(PostedMatReqLineSubform; "Mater Req Rel Lines")
            {
                ApplicationArea = all;
                SubPageLink = "Document No." = FIELD("MRS No.");
            }
        }
        area(FactBoxes)
        {
            part("Attached Documents"; "Document Attachment Factbox")
            {
                ApplicationArea = All;
                Caption = 'Attachments';
                SubPageLink = "Table ID" = CONST(50004),
                                "No." = FIELD("MRS No.");
            }
            systempart(PyamentTermsLinks; Links)
            {
                ApplicationArea = RecordLinks;
            }
            systempart(PyamentTermsNotes; Notes)
            {
                ApplicationArea = Notes;
            }
        }
    }

    actions
    {
        area(Processing)
        {
            group("&Requisition")
            {
                Caption = '&Requisition';
                action(Dimensions)
                {
                    ApplicationArea = all;
                    Caption = 'Dimensions';
                    ShortCutKey = 'Shift+Ctrl+D';
                    Image = Dimensions;
                    trigger OnAction();
                    begin
                        ShowDocDim();
                    end;
                }
                separator(Separator1102152037)
                {
                }

                group("F&unctions")
                {
                    Caption = 'F&unctions';
                    Visible = true;


                    action("Create Issue Jounal Batch")
                    {
                        ApplicationArea = all;
                        Caption = 'Create Issue Jounal Batch';
                        image = CreateLinesFromJob;
                        trigger OnAction();
                        begin
                            MRSLineRec.SETCURRENTKEY("Document No.", "Line No.");
                            MRSLineRec.RESET();
                            MRSLineRec.SETRANGE("Document No.", "MRS No.");
                            MRSLineRec.SETFILTER("Qty. to Issue", '<>%1', 0);
                            if not MRSLineRec.FINDFIRST() then
                                ERROR('No Line with Qty. to Issue > 0');


                            MRSLineRec.RESET();
                            MRSLineRec.SETCURRENTKEY("Document No.", "Line No.");
                            MRSLineRec.SETRANGE("Document No.", "MRS No.");
                            MRSLineRec.SETFILTER("Qty. to Issue", '<>%1', 0);
                            if MRSLineRec.FINDSET() then
                                repeat
                                    MRSLineRec.CALCFIELDS("Qty. on Requisition");
                                    MRSLineRec.CALCFIELDS("Available Stock");
                                    MRSLineRec.CALCFIELDS("Quantity Issued");
                                    if MRSLineRec."Qty. to Issue" > (MRSLineRec.Quantity - MRSLineRec."Quantity Issued") then
                                        ERROR(Text002Lbl, (MRSLineRec.Quantity - MRSLineRec."Quantity Issued"));
                                until MRSLineRec.NEXT() = 0;
                            CreateItemJnlLine();
                            CurrPage.Update();
                        end;
                    }
                    action("Create Return Jnl. Batch")
                    {
                        ApplicationArea = all;
                        Caption = 'Create Return Jnl. Batch';
                        image = Create;
                        trigger OnAction();
                        begin
                            MRSLineRec.Reset();
                            MRSLineRec.SETRANGE("Document No.", "MRS No.");
                            MRSLineRec.SETFILTER("Qty. to Return", '<>%1', 0);
                            if MRSLineRec.FIND('-') then
                                CreateReturnItemJnlLine()
                            else
                                Error('Please check value in Qty. to Return field. It should not be empty and atleast have in one line.');
                            CurrPage.Update();
                        end;
                    }
                    separator("-")
                    {
                        Caption = '-';
                    }
                    separator(Separator1000000011)
                    {
                    }
                    action("Calc. Qty. to Purchase")
                    {
                        ApplicationArea = all;
                        Caption = 'Calc. Qty. to Purchase';
                        Image = Calculate;
                        trigger OnAction();
                        begin
                            CalcQtyToPurch();
                            CurrPage.Update();
                        end;
                    }
                    //Balu 05132021>>
                    action("Create loading Advice")
                    {
                        ApplicationArea = all;
                        Caption = 'Create loading Advice';

                        trigger OnAction();
                        begin
                            MRSLineRec.RESET;
                            CLEAR(CreateLoadingAdvice);
                            MRSLineRec.SETCURRENTKEY("Document No.", "Line No.");
                            MRSLineRec.SETRANGE("Document No.", "MRS No.");
                            MRSLineRec.SETFILTER("Qty. to Issue", '<>%1', 0);
                            MRSLineRec.SETRANGE(MRSLineRec."Loading Advice Created", false);
                            if MRSLineRec.FINDSET then begin
                                repeat
                                    if MRSLineRec."Item Category Code" = 'FUEL' then
                                        CreateLoadingAdvice := true;
                                until MRSLineRec.NEXT = 0;
                            end else
                                ERROR('Loading Avice is Printed earlier.');

                            if CreateLoadingAdvice then begin
                                SETRANGE("MRS No.", "MRS No.");
                                REPORT.RUN(50054, true, false, Rec);//NYO;
                            end;
                        end;
                    }
                    action("Re-Print Loading Advice")
                    {
                        ApplicationArea = all;
                        Caption = 'Re-Print Loading Advice';
                        trigger OnAction();
                        begin
                            UserSetup.GET(USERID);
                            if not UserSetup."Material Loading Reprint" then
                                Error('You do not have the permission for reprint please enabla material loading reprint');
                            SETRANGE("MRS No.", "MRS No.");
                            REPORT.RUN(50054, true, false, Rec);
                        end;
                    }
                    //Balu 05132021<<
                    action("Make Purch. &Requisition")
                    {
                        ApplicationArea = all;
                        Caption = 'Make Purch. &Requisition';
                        Image = CreateDocument;
                        trigger OnAction();
                        var
                            BudgLne: record "Budget Line";
                            MrLne: Record MRSLine;
                        begin
                            //PhaniFeb112021 >>
                            MrLne.RESET;
                            MrLne.SetRange("Document No.", "MRS No.");
                            IF MrLne.findset then
                                repeat
                                    IF MrLne."Capex No." <> '' THEN BEGIN
                                        BudgLne.RESET;
                                        BudgLne.SetRange("No.", MrLne."Capex No.");
                                        IF BudgLne.Findfirst() then BEGIN
                                            BudgLne.CalcFields(Amount);
                                            BudgLne.CalcFields("Budget Utilized in MRS");
                                            IF MrLne.Amount > (BudgLne.Amount - BudgLne."Budget Utilized in MRS") then
                                                error('Amount Exceeds budget amount');
                                        end;
                                    end;
                                until mrLne.next = 0;
                            //PhaniFeb112021 <<


                            MRSLineRec.RESET();
                            MRSLineRec.SETCURRENTKEY("Document No.", "Line No.");
                            MRSLineRec.SETRANGE("Document No.", "MRS No.");
                            MRSLineRec.SETRANGE(Type, MRSLineRec.Type::Item);
                            MRSLineRec.SETFILTER("Insert To Pur. Req No.", '<>%1', '');
                            if MRSLineRec.FINDSET() then
                                Error('You can not select value in Insert Purch. Req field for line no. %1', MRSLineRec."Line No.");
                            MRSLineRec.RESET();
                            MRSLineRec.SETCURRENTKEY("Document No.", "Line No.");
                            MRSLineRec.SETRANGE("Document No.", "MRS No.");
                            MRSLineRec.SETFILTER("Qty. to Request", '<>%1', 0);
                            if MRSLineRec.FINDSET() then
                                repeat
                                    MRSLineRec.CalcFields("FA No.");

                                    IF Rec."MRS Type" = Rec."MRS Type"::"FA Asset" then BEGIN
                                        IF MRSLineRec."Existing FA No." = '' THEN
                                            MRSLineRec.TestField("FA No.");
                                        //B2B.P.K on 14.05.2021
                                        IF MRSLineRec."Maintenance Code" = '' then begin
                                            MRSLineRec.TestField("Capex No.");
                                            MRSLineRec.TestField("Capex Line No.");
                                        end;//B2B.P.K on 14.05.2021
                                    END;
                                    MRSLineRec.CALCFIELDS(MRSLineRec."Qty. on Requisition");
                                    MRSLineRec.CALCFIELDS(MRSLineRec."Available Stock");
                                    if MRSLineRec."Qty. on Requisition" > 0 then
                                        if MRSLineRec.Quantity < MRSLineRec."Available Stock" then
                                            ERROR(Text001Lbl);
                                until MRSLineRec.NEXT() = 0;
                            CreatePurchRequisition();
                            CurrPage.Update();
                        end;
                    }
                    action("Insert To Purch. &Requisition")
                    {
                        ApplicationArea = all;
                        Caption = 'Insert To Purch. &Requisition';
                        Image = CreateDocument;
                        trigger OnAction();
                        begin
                            MRSLineRec.RESET();
                            MRSLineRec.SETCURRENTKEY("Document No.", "Line No.");
                            MRSLineRec.SETRANGE("Document No.", "MRS No.");
                            MRSLineRec.SETRANGE(Type, MRSLineRec.Type::Item);
                            MRSLineRec.SETFILTER("Insert To Pur. Req No.", '<>%1', '');
                            if MRSLineRec.FINDSET() then BEGIN
                                repeat
                                    MRSLineRec.CALCFIELDS(MRSLineRec."Qty. on Requisition");
                                    MRSLineRec.CALCFIELDS(MRSLineRec."Available Stock");
                                    if MRSLineRec."Qty. on Requisition" > 0 then
                                        if MRSLineRec.Quantity < MRSLineRec."Available Stock" then
                                            ERROR(Text001Lbl);
                                until MRSLineRec.NEXT() = 0;
                                InsertToPurchRequisition();
                                CurrPage.Update();
                            end Else
                                Error('There is nothing to create.');
                        end;
                    }
                    //PhaniFeb102021 >>
                    action(Reopen)
                    {
                        ApplicationArea = all;
                        Caption = 'Reopen MRS';
                        Image = ReOpen;
                        trigger OnAction()
                        VAr
                            MatReqLne: Record MRSLine;
                        BEGIN
                            MatReqLne.RESET;
                            MatReqLne.SetRange("Document No.", "MRS No.");
                            MatReqLne.SetFilter("Purch. Req. Ref. No.", '<>%1', '');
                            IF MatReqLne.findfirst then
                                error('You Cannot Reopen this %1 because already Purchae Requisition raised.', "MRS No.");
                            Status := Status::Open;
                            CurrPage.Update(true);
                            Message('Document has been Reopened.');
                        END;

                    }
                    //PhaniFeb102021 <<
                    action("Close MRS")
                    {
                        ApplicationArea = all;
                        Caption = 'Close MRS';
                        Image = Cancel;

                        trigger OnAction();
                        var
                            PurchReqHdrLRec: record "Purch. Req Header";
                        begin

                            "Last Modified Date" := CurrentDateTime;
                            "Last Modified By" := USERID;

                            /*PurchReqHdrLRec.Reset();
                            PurchReqHdrLRec.SetRange("MRS No.", "MRS No.");
                            if not PurchReqHdrLRec.IsEmpty then
                                Error('Already Purchase requisation %1 is Created', PurchReqHdrLRec."No.");*/
                            Closed := true;
                            MODIFY();
                            Message('Document has been Closed.');
                        end;
                    }
                }
                group("&Print")
                {
                    Caption = '&Print';
                    action("Requisition Slip")
                    {
                        ApplicationArea = all;
                        Caption = 'Requisition Slip';
                        Ellipsis = true;
                        image = Report;
                        trigger OnAction();
                        var
                            MRSHeader: record MRSHeader;
                        begin
                            MRSHeader.RESET();
                            MRSHeader.SETRANGE("MRS No.", "MRS No.");
                            REPORT.RUNMODAL(50096, true, false, MRSHeader);
                        end;
                    }
                }
            }
        }
    }

    var
        CreateLoadingAdvice: Boolean;//05132021
        UserSetup: Record "User Setup";//05132021
        MRSLineRec: Record MRSLine;
        Text001Lbl: Label 'Purchase Requisition not required when Stock available is greater than MRS Quantity.';
        Text002Lbl: Label 'Qty to Issue must not exceed %1.';



}