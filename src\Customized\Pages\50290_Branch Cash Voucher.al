page 50290 "Branch Cash Voucher"
{
    // version CHI6.0

    // PROJECT : CHI 6.0
    // **********************************************************************************
    // SIGN
    // **********************************************************************************
    // CHI     :  CHI 6.0 Developments
    // UNL     :  Univision Nigeria Limited
    // SAA     :  SAHEED ADIO ADEOSUN
    // HO      :  Henry Osiningben
    // NYO     :  Nyong
    // **********************************************************************************
    // VER      SIGN         DATE          DESCRIPTION
    // **********************************************************************************
    // 1.0           UNL      06-Dec-11   -> Form Created to display journal voucher.
    // 1.0           HO       07-Sep-12   -> Code added to "Form-OnDelereRecord()" to allow Archive of deleted Journal Voucher Document No.
    // 3.0           SAA      18-May-17   -> Code added to OnOpenForm() to filter responsibility centers for users.
    // CRF:2019-0042 NYO 16-May-19   ->Form Caption Changed to"Bank Payment Settlement Voucher"
    //                               -> Code added in Form - OnNewRecord(BelowxRec : Boolean)
    //                               -> Set Table View of this Form added JV Type as "LBSV"

    Caption = 'Branch Cash Voucher';
    PageType = Document;
    SourceTable = "Voucher Header";
    SourceTableView = WHERE("Voucher Type" = FILTER(JV),
                            "JV Type" = FILTER(" " | General | BRJV | LBSV),
                            Status = FILTER(<> Released),
                            "Branch CPV" = FILTER(true));

    layout
    {
        area(content)
        {
            group(General)
            {
                Caption = 'General';
                field("Document No."; "Document No.")
                {
                    ApplicationArea = All;
                    trigger OnAssistEdit();
                    begin
                        if AssistEdit(xRec) then
                            CurrPage.UPDATE;
                    end;
                }
                field("Responsibility Center"; "Responsibility Center")
                {
                    Editable = true;
                    ApplicationArea = All;

                    trigger OnLookup(var Text: Text): Boolean;
                    begin
                        RespCent.SETCURRENTKEY(Code);
                        if RespCent.FINDSET then
                            repeat
                                if UserIDRespCent.GET(USERID, RespCent.Code) then
                                    RespCent.MARK(true);
                            until RespCent.NEXT = 0;

                        RespCent.MARKEDONLY(true);
                        if PAGE.RUNMODAL(0, RespCent) = ACTION::LookupOK then
                            "Responsibility Center" := RespCent.Code;

                        if RespCent.GET("Responsibility Center") then begin
                            if RespCent."Global Dimension 1 Code" <> '' then
                                VALIDATE("Shortcut Dimension 1 Code", RespCent."Global Dimension 1 Code");
                        end;
                    end;
                }
                field("Document Date"; "Document Date")
                {
                    Editable = false;
                    ApplicationArea = All;
                }
                field("Posting Date"; "Posting Date")
                {
                    ApplicationArea = All;
                }
                field("Account Type"; "Account Type")
                {
                    ApplicationArea = All;
                }
                field("Account No."; "Account No.")
                {
                    Caption = 'Credit Account No.';
                    ApplicationArea = All;

                    trigger OnValidate();
                    begin
                        //TestTransactionType;;
                        AccountNoOnAfterValidate;
                    end;
                }
                field(Narration; Narration)
                {
                    ApplicationArea = All;
                }
                field("Amount (LCY)"; "Amount (LCY)")
                {
                    ApplicationArea = All;
                }
                field("Amount (dbr.)"; "Amount (dbr.)")
                {
                    ApplicationArea = All;
                }
                field("Account Balance"; "Account Balance")
                {
                    ApplicationArea = All;
                }
                field(BalanceP; BalanceP)
                {
                    ApplicationArea = All;
                    Caption = 'Balance After Posting';
                    Editable = false;
                }
                field(Status; Status)
                {
                    Editable = false;
                    ApplicationArea = All;
                }
                field("Shortcut Dimension 1 Code"; "Shortcut Dimension 1 Code")
                {
                    ApplicationArea = All;
                }
                field("Shortcut Dimension 2 Code"; "Shortcut Dimension 2 Code")
                {
                    ApplicationArea = All;
                }
                field("Account Name"; "Account Name")
                {
                    ApplicationArea = All;
                }
            }
            part(VoucherLines; "Branch Cash Voucher Subform")
            {
                ApplicationArea = All;
                SubPageLink = "Journal Template Name" = FIELD("Journal Template Code"),
                              "Journal Batch Name" = FIELD("Journal Batch Name"),
                              "Document No." = FIELD("Document No.");
            }
            group(Usertrail)
            {
                Caption = 'Usertrail';
                field("Created By"; "Created By")
                {
                    ApplicationArea = All;
                }
                field("Created By Name"; "Created By Name")
                {
                    ApplicationArea = All;
                }
                field("Created Date"; "Created Date")
                {
                    ApplicationArea = All;
                }
                field("Created Time"; "Created Time")
                {
                    ApplicationArea = All;
                }
                field("Modified By"; "Modified By")
                {
                    ApplicationArea = All;
                }
                field("Modified By Name"; "Modified By Name")
                {
                    ApplicationArea = All;
                }
                field("Modified Date"; "Modified Date")
                {
                    ApplicationArea = All;
                }
                field("Modified Time"; "Modified Time")
                {
                    ApplicationArea = All;
                }
            }
        }
        area(FactBoxes)
        {
            part("Attached Documents"; "Document Attachment Factbox")
            {
                ApplicationArea = All;
                Caption = 'Attachments';
                SubPageLink = "Table ID" = CONST(50117),
                                "No." = FIELD("Document No.");
                // Type = FIELD("Voucher Type");
            }
            systempart(PyamentTermsLinks; Links)
            {
                ApplicationArea = RecordLinks;
            }
            systempart(PyamentTermsNotes; Notes)
            {
                ApplicationArea = Notes;
            }
        }
        //g2s29Dev23
    }

    actions
    {
        area(navigation)
        {
            group("&Voucher")
            {
                Caption = '&Voucher';
                separator(Separator1000000067)
                {
                }
                action(Dimensions)
                {
                    Caption = 'Dimensions';
                    ApplicationArea = All;

                    trigger OnAction();
                    begin
                        ShowDocDim;
                    end;
                }
                separator(Separator1000000069)
                {
                }
                action(Approvals)
                {
                    Caption = 'Approvals';

                    trigger OnAction();
                    begin
                        approvalmngmt.ApproveRecordApprovalRequest(RecordId());
                    end;
                }
                /*action(InsertLine)
                {
                    ApplicationArea = all;
                    Caption = 'Insert Line';
                    Promoted = true;
                    PromotedCategory = Process;
                    ShortcutKey = 'Ctrl+I';
                    trigger OnAction();
                    var
                        genjoulrec: Record "Gen. Journal Line";
                        linen: Integer;
                    begin
                        genjoulrec.RESET();
                        genjoulrec.SetRange("Journal Template Name", 'JV');
                        genjoulrec.SetRange("Journal Batch Name", 'Journal');
                        //genjoulrec.SetRange("Document No.", "Document No.");
                        IF genjoulrec.FINDLAST THEN
                            linen := 10000 + genjoulrec."Line No."
                        else
                            linen := 10000;

                        clear(genjoulrec);
                        genjoulrec.init();
                        genjoulrec."Journal Template Name" := 'JV';
                        genjoulrec."Journal Batch Name" := 'Journal';
                        genjoulrec."Voucher Type" := genjoulrec."Voucher Type"::JV;
                        genjoulrec."Document No." := "Document No.";
                        genjoulrec."Posting Date" := Today; //PJ
                        genjoulrec."Line No." := linen;
                        genjoulrec.insert(True);
                        Message('Line Inserted %1..%2', genjoulrec."Journal Template Name", genjoulrec."Journal Batch Name");
                        //insert(true);
                        CurrPage.Update();
                    end;
                }*/
            }
            group("F&unctions")
            {
                Caption = 'F&unctions';
                action("Send Approval Request")
                {
                    ApplicationArea = All;
                    Image = SendApprovalRequest;
                    Visible = Not OpenApprEntrEsists and CanrequestApprovForFlow;
                    Promoted = true;
                    PromotedIsBig = true;
                    PromotedCategory = Process;
                    PromotedOnly = true;
                    trigger OnAction()
                    begin
                        CheckDimensions();
                        TestField("Posting Date");
                        ClearValues();
                        CheckHeaderLines(Rec);
                        IF allinoneCU.CheckJournalVoucherApprovalsWorkflowEnabled(Rec) then
                            allinoneCU.OnSendJournalVoucherForApproval(Rec);
                    end;
                }
                action("Cancel Approval Request")
                {
                    ApplicationArea = All;
                    Image = CancelApprovalRequest;
                    Visible = CanCancelapprovalforrecord or CanCancelapprovalforflow;
                    Promoted = true;
                    PromotedIsBig = true;
                    PromotedCategory = Process;
                    PromotedOnly = true;
                    trigger OnAction()
                    begin
                        allinoneCU.OnCancelJournalVoucherForApproval(Rec);
                    end;
                }
                separator(Separator1000000049)
                {
                }
                action("Re&lease")
                {
                    ApplicationArea = all;
                    Caption = 'Re&lease';
                    ShortCutKey = 'Ctrl+F11';
                    Image = ReleaseDoc;
                    trigger OnAction()
                    begin
                        CheckDimensions();
                        TestField("Posting Date");
                        ClearValues();
                        CheckHeaderLines(Rec);
                        IF WorkflowManagement.CanExecuteWorkflow(Rec, allinoneCU.RunworkflowOnSendJournalVoucherforApprovalCode()) then
                            error('Workflow is enabled. You can not release manually.');

                        IF Status <> Status::Released then BEGIN
                            Status := Status::Released;
                            Modify();
                            Message('Document has been Released.');
                        end;
                    end;
                }
                action("Re&open")
                {
                    ApplicationArea = all;
                    Caption = 'Re&open';
                    Image = ReOpen;
                    trigger OnAction();
                    begin
                        IF Status <> Status::Released then
                            ERROR('Status must be Released');
                        RecordRest.Reset();
                        RecordRest.SetRange(ID, 50117);
                        RecordRest.SetRange("Record ID", Rec.RecordId());
                        IF RecordRest.FindFirst() THEN
                            error('This record is under in workflow process. Please cancel approval request if not required.');
                        IF Status <> Status::Open then BEGIN
                            Status := Status::Open;
                            Modify();
                            Message('Document has been Reopened.');
                        end;
                    end;
                }
                separator("---")
                {
                    Caption = '---';
                }
                action(Comments)
                {
                    Caption = 'Comments';
                    ApplicationArea = All;
                    RunObject = Page "Approval Comments";
                    RunPageLink = "Document Type" = FILTER('JV'),
                                  "Document No." = FIELD("Document No.");
                }
            }
            group("P&osting")
            {
                Caption = 'P&osting';

                action("P&ost")
                {
                    Caption = 'P&ost';
                    ShortCutKey = 'F11';
                    ApplicationArea = All;

                    trigger OnAction();
                    var
                        DocAttmtExt: Codeunit "Document Attachment Ext";
                    begin
                        // SAA 3.0 >>
                        CheckHeaderLines(Rec);
                        // SAA 3.0 <<
                        VoucherPost.RUN(Rec);
                        //g2s>>>>> TransferAttachment  150124 `
                        IF DocAttmtExt.IsAttachmentsEnabled() THEN BEGIN
                            MoveAttachment(Rec);

                            CopyLinksAndNotes(Rec);
                        END;
                        //g2s>>>>>>>>>>>>>>>>>>>> 150124
                    end;
                }
                action(Preview)
                {
                    ApplicationArea = all;
                    Caption = 'Preview';
                    ShortCutKey = 'Shift+F2';

                    trigger OnAction();
                    var
                        GeneralJrnlBtchLRec: Record "Gen. Journal Batch";
                        DocumentNo: Code[20];
                        GLRegGRec: Record "G/L Register";
                        genJounlin: Record "Gen. Journal Line 2";
                        VoucherPreview: Codeunit "Voucher Preview Posting";
                    begin
                        DocumentNo := "Document No.";
                        ClearValues();
                        VoucherPreview.RUN(Rec);
                    end;
                }
                action("Post and &Print")
                {
                    Caption = 'Post and &Print';
                    ApplicationArea = All;
                    ShortCutKey = 'Shift+F11';

                    trigger OnAction();
                    var
                        DocAttmtExt: Codeunit "Document Attachment Ext";
                    begin
                        // SAA 3.0 >>
                        CheckHeaderLines(Rec);
                        // SAA 3.0 <<
                        VoucherPost.RUN(Rec);
                        //g2s>>>>> TransferAttachment  150124 `
                        IF DocAttmtExt.IsAttachmentsEnabled() THEN BEGIN
                            MoveAttachment(Rec);

                            CopyLinksAndNotes(Rec);
                        END;
                        //g2s>>>>>>>>>>>>>>>>>>>> 150124
                    end;
                }
            }
        }
        area(processing)
        {
            action("&Print")
            {
                Caption = '&Print';
                Ellipsis = true;
                //Enabled = false;
                Promoted = true;
                PromotedCategory = Process;

                trigger OnAction();
                begin
                    VoucherHeader.SETRANGE("Voucher Type", "Voucher Type");
                    VoucherHeader.SETRANGE("Document No.", "Document No.");
                    if VoucherHeader.FINDFIRST then
                        REPORT.RUN(50078, true, false, VoucherHeader);
                end;
            }
        }
    }

    trigger OnAfterGetRecord();
    begin
        if "Account Type" = "Account Type"::"Bank Account" then begin
            if BankAc.GET("Account No.") then begin
                BankAc.CALCFIELDS("Balance (LCY)");
                "Account Balance" := BankAc."Balance (LCY)";
            end;
        end else
            if "Account Type" = "Account Type"::Vendor then begin
                if VendR.GET("Account No.") then begin
                    VendR.CALCFIELDS("Balance (LCY)");
                    "Account Balance" := ABS(VendR."Balance (LCY)");
                end;
            end;
        CALCFIELDS("Amount (dbr.)");
        BalanceP := "Account Balance" - "Amount (dbr.)";

        OpenAppEntrExistsForCurrUser := approvalmngmt.HasOpenApprovalEntriesForCurrentUser(RecordId());
        OpenApprEntrEsists := approvalmngmt.HasOpenApprovalEntries(RecordId());
        CanCancelapprovalforrecord := approvalmngmt.CanCancelApprovalForRecord(RecordId());
        workflowwebhookmangt.GetCanRequestAndCanCancel(RecordId(), CanrequestApprovForFlow, CanCancelapprovalforflow);
    end;

    trigger OnDeleteRecord(): Boolean;
    begin
        DelDocNoArchive.ArchiveNo("Document No.", 7, TODAY, TIME, USERID, DATABASE::"Voucher Header"); //HO1.0
    end;

    trigger OnInsertRecord(BelowxRec: Boolean): Boolean;
    begin
        "Branch CPV" := true;
    end;

    trigger OnNewRecord(BelowxRec: Boolean);
    Var
        ResponsibilityCenterLRec: Record "Responsibility Center";
        ResponsibilityPage: Page "Responsibility Center List";
    begin
        //CRF:2019-0042 NYO 15-05-19 >>
        //"Dim. Document Type" := "Dim. Document Type" :: JV;
        //"Document Date" := TODAY;
        //"JV Type" := "JV Type"::BRJV;
        //"Branch CPV" := TRUE;

        /*         CLEAR(ResponsibilityCode);
                Win.OPEN('Enter the Responsibility Center #1######################\');
                //Win.INPUT(1,ResponsibilityCode);CHI 9.0
                Win.CLOSE; */
        Clear(ResponsibilityCode);
        Clear(ResponsibilityPage);
        ResponsibilityPage.LookupMode(true);
        ResponsibilityPage.RunModal();
        ResponsibilityPage.SetSelection(ResponsibilityCenterLRec);
        IF ResponsibilityCenterLRec.FindFirst() then
            ResponsibilityCode := ResponsibilityCenterLRec.Code;
        if not RespCent2.GET(ResponsibilityCode) then
            ERROR('%1 is Invalid Responsibility Center!', ResponsibilityCode);

        if ResponsibilityCode <> 'LOS' then begin
            "Responsibility Center" := ResponsibilityCode;
            "Dim. Document Type" := "Dim. Document Type"::JV;
            "Document Date" := TODAY;
            "Posting Date" := Today;
            "JV Type" := "JV Type"::BRJV;
            "Branch CPV" := true;
        end;


        if ResponsibilityCode = 'LOS' then begin
            "Responsibility Center" := ResponsibilityCode;
            "Dim. Document Type" := "Dim. Document Type"::JV;
            "Document Date" := TODAY;
            "Posting Date" := Today;
            "JV Type" := "JV Type"::LBSV;
            "Branch CPV" := true;
        end;
        //CRF:2019-0042 NYO 15-05-19 <<
    end;

    trigger OnOpenPage();
    begin
        //ERROR('This Form is in Developmnet Statge please try after some time.!');
        //"Account Type" := "Account Type"::"Bank Account";

        //added to filter responsibility center SAA3.0 >>
        BuildFilter := RespCentFilter.BuildRespCentFilter;
        if BuildFilter <> '' then begin
            BuildFilter := QT + QT + '|' + BuildFilter;
            FILTERGROUP(2);
            SETFILTER("Responsibility Center", BuildFilter);
            FILTERGROUP(0);
        end;
        //SAA3.0 <<
    end;

    trigger OnModifyRecord(): Boolean
    BEGIN
        TestField(Status, Status::Open);
    END;

    var
        VoucherHeader: Record "Voucher Header";
        ApprovalMgt: Codeunit 1535;
        ApprovalEntry: Record "Approval Entry";
        ReleaseVoucher: Codeunit "Voucher Release";
        ApprovalEntries: Page 658;
        ChangeExchangeRate: Page "Change Exchange Rate";
        GenJrnlLine: Record "Gen. Journal Line";
        ReportPrint: Codeunit "Test Report-Print";
        VoucherPost: Codeunit "Voucher Post";
        DelDocNoArchive: Codeunit "Deleted Doc. No. Archive";
        UserSetup: Record "User Setup";
        UserIDRespCent: Record "UserID Resp. Cent. Lines";
        RespCent: Record "Responsibility Center";
        RespCentFilter: Codeunit "Responsibility Center Filter";
        BankAc: Record "Bank Account";
        VendR: Record Vendor;
        BalanceP: Decimal;
        BuildFilter: Text[200];
        QT: Label '''';
        Win: Dialog;
        RespCent2: Record "Responsibility Center";
        ResponsibilityCode: Code[10];
        WorkflowManagement: Codeunit "Workflow Management";
        allinoneCU: codeunit IJLSubEvents;
        RecordRest: record "Restricted Record";
        approvalmngmt: Codeunit "Approvals Mgmt.";
        OpenApprEntrEsists: Boolean;
        CanrequestApprovForFlow: Boolean;
        CanCancelapprovalforrecord: Boolean;
        CanCancelapprovalforflow: Boolean;
        OpenAppEntrExistsForCurrUser: Boolean;
        workflowwebhookmangt: Codeunit "Workflow Webhook Management";

    procedure CheckHeaderLines(VoucherHeaderRec: Record "Voucher Header");
    var
        GenJnlLine: Record "Gen. Journal Line 2";//PK-GJ2
        VendorLedgerEntry: Record "Vendor Ledger Entry";
        Vendor: Record Vendor;
        Text50200: Label 'Maturity Date must not be blank for this line no - %1';
        Text50201: Label 'The %1 in Journal Header must be zero';
        Text50202: Label 'There is no Journals lines to approve';
    begin
        with VoucherHeaderRec do begin
            //TESTFIELD(Narration);
            TestField("Shortcut Dimension 1 Code");
            TestField("Shortcut Dimension 2 Code");
            GenJnlLine.SETRANGE(GenJnlLine."Journal Template Name", "Journal Template Code");
            GenJnlLine.SETRANGE(GenJnlLine."Journal Batch Name", "Journal Batch Name");
            GenJnlLine.SETRANGE(GenJnlLine."Document No.", "Document No.");
            if GenJnlLine.FIND('-') then begin
                CALCFIELDS("Amount (LCY)");
                if "Amount (LCY)" <> 0 then
                    ERROR(Text50201, FIELDCAPTION("Amount (LCY)"));
                repeat
                    GenJnlLine.TESTFIELD("Account No.");
                    GenJnlLine.TESTFIELD("Posting Date");
                    GenJnlLine.TESTFIELD(Amount);
                    GenJnlLine.TESTFIELD("Shortcut Dimension 1 Code");
                    GenJnlLine.TESTFIELD("Shortcut Dimension 2 Code");
                    //GenJnlLine.TESTFIELD("Description 2");
                    GenJnlLine.TESTFIELD(Narration);
                    //GenJnlLine.TESTFIELD("Description 2");
                    if GenJnlLine."Account Type" = GenJnlLine."Account Type"::"Fixed Asset" then
                        GenJnlLine.TESTFIELD("FA Posting Type");
                    if GenJnlLine."FA Posting Type" = GenJnlLine."FA Posting Type"::Maintenance then
                        GenJnlLine.TESTFIELD("Maintenance Code");
                    //Nyo
                    if GenJrnlLine."Account Type" = GenJrnlLine."Account Type"::"Fixed Asset" then
                        GenJnlLine.TESTFIELD("FA Posting Type");
                    if (GenJnlLine."Account Type" = GenJnlLine."Account Type"::Customer)
                      or (GenJnlLine."Bal. Account Type" = GenJnlLine."Bal. Account Type"::Customer) then
                        GenJnlLine.TESTFIELD("Responsibility Center");
                    /*if GenJnlLine."FA Posting Type" = GenJnlLine."FA Posting Type" ::"capital work in progress" then begin
                      //TESTFIELD("Shortcut Dimension 4 Code");
                      GenJnlLine.TESTFIELD("Capex No.");
                      GenJnlLine.TESTFIELD("Capex Line No.");
                    end;*///CHI 9.0
                    if GenJnlLine."FA Posting Type" = GenJnlLine."FA Posting Type"::"Acquisition Cost" then begin
                        GenJnlLine.TESTFIELD("Capex No.");
                        GenJnlLine.TESTFIELD("Capex Line No.");
                    end;
                    //Nyo
                    if GenJnlLine."Account Type" in [GenJnlLine."Account Type"::"G/L Account",
                      GenJnlLine."Account Type"::"Bank Account"] then begin
                        GenJnlLine.TESTFIELD("Gen. Bus. Posting Group", '');
                        GenJnlLine.TESTFIELD("Gen. Prod. Posting Group", '');
                        GenJnlLine.TESTFIELD("Gen. Posting Type", 0);
                        GenJnlLine.TESTFIELD("VAT Bus. Posting Group", '');
                        GenJnlLine.TESTFIELD("VAT Prod. Posting Group", '');
                        GenJnlLine.TESTFIELD("Bal. Gen. Bus. Posting Group", '');
                        GenJnlLine.TESTFIELD("Bal. Gen. Prod. Posting Group", '');
                        GenJnlLine.TESTFIELD("Bal. VAT Bus. Posting Group", '');
                        GenJnlLine.TESTFIELD("Bal. VAT Prod. Posting Group", '');
                    end;
                    if (GenJnlLine."Bank Doc. Type" in [3]) and (GenJnlLine."Maturity Date" = 0D) then
                        ERROR(Text50200, GenJnlLine."Line No.");
                    if GenJnlLine."Loan ID" <> '' then
                        GenJnlLine.TESTFIELD("Applies-to Doc. No.");

                    //added to mandate currency code for Import Vendors. CRN:154
                    if GenJnlLine."Account Type" = GenJnlLine."Account Type"::Vendor then begin
                        Vendor.GET(GenJnlLine."Account No.");
                        if Vendor."Vendor Type" = Vendor."Vendor Type"::Import then
                            if Vendor."Currency Code" <> '' then begin
                                if GenJnlLine."Currency Code" <> Vendor."Currency Code" then
                                    ERROR('Currency Code must be %1 on line %2 for this vendor %3', Vendor."Currency Code", GenJnlLine."Line No.",
                                      Vendor.Name);
                            end else
                                //ERROR('Currency Code must not be blank for this vendor %1',Vendor.Name);END;  //<<
                                GenJnlLine.TESTFIELD("Currency Code");
                    end;
                until GenJnlLine.NEXT = 0;
            end else
                ERROR(Text50202);
        end;
    end;

    procedure PostingDateValidate();
    var
        Text50201: Label 'Posting Date must not be a future date';
    begin
        if "Posting Date" > TODAY then
            ERROR(Text50201);
    end;

    procedure AccountNoValidate();
    var
        GenJnlLine: Record "Gen. Journal Line 2";//PK-GJ2
        Text50200: Label 'Please use purchase journal to make vendor corrections';
        Text50201: Label 'Please use sales journal to make customer corrections';
    begin
        if "Account Type" = "Account Type"::Vendor then
            ERROR(Text50200) else

            if "Account Type" = "Account Type"::Customer then
                ERROR(Text50201);

        //IF "Bal. Account No." <> 'BKLOGTR01' THEN
        //IF "Account Type" = "Account Type"::"Bank Account" THEN
        //TESTFIELD("Bank Doc. Type");

        //IF ("Account Type"="Account Type"::"G/L Account") AND ("Bal. Account Type" = "Bal. Account Type"::"G/L Account") THEN
        //"Bank Doc. Type" := 0;
    end;

    procedure AccountTypeValidate();
    var
        GenJnlLine: Record "Gen. Journal Line";
        Text50200: Label 'Please use purchase journal to make vendor corrections';
        Text50201: Label 'Please use sales journal to make customer corrections';
    begin
        if "Account Type" = "Account Type"::Vendor then
            ERROR(Text50200) else

            if "Account Type" = "Account Type"::Customer then
                ERROR(Text50201);

        //IF "Bal. Account No." <> 'BKLOGTR01' THEN
        //IF "Account Type" = "Account Type"::"Bank Account" THEN
        //TESTFIELD("Bank Doc. Type");

        //IF ("Account Type"="Account Type"::"G/L Account") AND ("Bal. Account Type" = "Bal. Account Type"::"G/L Account") THEN
        //  "Bank Doc. Type" := 0;
    end;

    local procedure AccountNoOnAfterValidate();
    begin
        CurrPage.UPDATE;
    end;

    local procedure CheckDimensions()
    var
        GenJnlLine: Record "Gen. Journal Line 2";
    begin
        TestField("Shortcut Dimension 1 Code");
        TestField("Shortcut Dimension 2 Code");
        GenJnlLine.Reset();
        GenJnlLine.SetRange("Journal Batch Name", "Journal Template Code");
        GenJnlLine.SetRange("Journal Batch Name", "Journal Batch Name");
        GenJnlLine.SetRange("Document No.", "Document No.");
        //GenJnlLine.SetRange("Voucher type", GenJnlLine."Voucher type"::JV);
        if GenJnlLine.FindSet() then
            repeat
                GenJnlLine.TestField("Shortcut Dimension 1 Code");
                GenJnlLine.TestField("Shortcut Dimension 2 Code");
                GenJnlLine.TestField(Amount);

            until GenJnlLine.Next() = 0;
    end;

    //G2s moveAttachment 170224
    /// <summary>
    /// MoveAttachment.
    /// </summary>
    /// <param name="VoucHdr">VAR Record "Voucher Header".</param>
    procedure MoveAttachment(var VoucHdr: Record "Voucher Header")
    var

    begin
        PostedVoucherHdr.Reset();
        PostedVoucherHdr.SetRange("Voucher No.", VoucHdr."Document No.");
        if PostedVoucherHdr.FindFirst() then begin
            DocumentAttachment.Reset();
            DocumentAttachment.SetRange("No.", PostedVoucherHdr."Voucher No.");
            If DocumentAttachment.FindFirst() then begin
                repeat
                    DocumentAttachment2.Init();
                    DocumentAttachment2.TransferFields(DocumentAttachment);
                    DocumentAttachment2."Table ID" := 50118;
                    DocumentAttachment2."No." := PostedVoucherHdr."Document No.";
                    DocumentAttachment2.Insert(true);
                until DocumentAttachment.Next() = 0;
            end;
        end;
    end;





    //G2s Transfer Attachment 170224
    /// <summary>
    /// CopyLinksAndNotes.
    /// </summary>
    /// <param name="VoucherHdr">VAR Record "Voucher Header".</param>
    procedure CopyLinksAndNotes(var VoucherHdr: Record "Voucher Header")
    var
        rec_JournalApprovedPage: Page 50299;
        RecordLink, RecordLink2 : Record "Record Link";
        RecRef: RecordRef;
        NoteText: BigText;
        PostedVoucherRecID: RecordId;
        Stream: InStream;
        PostedVoucherHdr: Record "Posted Voucher Header";
        postedVHrNo: Code[20];

    begin
        // VoucherHdr.get("Document No.");
        //postedVHrNo := VoucherHdr."Document No.";
        VoucherHdr.SetRecFilter();
        clear(RecRef);
        RecRef.GetTable(VoucherHdr);
        // RecRef.FindFirst();
        PostedVoucherHdr.Reset();
        PostedVoucherHdr.SetRange("Voucher No.", VoucherHdr."Document No.");
        if PostedVoucherHdr.FindFirst() then begin
            PostedVoucherRecID := PostedVoucherHdr.RecordId;
        end;
        RecordLink.Reset();
        RecordLink.SetCurrentKey("Record ID");
        RecordLink.SetRange("Record ID", RecRef.RecordId);
        If RecordLink.FindSet() then begin
            repeat
                RecordLink2.Reset();
                RecordLink2.Copy(RecordLink);
                RecordLink2."Record ID" := PostedVoucherRecID;
                RecordLink2.Modify();
            until RecordLink.Next() = 0;
        end;


    end;

    var
        DocumentAttachment, DocumentAttachment2 : Record "Document Attachment";
        PostedVoucherHdr: Record "Posted Voucher Header";
}

