﻿<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2016/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="DataSource">
      <ConnectionProperties>
        <DataProvider>SQL</DataProvider>
        <ConnectString />
      </ConnectionProperties>
      <rd:SecurityType>None</rd:SecurityType>
    </DataSource>
  </DataSources>
  <ReportSections>
    <ReportSection>
      <Body>
        <Height>2in</Height>
        <Style />
      </Body>
      <Width>6.5in</Width>
      <Page>
        <Style />
      </Page>
    </ReportSection>
  </ReportSections>
  <Code>Public Function BlankZero(ByVal Value As Decimal)
    if Value = 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankPos(ByVal Value As Decimal)
    if Value &gt; 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankZeroAndPos(ByVal Value As Decimal)
    if Value &gt;= 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankNeg(ByVal Value As Decimal)
    if Value &lt; 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankNegAndZero(ByVal Value As Decimal)
    if Value &lt;= 0 then
        Return ""
    end if
    Return Value
End Function
</Code>
  <Language>=User!Language</Language>
  <ConsumeContainerWhitespace>true</ConsumeContainerWhitespace>
  <rd:ReportUnitType>Inch</rd:ReportUnitType>
  <rd:ReportID>0eeb6585-38ae-40f1-885b-8d50088d51b4</rd:ReportID>
  <DataSets>
    <DataSet Name="DataSet_Result">
      <Fields>
        <Field Name="TransactionID">
          <DataField>TransactionID</DataField>
        </Field>
        <Field Name="ReceiptNo">
          <DataField>ReceiptNo</DataField>
        </Field>
        <Field Name="Date">
          <DataField>Date</DataField>
        </Field>
        <Field Name="Outlet">
          <DataField>Outlet</DataField>
        </Field>
        <Field Name="Staff">
          <DataField>Staff</DataField>
        </Field>
        <Field Name="Transactor">
          <DataField>Transactor</DataField>
        </Field>
        <Field Name="CashAmount">
          <DataField>CashAmount</DataField>
        </Field>
        <Field Name="CashAmountFormat">
          <DataField>CashAmountFormat</DataField>
        </Field>
        <Field Name="CardAmount">
          <DataField>CardAmount</DataField>
        </Field>
        <Field Name="CardAmountFormat">
          <DataField>CardAmountFormat</DataField>
        </Field>
        <Field Name="ChequeAmount">
          <DataField>ChequeAmount</DataField>
        </Field>
        <Field Name="ChequeAmountFormat">
          <DataField>ChequeAmountFormat</DataField>
        </Field>
        <Field Name="BankAmount">
          <DataField>BankAmount</DataField>
        </Field>
        <Field Name="BankAmountFormat">
          <DataField>BankAmountFormat</DataField>
        </Field>
        <Field Name="WebAmount">
          <DataField>WebAmount</DataField>
        </Field>
        <Field Name="WebAmountFormat">
          <DataField>WebAmountFormat</DataField>
        </Field>
        <Field Name="Remarks">
          <DataField>Remarks</DataField>
        </Field>
        <Field Name="Line_ItemID">
          <DataField>Line_ItemID</DataField>
        </Field>
        <Field Name="Line_Qty">
          <DataField>Line_Qty</DataField>
        </Field>
        <Field Name="Line_QtyFormat">
          <DataField>Line_QtyFormat</DataField>
        </Field>
        <Field Name="Line_Price">
          <DataField>Line_Price</DataField>
        </Field>
        <Field Name="Line_PriceFormat">
          <DataField>Line_PriceFormat</DataField>
        </Field>
        <Field Name="Line_Discount">
          <DataField>Line_Discount</DataField>
        </Field>
        <Field Name="Line_DiscountFormat">
          <DataField>Line_DiscountFormat</DataField>
        </Field>
        <Field Name="Line_Tax">
          <DataField>Line_Tax</DataField>
        </Field>
        <Field Name="Line_TaxFormat">
          <DataField>Line_TaxFormat</DataField>
        </Field>
        <Field Name="Line_Total">
          <DataField>Line_Total</DataField>
        </Field>
        <Field Name="Line_TotalFormat">
          <DataField>Line_TotalFormat</DataField>
        </Field>
        <Field Name="PaymentType">
          <DataField>PaymentType</DataField>
        </Field>
        <Field Name="PaymentRef">
          <DataField>PaymentRef</DataField>
        </Field>
        <Field Name="PaymentAmount">
          <DataField>PaymentAmount</DataField>
        </Field>
        <Field Name="PaymentAmountFormat">
          <DataField>PaymentAmountFormat</DataField>
        </Field>
        <Field Name="AccountID">
          <DataField>AccountID</DataField>
        </Field>
        <Field Name="AccountNo">
          <DataField>AccountNo</DataField>
        </Field>
      </Fields>
      <Query>
        <DataSourceName>DataSource</DataSourceName>
        <CommandText />
      </Query>
    </DataSet>
  </DataSets>
</Report>