page 50299 "Approved General JVs"
{
    // version CHI6.0

    // PROJECT : CHI 6.0
    // **********************************************************************************
    // SIGN
    // **********************************************************************************
    // CHI     :  CHI 6.0 Developments
    // UNL     :  Univision Nigeria Limited
    // SAA     :  SAHEED ADIO ADEOSUN
    // HO      :  Henry <PERSON>ben
    // **********************************************************************************
    // VER      SIGN         DATE          DESCRIPTION
    // **********************************************************************************
    // 1.0      UNL      06-Dec-11      -> Form Created to display journal voucher.
    // 1.0      HO       07-Sep-12   -> Code added to "Form-OnDelereRecord()" to allow Archive of deleted Journal Voucher Document No.
    // CRF:2019-0042  NYO 21-05-19   -> Set table view Property added a new oprion "LBSV"

    Editable = false;
    PageType = Document;
    SourceTable = "Voucher Header";
    SourceTableView = WHERE("Voucher Type" = FILTER(JV),
                            "JV Type" = FILTER(" " | General | BRJV | LBSV),
                            Status = FILTER(Released));
    layout
    {
        area(content)
        {
            group(General)
            {
                Caption = 'General';
                field("Document No."; "Document No.")
                {
                    ApplicationArea = all;
                    trigger OnAssistEdit();
                    begin
                        if AssistEdit(xRec) then
                            CurrPage.UPDATE;
                    end;
                }
                field("Document Date"; "Document Date")
                {
                    Editable = false;
                    ApplicationArea = all;
                }
                field(Narration; Narration)
                {
                    ApplicationArea = all;
                }
                field("Amount (LCY)"; "Amount (LCY)")
                {
                    ApplicationArea = all;
                }
                field(Status; Status)
                {
                    ApplicationArea = all;
                }
            }
            part(VoucherLines; "Appr. Gen. JVs Subform")
            {
                SubPageLink = "Journal Template Name" = FIELD("Journal Template Code"),
                              "Journal Batch Name" = FIELD("Journal Batch Name"),
                              "Document No." = FIELD("Document No.");
                ApplicationArea = all;
            }
            group(Usertrail)
            {
                Caption = 'Usertrail';
                field("Created By"; "Created By")
                {
                    ApplicationArea = all;
                }
                field("Created By Name"; "Created By Name")
                {
                    ApplicationArea = all;
                }
                field("Created Date"; "Created Date")
                {
                    ApplicationArea = all;
                }
                field("Created Time"; "Created Time")
                {
                    ApplicationArea = all;
                }
                field("Modified By"; "Modified By")
                {
                    ApplicationArea = all;
                }
                field("Modified By Name"; "Modified By Name")
                {
                    ApplicationArea = all;
                }
                field("Modified Date"; "Modified Date")
                {
                    ApplicationArea = all;
                }
                field("Modified Time"; "Modified Time")
                {
                    ApplicationArea = all;
                }
            }
        }
        area(FactBoxes)
        {
            part("Attached Documents"; "Document Attachment Factbox")
            {
                ApplicationArea = All;
                Caption = 'Attachments';
                SubPageLink = "Table ID" = CONST(50117),
                                "No." = FIELD("Document No.");
                // Type = FIELD("Voucher Type");
            }
            systempart(PyamentTermsLinks; Links)
            {
                ApplicationArea = RecordLinks;
            }
            systempart(PyamentTermsNotes; Notes)
            {
                ApplicationArea = Notes;
            }
        }
        //g2s29Dev23
    }

    actions
    {
        area(navigation)
        {
            group("&Voucher")
            {
                Caption = '&Voucher';
                separator(Separator1000000067)
                {
                }
                action(Dimensions)
                {
                    Caption = 'Dimensions';
                    ApplicationArea = all;

                    trigger OnAction();
                    begin
                        ShowDocDim;
                    end;
                }
                separator(Separator1000000069)
                {
                }
                action(Approvals)
                {
                    Caption = 'Approvals';
                    ApplicationArea = all;
                    trigger OnAction();
                    begin
                        ApprovalEntries.Setfilters(DATABASE::"Voucher Header", 7, "Document No.");
                        ApprovalEntries.RUN;
                    end;
                }
            }
            group("F&unctions")
            {
                Caption = 'F&unctions';

                action("Send A&pproval Request")
                {
                    Caption = 'Send A&pproval Request';
                    ApplicationArea = all;
                    trigger OnAction();
                    begin
                        TestField("Posting Date");
                        // SAA 3.0 >>
                        CheckHeaderLines(Rec);
                        // SAA 3.0 <<
                        //IF ApprovalMgt.SendVoucherApprovalRequest(Rec) THEN;
                    end;
                }
                action("Cancel Approval Re&quest")
                {
                    Caption = 'Cancel Approval Re&quest';
                    ApplicationArea = all;
                    trigger OnAction();
                    begin
                        //IF ApprovalMgt.CancelVoucherApprovalRequest(Rec,TRUE,FALSE) THEN;
                    end;
                }
                separator(Separator1000000049)
                {
                }
                action("Re&lease")
                {
                    ApplicationArea = all;
                    Caption = 'Re&lease';
                    ShortCutKey = 'Ctrl+F11';
                    Visible = false;
                    trigger OnAction();
                    begin
                        TestField("Posting Date");
                        PerformManualRelease;
                    end;
                }
                action("Re&open")
                {
                    ApplicationArea = all;
                    Caption = 'Re&open';
                    Image = ReOpen;
                    trigger OnAction();
                    begin
                        RecordRest.Reset();
                        RecordRest.SetRange(ID, 50117);
                        RecordRest.SetRange("Record ID", Rec.RecordId());
                        IF RecordRest.FindFirst() THEN
                            error('This record is under in workflow process. Please cancel approval request if not required.');
                        IF Status <> Status::Open then BEGIN
                            Status := Status::Open;
                            Modify();
                            Message('Document has been Reopened.');
                        end;
                    end;
                }
                separator("---")
                {
                    Caption = '---';
                }
                action(Comments)
                {
                    ApplicationArea = all;
                    Caption = 'Comments';
                    RunObject = Page "Approval Comments";
                    RunPageLink = "Document Type" = FILTER('JV'),
                                  "Document No." = FIELD("Document No.");
                }
                action("Update Segments")
                {
                    Caption = 'Update Segments';
                    ApplicationArea = all;
                    trigger OnAction();
                    begin
                        //UpdateSegments("Document No.");
                    end;
                }
            }
            action(Preview)
            {
                ApplicationArea = all;
                Caption = 'Preview';
                ShortCutKey = 'Shift+F2';
                trigger OnAction();
                var
                    GeneralJrnlBtchLRec: Record "Gen. Journal Batch";
                    DocumentNo: Code[20];
                    GLRegGRec: Record "G/L Register";
                    genJounlin: Record "Gen. Journal Line 2";
                    VoucherPreview: Codeunit "Voucher Preview Posting";
                begin
                    DocumentNo := "Document No.";
                    ClearValues();
                    VoucherPreview.RUN(Rec);
                end;
            }
            group("P&osting")
            {
                Caption = 'P&osting';
                action("Test Report")
                {
                    ApplicationArea = all;
                    Caption = 'Test Report';

                    trigger OnAction();
                    begin
                        JournalLine2.RESET;
                        JournalLine2.SETRANGE("Journal Template Name", "Journal Template Code");
                        JournalLine2.SETRANGE("Journal Batch Name", "Journal Batch Name");
                        JournalLine2.SETRANGE("Document No.", "Document No.");
                        //IF JournalLine.FINDFIRST THEN
                        ReportPrint.PrintGenJnlLine(JournalLine2);
                    end;
                }
                action("P&ost")
                {
                    Caption = 'P&ost';
                    ShortCutKey = 'F11';
                    ApplicationArea = all;
                    trigger OnAction();
                    Var
                        GeneralJrnlBtchLRec: Record "Gen. Journal Batch";
                        Text50201: Label 'The %1 in Journal Header must be zero';
                        DocAttmtExt: Codeunit "Document Attachment Ext";
                    begin
                        GeneralJrnlBtchLRec.Reset();
                        GeneralJrnlBtchLRec.SetRange("Journal Template Name", 'JV');
                        GeneralJrnlBtchLRec.SetRange(Name, 'Journal');
                        if GeneralJrnlBtchLRec.FindFirst() then begin
                            GeneralJrnlBtchLRec.TestField("No. Series", '');
                            GeneralJrnlBtchLRec.TestField("Posting No. Series", '');
                        end;
                        // SAA 3.0 >>
                        CheckHeaderLines(Rec);
                        // SAA 3.0 <<
                        VoucherPost.RUN(Rec);

                        //g2s>>>>> TransferAttachment  150124 `
                        IF DocAttmtExt.IsAttachmentsEnabled() THEN BEGIN
                            MoveAttachment(Rec);

                            CopyLinksAndNotes(Rec);
                        END;
                        //g2s>>>>>>>>>>>>>>>>>>>> 150124
                    end;
                }
                action("Post and &Print")
                {
                    Caption = 'Post and &Print';
                    ShortCutKey = 'Shift+F11';
                    ApplicationArea = all;
                    trigger OnAction();
                    var
                        GeneralJrnlBtchLRec: Record "Gen. Journal Batch";
                        DocAttmtExt: Codeunit "Document Attachment Ext";
                    begin
                        GeneralJrnlBtchLRec.Reset();
                        GeneralJrnlBtchLRec.SetRange("Journal Template Name", 'JV');
                        GeneralJrnlBtchLRec.SetRange(Name, 'Journal');
                        if GeneralJrnlBtchLRec.FindFirst() then begin
                            GeneralJrnlBtchLRec.TestField("No. Series", '');
                            GeneralJrnlBtchLRec.TestField("Posting No. Series", '');
                        end;
                        // SAA 3.0 >>
                        CheckHeaderLines(Rec);
                        // SAA 3.0 <<
                        VoucherPost.RUN(Rec);

                        //g2s>>>>> TransferAttachment  150124 `
                        IF DocAttmtExt.IsAttachmentsEnabled() THEN BEGIN
                            MoveAttachment(Rec);

                            CopyLinksAndNotes(Rec);
                        END;
                        //g2s>>>>>>>>>>>>>>>>>>>> 150124
                    end;
                }
            }
        }
        area(processing)
        {
            action("&Print")
            {
                Caption = '&Print';
                Ellipsis = true;
                Promoted = true;
                PromotedCategory = Process;
                ApplicationArea = all;

                trigger OnAction();
                begin
                    VoucherHeader.SETRANGE("Voucher Type", "Voucher Type");
                    VoucherHeader.SETRANGE("Document No.", "Document No.");
                    if VoucherHeader.FINDFIRST then
                        REPORT.RUN(50078, true, false, VoucherHeader);
                end;
            }
        }
    }

    trigger OnDeleteRecord(): Boolean;
    begin
        DelDocNoArchive.ArchiveNo("Document No.", 7, TODAY, TIME, USERID, DATABASE::"Voucher Header"); //HO1.0
    end;

    trigger OnNewRecord(BelowxRec: Boolean);
    begin
        "Dim. Document Type" := "Dim. Document Type"::JV;
    end;

    var
        VoucherHeader: Record "Voucher Header";
        ApprovalMgt: Codeunit "Approvals Mgmt.";
        ApprovalEntry: Record "Approval Entry";
        ReleaseVoucher: Codeunit "Voucher Release";
        ApprovalEntries: Page "Approval Entries";
        ChangeExchangeRate: Page "Change Exchange Rate";
        GenJrnlLine: Record "Gen. Journal Line";
        ReportPrint: Codeunit "Test Report-Print";
        VoucherPost: Codeunit "Voucher Post";
        DelDocNoArchive: Codeunit "Deleted Doc. No. Archive";
        JournalLine2: Record "Gen. Journal Line";
        RecordRest: record "Restricted Record";

    procedure CheckHeaderLines(VoucherHeaderRec: Record "Voucher Header");
    var
        GenJnlLine: Record "Gen. Journal Line 2";//PK-GJ2
        VendorLedgerEntry: Record "Vendor Ledger Entry";
        Vendor: Record Vendor;
        Text50200: Label 'Maturity Date must not be blank for this line no - %1';
        Text50201: Label 'The %1 in Journal Header must be zero';
        Text50202: Label 'There is no Journals lines to approve';
    begin
        with VoucherHeaderRec do begin
            TESTFIELD(Narration);
            GenJnlLine.SETRANGE(GenJnlLine."Journal Template Name", "Journal Template Code");
            GenJnlLine.SETRANGE(GenJnlLine."Journal Batch Name", "Journal Batch Name");
            GenJnlLine.SETRANGE(GenJnlLine."Document No.", "Document No.");
            if GenJnlLine.FIND('-') then begin
                CALCFIELDS("Amount (LCY)");
                if Narration <> 'Journal Correction' then
                    if "Amount (LCY)" <> 0 then
                        ERROR(Text50201, FIELDCAPTION("Amount (LCY)"));
                repeat
                    GenJnlLine.TESTFIELD("Account No.");
                    GenJnlLine.TESTFIELD("Posting Date");
                    GenJnlLine.TESTFIELD(Amount);
                    GenJnlLine.TESTFIELD("Shortcut Dimension 1 Code");
                    GenJnlLine.TESTFIELD("Shortcut Dimension 2 Code");

                    //GenJnlLine.TESTFIELD("Description 2");
                    if GenJnlLine."Account Type" = GenJnlLine."Account Type"::"Fixed Asset" then
                        GenJnlLine.TESTFIELD("FA Posting Type");
                    if GenJnlLine."FA Posting Type" = GenJnlLine."FA Posting Type"::Maintenance then
                        GenJnlLine.TESTFIELD("Maintenance Code");
                    if GenJnlLine."Account Type" in [GenJnlLine."Account Type"::"G/L Account",
                      GenJnlLine."Account Type"::"Bank Account"] then begin
                        GenJnlLine.TESTFIELD("Gen. Bus. Posting Group", '');
                        GenJnlLine.TESTFIELD("Gen. Prod. Posting Group", '');
                        GenJnlLine.TESTFIELD("Gen. Posting Type", 0);
                        GenJnlLine.TESTFIELD("VAT Bus. Posting Group", '');
                        GenJnlLine.TESTFIELD("VAT Prod. Posting Group", '');
                        GenJnlLine.TESTFIELD("Bal. Gen. Bus. Posting Group", '');
                        GenJnlLine.TESTFIELD("Bal. Gen. Prod. Posting Group", '');
                        GenJnlLine.TESTFIELD("Bal. VAT Bus. Posting Group", '');
                        GenJnlLine.TESTFIELD("Bal. VAT Prod. Posting Group", '');
                    end;
                    if (GenJnlLine."Bank Doc. Type" in [3]) and (GenJnlLine."Maturity Date" = 0D) then
                        ERROR(Text50200, GenJnlLine."Line No.");
                    if GenJnlLine."Loan ID" <> '' then
                        GenJnlLine.TESTFIELD("Applies-to Doc. No.");
                until GenJnlLine.NEXT = 0;
            end else
                ERROR(Text50202);
        end;
    end;

    procedure PostingDateValidate();
    var
        Text50201: Label 'Posting Date must not be a future date';
    begin
        if "Posting Date" > TODAY then
            ERROR(Text50201);
    end;

    procedure AccountNoValidate();
    var
        GenJnlLine: Record "Gen. Journal Line 2";//PK-GJ2
        Text50200: Label 'Please use purchase journal to make vendor corrections';
        Text50201: Label 'Please use sales journal to make customer corrections';
    begin
        if "Account Type" = "Account Type"::Vendor then
            ERROR(Text50200) else

            if "Account Type" = "Account Type"::Customer then
                ERROR(Text50201);

        //IF "Bal. Account No." <> 'BKLOGTR01' THEN
        //IF "Account Type" = "Account Type"::"Bank Account" THEN
        //TESTFIELD("Bank Doc. Type");

        //IF ("Account Type"="Account Type"::"G/L Account") AND ("Bal. Account Type" = "Bal. Account Type"::"G/L Account") THEN
        //"Bank Doc. Type" := 0;
    end;

    procedure AccountTypeValidate();
    var
        GenJnlLine: Record "Gen. Journal Line";
        Text50200: Label 'Please use purchase journal to make vendor corrections';
        Text50201: Label 'Please use sales journal to make customer corrections';
    begin
        if "Account Type" = "Account Type"::Vendor then
            ERROR(Text50200) else

            if "Account Type" = "Account Type"::Customer then
                ERROR(Text50201);

        //IF "Bal. Account No." <> 'BKLOGTR01' THEN
        //IF "Account Type" = "Account Type"::"Bank Account" THEN
        //TESTFIELD("Bank Doc. Type");

        //IF ("Account Type"="Account Type"::"G/L Account") AND ("Bal. Account Type" = "Bal. Account Type"::"G/L Account") THEN
        //  "Bank Doc. Type" := 0;
    end;

    /*
    procedure UpdateSegments(DocNo : Code[20]);
    var
        GenJournalL : Record "Gen. Journal Line";
    begin
        GenJournalL.RESET;
        GenJournalL.SETRANGE("Document No.",DocNo);
        if GenJournalL.FINDSET then
        repeat
          case GenJournalL."Shortcut Dimension 9 Code" of
            'CHICAP','CHIEXOTIC','CHIHAPPYHR','CHIICETEA','CHIVITA','SIPIT':
            begin
               GenJournalL."Shortcut Dimension 14 Code" := 'JNSD';
               GenJournalL.MODIFY;
            end;
            'CHICXL','CHIEVAMILK','CHIFLVMILK','CHIHEALTH','CHILIQCUST','CHIMILKY','CHIPLNMILK','CHIYOGHURT':
             begin
               GenJournalL."Shortcut Dimension 14 Code" := 'DAIRY';
               GenJournalL.MODIFY;
             end;
            'CHIROOVERS':
            begin
               GenJournalL."Shortcut Dimension 14 Code" := 'SNACKS';
               GenJournalL.MODIFY;
            end;
            'CHICOM':
             begin
               GenJournalL."Shortcut Dimension 14 Code" := 'COMMODITY';
               GenJournalL.MODIFY;
             end;
            'CHIFLUDOR','CHIMAIN','CHITGI','RETAILABJ','RETAILAJA','RETAILAPA','RETAILIKE','RETAILIKO','RETAILSUL':
             begin
               GenJournalL."Shortcut Dimension 14 Code" := 'SERVICES';
               GenJournalL.MODIFY;
            end;
            'BRCHIABA','BRCHIABJ','BRCHIBEN','BRCHIENG','BRCHIGOM','BRCHIHOC','BRCHIIBD','BRCHIKAD',
            'BRCHIKAN','BRCHIMAI','BRCHIMAK','BRCHIONI','BRCHIPHC','BRCHISOK','BRCHIWAR','BRCHIYOL','CHIBRANCH':
             begin
               GenJournalL."Shortcut Dimension 14 Code" := 'BRSALES';
               GenJournalL.MODIFY;
             end;
             'CHIRETSHOP','':
             begin
               GenJournalL."Shortcut Dimension 14 Code" := 'SERVICES';
               GenJournalL.MODIFY;
            end;

          end;
        until GenJournalL.NEXT = 0;
    end;
    */

    /// <summary>
    /// MoveAttachment.
    /// </summary>
    /// <param name="VoucHdr">VAR Record "Voucher Header".</param>
    procedure MoveAttachment(var VoucHdr: Record "Voucher Header")
    var

    begin
        PostedVoucherHdr.Reset();
        PostedVoucherHdr.SetRange("Voucher No.", VoucHdr."Document No.");
        if PostedVoucherHdr.FindFirst() then begin
            DocumentAttachment.Reset();
            DocumentAttachment.SetRange("No.", PostedVoucherHdr."Voucher No.");
            If DocumentAttachment.FindFirst() then begin
                repeat
                    DocumentAttachment2.Init();
                    DocumentAttachment2.TransferFields(DocumentAttachment);
                    DocumentAttachment2."Table ID" := 50118;
                    DocumentAttachment2."No." := PostedVoucherHdr."Document No.";
                    DocumentAttachment2.Insert(true);
                until DocumentAttachment.Next() = 0;
            end;
        end;
    end;

    /// <summary>
    /// CopyLinksAndNotes.
    /// </summary>
    /// <param name="VoucherHdr">VAR Record "Voucher Header".</param>
    procedure CopyLinksAndNotes(var VoucherHdr: Record "Voucher Header")
    var
        rec_JournalApprovedPage: Page 50299;
        RecordLink, RecordLink2 : Record "Record Link";
        RecRef: RecordRef;
        NoteText: BigText;
        PostedVoucherRecID: RecordId;
        Stream: InStream;
        PostedVoucherHdr: Record "Posted Voucher Header";
        postedVHrNo: Code[20];

    begin
        // VoucherHdr.get("Document No.");
        //postedVHrNo := VoucherHdr."Document No.";
        VoucherHdr.SetRecFilter();
        clear(RecRef);
        RecRef.GetTable(VoucherHdr);
        // RecRef.FindFirst();
        PostedVoucherHdr.Reset();
        PostedVoucherHdr.SetRange("Voucher No.", VoucherHdr."Document No.");
        if PostedVoucherHdr.FindFirst() then begin
            PostedVoucherRecID := PostedVoucherHdr.RecordId;
        end;
        RecordLink.Reset();
        RecordLink.SetCurrentKey("Record ID");
        RecordLink.SetRange("Record ID", RecRef.RecordId);
        If RecordLink.FindSet() then begin
            repeat
                RecordLink2.Reset();
                RecordLink2.Copy(RecordLink);
                RecordLink2."Record ID" := PostedVoucherRecID;
                RecordLink2.Modify();
            until RecordLink.Next() = 0;
        end;


    end;

    var
        RecordLinkType: Option Link,Note;
        LastLinkID: Integer;

        DocumentAttachment, DocumentAttachment2 : Record "Document Attachment";
        PostedVoucherHdr: Record "Posted Voucher Header";
}

