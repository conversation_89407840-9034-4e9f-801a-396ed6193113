enum 50341 "Exclusive JSND Distributor"
{
    Extensible = true;


    value(0; Juice)
    {
        caption = 'Juice';
    }
    value(1; Yoghurt)
    {
        caption = 'Yoghurt';
    }
    value(2; "Non JSND Dist")
    {
        Caption = 'Non JSND Dist';
    }
}
enum 50242 "Exclusive EVAP Distributor"
{
    Extensible = true;


    value(0; "Hollandia Evoprated Milk")
    {
        Caption = 'Hollandia Evoprated Milk';

    }
    value(1; "Non EVAP Distributor")
    {
        Caption = 'Non EVAP Distributor';
    }
    value(5; "UHT FULL CREAM MILK")
    {
        Caption = 'UHT FULL CREAM MILK';
    }
}
