table 50799 "Retail Products Log"

{
    DataClassification = ToBeClassified;
    Caption = 'Retail Product';

    fields
    {
        // General fields
        field(1; "Code"; Code[20])
        {
            Caption = 'Code';
            DataClassification = ToBeClassified;
        }

        field(2; "Description"; Text[100])
        {
            Caption = 'Description';
            DataClassification = ToBeClassified;
        }

        field(3; "Generic Name"; Text[100])
        {
            Caption = 'Generic Name';
            DataClassification = ToBeClassified;
        }

        field(4; "Manufacturer ID"; Code[20])
        {
            Caption = 'Manufacturer ID';
            DataClassification = ToBeClassified;
        }

        field(5; "Category ID"; Code[20])
        {
            Caption = 'Category ID';
            DataClassification = ToBeClassified;
        }

        field(6; "Cost"; Decimal)
        {
            Caption = 'Cost';
            DataClassification = ToBeClassified;
        }

        field(7; "Unit"; Text[10])
        {
            Caption = 'Unit';
            DataClassification = ToBeClassified;
        }

        field(8; "Recurring Qty"; Integer)
        {
            Caption = 'Recurring Quantity';
            DataClassification = ToBeClassified;
        }

        field(9; "Markup"; Decimal)
        {
            Caption = 'Markup';
            DataClassification = ToBeClassified;
        }

        field(10; "Price"; Decimal)
        {
            AutoFormatExpression = "Currency Code";
            AutoFormatType = 2;
            Caption = 'Price';
            MinValue = 0;
            // DataClassification = ToBeClassified;
        }

        field(11; "Taxable"; Boolean)
        {
            Caption = 'Taxable';
            DataClassification = ToBeClassified;
        }

        field(12; "Watched"; Boolean)
        {
            Caption = 'Watched';
            DataClassification = ToBeClassified;
        }

        field(13; "Online"; Boolean)
        {
            Caption = 'Online';
            DataClassification = ToBeClassified;
        }

        field(14; "Raw Material"; Boolean)
        {
            Caption = 'Raw Material';
            DataClassification = ToBeClassified;
        }

        field(15; "Produce"; Boolean)
        {
            Caption = 'Produce';
            DataClassification = ToBeClassified;
        }

        // Fields for Price Levels with "PL_" prefix
        field(16; "PL Description"; Text[100])
        {
            Caption = 'Price Level Description';
            DataClassification = ToBeClassified;
        }

        field(17; "PL Quantity"; Integer)
        {
            Caption = 'Price Level Quantity';
            DataClassification = ToBeClassified;
        }

        field(18; "PL Unit"; Text[10])
        {
            Caption = 'Price Level Unit';
            DataClassification = ToBeClassified;
        }

        field(19; "PL Price"; Decimal)
        {
            Caption = 'Price Level Price';
            DataClassification = ToBeClassified;
        }

        // Fields for Wholesales with "WS_" prefix
        field(20; "WS Description"; Text[100])
        {
            Caption = 'Wholesale Description';
            DataClassification = ToBeClassified;
        }

        field(21; "WS Quantity"; Integer)
        {
            Caption = 'Wholesale Quantity';
            DataClassification = ToBeClassified;
        }

        field(22; "WS Code"; Code[20])
        {
            Caption = 'Wholesale Code';
            DataClassification = ToBeClassified;
        }

        field(23; "WS Price"; Decimal)
        {
            Caption = 'Wholesale Price';
            DataClassification = ToBeClassified;
        }

        // Fields for Price Profiles with "PP_" prefix
        field(24; "PP Profile ID"; Code[20])
        {
            Caption = 'Price Profile ID';
            DataClassification = ToBeClassified;
        }

        field(25; "PP Price"; Decimal)
        {
            Caption = 'Price Profile Price';
            DataClassification = ToBeClassified;
        }
        field(26; Status; Enum APIStatus)
        {
            Caption = 'Status';
            DataClassification = ToBeClassified;
            trigger OnValidate()
            var
                myInt: Integer;
            begin

            end;
        }
        field(27; ProductID; Text[100])
        {
            Caption = 'Product ID';
            DataClassification = ToBeClassified;
            trigger OnValidate()
            var
                myInt: Integer;
            begin

            end;
        }
        field(28; ResponseMessage; Text[100])
        {
            Caption = 'Response Message';
            DataClassification = ToBeClassified;
            trigger OnValidate()
            var
                myInt: Integer;
            begin

            end;
        }
        field(29; ResponseSuccess; Boolean)
        {
            Caption = 'Response Success';
            DataClassification = ToBeClassified;
            trigger OnValidate()
            var
                myInt: Integer;
            begin

            end;
        }
        field(30; "WS Description1"; Text[100])
        {
            Caption = 'Wholesale Description 1';
            DataClassification = ToBeClassified;
        }

        field(31; "WS Quantity1"; Integer)
        {
            Caption = 'Wholesale Quantity 1';
            DataClassification = ToBeClassified;
        }

        field(32; "WS Code1"; Code[20])
        {
            Caption = 'Wholesale Code 1';
            DataClassification = ToBeClassified;
        }

        field(33; "WS Price1"; Decimal)
        {
            Caption = 'Wholesale Price 1';
            DataClassification = ToBeClassified;
        }
        field(34; "Item No."; Code[20])
        {
            Caption = 'Item No.';
            DataClassification = ToBeClassified;
        }
        field(35; "Currency Code"; Code[10])
        {
            Caption = 'Currency Code';
            Editable = false;
            TableRelation = Currency;
        }
        field(36; "Product ID"; Text[20])
        {
            Caption = 'Formular Item-Product ID';
        }
        field(37; "Quantity"; Integer)
        {
            Caption = 'Formular Item-Qty';
        }
        field(38; "Formular-Item"; boolean)
        {

        }
        field(39; "Allow Fraction"; Boolean)
        {

        }

    }

    keys
    {
        key(PK; "Code")
        {
            Clustered = true;
        }
    }

    trigger OnInsert();
    begin
        // Add logic if needed
    end;
}

