page 50383 "Posted Cash Peceipt Vouch List"
{
    // version CHI6.0

    // PROJECT : CHI 6.0
    // **********************************************************************************
    // SIGN
    // **********************************************************************************
    // CHI     :  CHI 6.0 Developments
    // UNL     :  Univision Nigeria Limited
    // SAA     :  SAHEED ADIO ADEOSUN
    // **********************************************************************************
    // VER      SIGN         DATE          DESCRIPTION
    // **********************************************************************************
    // 1.0      UNL      06-Dec-11      -> Form Created to display Posted voucher documents list.

    Editable = false;
    PageType = List;
    SourceTable = "Posted Voucher Header";
    UsageCategory = Lists;
    SourceTableView = WHERE("Voucher Type" = FILTER(CRV));
    CardPageId = "Posted Cash Receipt Voucher";
    Caption = 'Posted Cash Receipt Voucher List';

    layout
    {
        area(content)
        {
            repeater(Control1000000000)
            {
                field("Posting Date"; "Posting Date")
                {
                }
                field("Document No."; "Document No.")
                {
                }
                field("Voucher Type"; "Voucher Type")
                {
                }
                field("Receiving Code"; "Receiving Code")
                {
                }
                field("Account Type"; "Account Type")
                {
                }
                field("Created By Name"; "Created By Name")
                {
                }
                field("Teller / Cheque No."; "Teller / Cheque No.")
                {
                }
                field("Import File No."; "Import File No.")
                {
                }
                field("Created Time"; "Created Time")
                {
                }
                field("Posted By"; "Posted By")
                {
                }
                field("Posted By Name"; "Posted By Name")
                {
                }
                field("Posted Date"; "Posted Date")
                {
                }
                field("Posted Time"; "Posted Time")
                {
                }
                field("Created Date"; "Created Date")
                {
                }
                field("Created By"; "Created By")
                {
                }
                field("Account No."; "Account No.")
                {
                }
                field("Account Name"; "Account Name")
                {
                }
                field(Narration; Narration)
                {
                }
                field(Amount; Amount)
                {
                    Visible = false;
                }
                field("Amount (LCY)"; "Amount (LCY)")
                {
                    Visible = false;
                }
                field("Voucher No."; "Voucher No.")
                {
                }
                field("Approved By1"; "Approved By1")
                {
                    Visible = false;
                }
                field("Approved By2"; "Approved By2")
                {
                    Visible = false;
                }
                field("Approved By3"; "Approved By3")
                {
                    Visible = false;
                }
                field("Approved By4"; "Approved By4")
                {
                    Visible = false;
                }
            }
        }
        area(FactBoxes)
        {
            part("Attached Documents"; "Document Attachment Factbox")
            {
                ApplicationArea = All;
                Caption = 'Attachments';
                SubPageLink = "Table ID" = CONST(50118),
                                "No." = FIELD("Document No.");
                // Type = FIELD("Voucher Type");
            }
            systempart(PyamentTermsLinks; Links)
            {
                ApplicationArea = RecordLinks;
            }
            systempart(PyamentTermsNotes; Notes)
            {
                ApplicationArea = Notes;
            }
        }
        //g2s29Dev23
    }

    actions
    {
    }
}

