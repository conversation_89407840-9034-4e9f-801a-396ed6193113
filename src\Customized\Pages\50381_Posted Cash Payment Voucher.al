page 50381 "Posted Cash Payment Voucher"
{
    // version CHI6.0

    // PROJECT : CHI 6.0
    // **********************************************************************************
    // SIGN
    // **********************************************************************************
    // CHI     :  CHI 6.0 Developments
    // UNL     :  Univision Nigeria Limited
    // SAA     :  SAHEED ADIO ADEOSUN
    // **********************************************************************************
    // VER      SIGN         DATE          DESCRIPTION
    // **********************************************************************************
    // 1.0      UNL      06-Dec-11      -> Form Created to display Posted Cash Pmt Document Details.
    // 3.0      SAA      31-Jan-12      -> New TAB "Payment Details" created in form.
    // 3.0      SAA      18-Apr-17      -> Added codes to OnPush() of Print button to flag error if user does not have
    //                                     permission to reprint.

    DeleteAllowed = false;
    Editable = false;
    InsertAllowed = false;
    ModifyAllowed = false;
    PageType = Document;
    SourceTable = "Posted Voucher Header";
    SourceTableView = WHERE("Voucher Type" = FILTER(CPV));
    Caption = 'Posted Cash Payment Voucher';

    layout
    {
        area(content)
        {
            group(General)
            {
                Caption = 'General';
                field("Document No."; "Document No.")
                {
                }
                field("Posting Date"; "Posting Date")
                {
                }
                field("Account Type"; "Account Type")
                {
                }
                field("Account No."; "Account No.")
                {
                    Caption = 'Credit Account No.';
                }
                field("Account Name"; "Account Name")
                {
                }
                field(Narration; Narration)
                {
                }
                field("Currency Code"; "Currency Code")
                {
                }
                field("Amount (LCY)"; "Amount (LCY)")
                {
                }
                field("Voucher No."; "Voucher No.")
                {
                }
                field("Responsibility Center"; "Responsibility Center")
                {
                }
                field("Cash Requisition Slip No."; "Cash Requisition Slip No.")
                {
                    Editable = false;
                }
            }
            part(VoucherLines; "Posted Cash Pmt. Vchr. Subform")
            {
                SubPageLink = "Document No." = FIELD("Document No.");
                SubPageView = SORTING("Document No.");
            }
            group(Usertrail)
            {
                Caption = 'Usertrail';
                field("Created By"; "Created By")
                {
                }
                field("Created By Name"; "Created By Name")
                {
                }
                field("Created Date"; "Created Date")
                {
                }
                field("Created Time"; "Created Time")
                {
                }
                field("Posted By"; "Posted By")
                {
                }
                field("Posted By Name"; "Posted By Name")
                {
                }
                field("Posted Date"; "Posted Date")
                {
                }
                field("Posted Time"; "Posted Time")
                {
                }
                field("Modified By"; "Modified By")
                {
                }
                field("Modified By Name"; "Modified By Name")
                {
                }
                field("Modified Date"; "Modified Date")
                {
                }
                field("Modified Time"; "Modified Time")
                {
                }
            }
            group("Payment Details")
            {
                Caption = 'Payment Details';
                field(ToBeCollectedBy; ToBeCollectedBy)
                {
                }
                field(PaymentSettlementOf; PaymentSettlementOf)
                {
                }
                field("Payable to"; "Payable to")
                {
                }
                field("Payable Code"; "Payable Code")
                {

                    trigger OnLookup(var Text: Text): Boolean;
                    begin
                        if "Payable to" = "Payable to"::Customer then begin
                            if PAGE.RUNMODAL(22, Customer, Customer."No.") = ACTION::LookupOK then
                                "Payable Code" := Customer."No.";
                            "Payable Name" := Customer.Name;
                            /*IF NOT Customer."Approved Customer" THEN BEGIN
                               ERROR('THIS IS NOT AN APPROVED CUSTOMER');
                            END;
                            */
                        end;


                        if "Payable to" = "Payable to"::Vendor then begin
                            if PAGE.RUNMODAL(27, Vendor, Vendor."No.") = ACTION::LookupOK then
                                "Payable Code" := Vendor."No.";
                            "Payable Name" := Vendor.Name;
                        end;

                        if "Payable to" = "Payable to"::Staff then begin
                            if PAGE.RUNMODAL(5201, Employee, Employee."No.") = ACTION::LookupOK then
                                "Payable Code" := Employee."No.";
                            "Payable Name" := Employee."First Name";
                        end;

                        if "Payable to" = "Payable to"::Bank then begin
                            if PAGE.RUNMODAL(371, Bank, Bank."No.") = ACTION::LookupOK then
                                "Payable Code" := Bank."No.";
                            "Payable Name" := Bank.Name;
                        end;

                    end;
                }
                field("Payable Name"; "Payable Name")
                {
                }
                field("Cash Payments Options"; "Cash Payments Options")
                {
                }
                field("Cash Paid"; "Cash Paid")
                {
                }
                field("Cash Paid By"; "Cash Paid By")
                {
                    Editable = false;
                }
                field("Cash Paid On"; "Cash Paid On")
                {
                    Editable = false;
                }
                field("ABS(""Amount (LCY)"")"; ABS("Amount (LCY)"))
                {
                    Caption = 'Amount Paid';
                }
                field("Reprint CPV Slip"; "Reprint CPV Slip")
                {

                    trigger OnValidate();
                    begin
                        //PermCodeUnit.CheckAutority(USERID,609);
                        if "Reprint CPV Slip" then
                            if "Cash Paid" then begin
                                if CONFIRM(Text50202, true) then begin
                                    "Reprinted By" := USERID;
                                    "Reprinted On" := CURRENTDATETIME;
                                    //GenJounalBatchRec.GET("Journal Template Name","Journal Batch Name");
                                    //GenJounalBatchRec."Reprinted By":= ReprintedBy;
                                    //GenJounalBatchRec."Reprinted Date":= ReprintedDate;
                                    //GenJounalBatchRec.MODIFY;
                                    //GenJounalBatchRecCPV.SETRANGE(GenJounalBatchRecCPV."Journal Template Name","Journal Template Name");
                                    //GenJounalBatchRecCPV.SETRANGE(GenJounalBatchRecCPV.Name,"Journal Batch Name");
                                    //IF GenJounalBatchRecCPV.FINDFIRST THEN
                                    //REPORT.RUN(50465,FALSE,FALSE,GenJounalBatchRecCPV);
                                end;
                            end else
                                ERROR(Text50205);
                    end;
                }
                field("Reprinted By"; "Reprinted By")
                {
                    Editable = false;
                }
                field("Reprinted On"; "Reprinted On")
                {
                    Editable = false;
                }
            }
        }
        area(FactBoxes)
        {
            part("Attached Documents"; "Document Attachment Factbox")
            {
                ApplicationArea = All;
                Caption = 'Attachments';
                SubPageLink = "Table ID" = CONST(50118),
                                "No." = FIELD("Document No.");
                // Type = FIELD("Voucher Type");
            }
            systempart(PyamentTermsLinks; Links)
            {
                ApplicationArea = RecordLinks;
            }
            systempart(PyamentTermsNotes; Notes)
            {
                ApplicationArea = Notes;
            }
        }
        //g2s29Dev23
    }

    actions
    {
        area(navigation)
        {
            group("&Voucher")
            {
                Caption = '&Voucher';
                action(Dimensions)
                {
                    Caption = 'Dimensions';

                    trigger OnAction();
                    begin
                        ShowDocDim;
                    end;
                }
                action(Approvals)
                {
                    Caption = 'Approvals';

                    trigger OnAction();
                    var
                        PostedApprovalEntries: Page "Posted Approval Entries";
                    begin
                        //PostedApprovalEntries.Setfilters(DATABASE::"Posted Voucher Header","Document No.");
                        PostedApprovalEntries.RUN;
                    end;
                }
            }
        }
        area(processing)
        {
            action("&Print")
            {
                Caption = '&Print';
                Ellipsis = true;
                Promoted = true;
                PromotedCategory = Process;
                trigger OnAction();
                var
                    GLENt: Record "G/L Entry";
                begin
                    if UserSetup.GET(USERID) then BEGIN
                        if UserSetup."Reprint Payment Documents" then begin  //SAA3.0 >>
                            GLENt.RESET;
                            //GLENt.SETRANGE("Voucher Type", "Voucher Type");
                            GLENt.SETRANGE("Document No.", "Document No.");
                            if GLENt.FindSet() then
                                //message('%1', GLENt."Document No.");
                            REPORT.RUN(50496, true, false, GLENt);
                        end else
                            ERROR(TEXT001);
                    end;
                end;
            }
            action("&Navigate")
            {
                Caption = '&Navigate';
                Promoted = true;
                PromotedCategory = Process;

                trigger OnAction();
                begin
                    Navigate;
                end;
            }
        }
    }

    trigger OnOpenPage();
    begin
        // SAA 3.0 >>
        UserSetup.GET(USERID);
        if UserSetup.FilterResponsibilityCenter <> '' then begin
            FILTERGROUP(2);
            SETFILTER("Responsibility Center", UserSetup.FilterResponsibilityCenter);
            FILTERGROUP(0);
        end;
        // SAA 3.0 <<
    end;

    var
        PostedApprovalEntries: Page "Posted Approval Entries";
        VoucherHeader: Record "Posted Voucher Header";
        Vendor: Record Vendor;
        Customer: Record Customer;
        Employee: Record Employee;
        Bank: Record "Bank Account";
        Text50200: Label 'Status must be Released to pay Cash.';
        Text50201: Label 'You have already Printed the CPV Slip, you cannot Untick';
        Text50202: Label 'Do You Want to RePrint this CPV Slip ?\';
        Text50203: Label 'Cash Paid is False, hence you cannot Post.';
        Text50204: Label 'To be collected by must not be Blank';
        Text50205: Label 'You cannot Reprint while Cash Paid is False.';
        Text50206: Label 'Cash payment options must not be blank';
        Text50207: Label 'You cannot ReOpen Document while Cash Paid is True.';
        UserSetup: Record "User Setup";
        TEXT001: Label 'You do not have permission to reprint payment vouchers';

}

