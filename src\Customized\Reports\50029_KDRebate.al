report 50029 "KD Rebate Details" //CR220036-PKON22MA21 Whole report and Layout //PKON22JU29 //PKON22AU1 -whole Report
{
    UsageCategory = ReportsAndAnalysis;
    ApplicationArea = All;
    Caption = 'KD Rebate Details_50029';
    RDLCLayout = './KDRdetailspk.rdl';

    dataset
    {
        dataitem("Key Distributor Targets"; "Key Distributor Targets")
        {
            RequestFilterFields = "Customer No.";

            column(Shrinkage_Base_Amount; "Shrinkage Base Amount")
            { }
            column(MidEvapAch; MidEvapAch)
            { }
            column(ExclJsndAch; ExclJsndAch)
            { }
            column(ExclEvapAch; ExclEvapAch)
            { }
            column(MidEvapTarget; MidEvapTarget)
            { }
            column(ExclJsndTarget; ExclJsndTarget)
            { }
            column(ExclEvapTarget; ExclEvapTarget)
            { }
            column(MidEvapDisc; MidEvapDisc)
            { }
            column(ExclJsndDisc; ExclJsndDisc)
            { }
            column(ExclEvapDisc; ExclEvapDisc)
            { }
            column(Discount_group; ParTargetPeriod)
            {

            }
            column(Customer_No_; "Customer No.")
            {

            }
            column(Customer_Name; "Customer Name")
            {

            }

            //NewColumns Added for report G2S.....................250324
            column(DMS_Usage_Disc; "DMS_Usage Disc")
            {

            }

            column(StockNorm_Disc; "StockNorm Disc")
            {

            }

            column(Working_Cap_Disc; "Working Cap Disc")
            {

            }

            column(Van_Move_disc; "Van Move disc")
            {

            }
            //..................................................250324
            column(Global_Dimension_1_Code; "Global Dimension 1 Code")
            {

            }
            column(Monthly_Target_Value; "Monthly Target Value")
            {

            }
            column(Monthly_Total_Ach__Value; "Monthly Total Ach. Value")
            {

            }
            column(Flat_Discount; FD)
            {

            }
            column(Target_For_Category_A; "Target For Category A")
            {

            }
            column(Achieve_On_Category_A; "Achieve On Category A")
            {

            }
            column(Disc_On_Category_A; DISCA)
            {

            }
            column(Target_For_Category_B; "Target For Category B")
            {

            }
            column(Achieve_On_Category_B; "Achieve On Category B")
            {

            }
            column(Disc_On_Category_B; DISCB)
            {

            }
            column(Mixed_Focus_Brand_Target_Capr_; "Evap & JSND JuiceTarget - 1Ltr" + "Evap & JSND JuiceTarget - Cans" + "Evap&JSND JuiTarget- Caprisun" +
                "Evap&JSND Yoghurt Target-1lit" + "Evap&JSND Yoghurt Target-90ml" + "Evap&JSND HollandiaTarget-120g" + "EvapJSND Hollandia Target-190g" + "EvapJSND Holla Target-UHT" + "EvapJSND Juice Target-315ml" + "EvapJSND Juice Target-Pet" + "EvapJSND Yoghurt Target-315ml") //G2S Added "EvapJSND Hollandia Target-190g"
            {

            }
            column(Achieve_On_Mixed_Capr; MixdAch)
            {

            }
            column(Mixed_Focus_Brand_Disc_Capr_; MixdD)
            {

            }
            column(MixedPer; MixedPer)
            { }
            column(Evap_Excl__60G_Target; "Excl JSND Juice Targe 1-lit" + "Excl JSND JuiceTarget -cans" + "ExclJSNDJuiceTarget -capricun" + "Excl JSND Yoghurt Target-1lit" + "Excl JSND Yoghurt Target-90ml")
            {

            }
            column(Achieve_On_Evap_120G; EvapAch)
            {

            }
            column(EvaPer; EvaPer)
            { }
            column(Evap_Excl__120G_Disc; EVAPD)
            {

            }
            //G2s>>>>>>>Added 190gFC into the formula>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>020224
            column(JNSD_Capr_Target; "ExclEVAP Holla Target-190g Fc" + "ExclEVAP Holla Target-120g Fc" + "ExclEVAP Holla Target-120gslim" + "ExclEVAP Holla Target-50g Fc" + "ExclEVAP Holla Target-50gslim" + "ExclEVAP Holla Target-UHT MILK")
            {

            }
            //G2s>>>>>>>Added 190gFC into the formula>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>020224
            column(Achieve_On_JSND_Capr; JSDAch)
            {

            }
            column(JNSD_Capr_Disc; JSNDDic)
            {

            }
            column(JSNPer; JSNPer)
            {

            }
            column(Quarterly_Target_Value; "Quarterly Target Value")
            {

            }
            column(Quarterly_Achievement; "Qtrly Achievement")
            {

            }
            column(Quarterly_Rebate; QR)
            {

            }
            column(TargetDedu; '20')
            {

            }
            column(AchDeds; "DMS Usage Days")
            {

            }
            column(Deduction_On_Qtr__Disc; DD)
            {

            }
            column(Total_Rebate; TR)
            {

            }
            column(CompInf; CompInf.Name)
            {

            }
            column(CompInfPic; CompInf.Picture)
            {

            }
            column(QtrPer; QtrPer)
            {

            }
            column(Shrinakge_Variance; "Shrinakge Variance")
            {

            }
            // >>>>G2S>>>9805_CAS-01427-T0Z6Y9>>>>070425
            column(Shrinkage02; Shrinkage02)
            {

            }
            column(MonthlySales45; MonthlySales45)
            {

            }
            column(TotalMonthlySales45; TotalMonthlySales45)
            {

            }
            // >>>>G2S>>>9805_CAS-01427-T0Z6Y9>>>>070425


            trigger OnPreDataItem()
            begin
                CalPeriod();//PKONSE21
                setrange("Target Period", ParTargetPeriod)
            end;

            trigger OnAfterGetRecord()
            var
                kdCustFocus: Record "KD Cust. Focus Brands ";
                myInt: Integer;
            begin
                IF Not Processed THEN BEGIN
                    //Calculate Flow Fields Of Achieve
                    Calcfields("Monthly Total Ach. Value",
                   "Achieve On Category A",
                   "Achieve On Category B",
                   "Achieve On Mixed Evap",
                   "Achieve On Mixed Capr",
                   "Achieve On Evap 60G",
                   "Achieve On Evap 120G",
                   "Achieve On EvapJSND 190G",
                   "Achieve On ExclEvap 190Gfc",
                   "Achieve On JSND Mid",
                   "Achieve On JSND Capr", "Achi On YOGH 90ML & EVAP", "Achieve On YOGHURT 90ML",
                   "Qtrly Achievement");


                    //ElementWise Rebate Calculation
                    //Oveerall
                    Clear("Flat Discount");
                    // IF "Monthly Total Ach. Value" <> 0 THEN
                    // "Flat Discount" := "Monthly Total Ach. Value" * 0.045;   //g2s commented out
                    //B2BSPON22JU20>> and layout
                    //Sai>>
                    //JSND&Evap-Juice-1Ltr

                    //Sai>>
                    // jsnd & evap -juice-1liter
                    clear("Evap & JSND Juice Dis-1Ltr");
                    CalcFields("Evap & JSND Juice Ach-1lit");

                    IF ("Evap & JSND Juice Ach-1lit" <> 0) And ("Evap & JSND JuiceTarget - 1Ltr" <> 0)
                        THEN begin
                        //>100
                        IF ("Evap & JSND Juice Ach-1lit" / "Evap & JSND JuiceTarget - 1Ltr") * 100 >= 100 then
                            "Evap & JSND Juice Dis-1Ltr" := "Monthly Total Ach. Value" * 1 * 0.0015 //changedby G2S from 0.0020 >>110625
                        else
                            //95-100
                            IF ("Evap & JSND Juice Ach-1lit" / "Evap & JSND JuiceTarget - 1Ltr") * 100 >= 95 then
                                "Evap & JSND Juice Dis-1Ltr" := "Monthly Total Ach. Value" * 0.0008 //changedby G2S from 0.0010 >>110625
                            else
                                //<90-95
                                IF ("Evap & JSND Juice Ach-1lit" / "Evap & JSND JuiceTarget - 1Ltr") * 100 >= 90 then
                                    "Evap & JSND Juice Dis-1Ltr" := "Monthly Total Ach. Value" * 0.0004; //changedby G2S from 0.0005 >>110625
                    end;

                    // jsnd & evap -juice can
                    clear("Evap & JSND Juice Dis-cans");
                    CalcFields("Evap & JSND Juice Ach-cans");
                    IF ("Evap & JSND Juice Ach-cans" <> 0) And ("Evap & JSND JuiceTarget - cans" <> 0)
                        THEN begin
                        //>100
                        IF ("Evap & JSND Juice Ach-cans" / "Evap & JSND JuiceTarget - cans") * 100 >= 100 then
                            "Evap & JSND Juice Dis-cans" := "Monthly Total Ach. Value" * 1 * 0.0010     //changedby G2S from 0.00125 >>290124
                        else
                            //95-100
                            IF ("Evap & JSND Juice Ach-cans" / "Evap & JSND JuiceTarget - cans") * 100 >= 95 then
                                "Evap & JSND Juice Dis-cans" := "Monthly Total Ach. Value" * 0.0005      //changedby G2S from 0.0006 >>290124
                            else
                                //<90-95
                                IF ("Evap & JSND Juice Ach-cans" / "Evap & JSND JuiceTarget - cans") * 100 >= 90 then
                                    "Evap & JSND Juice Dis-cans" := "Monthly Total Ach. Value" * 0.0003;     //changedby G2S from 0.0003 >>290124
                    end;

                    // jsnd & evap -juice capricun
                    clear("Evap & JSND Juice Dis-caprisun");
                    CalcFields("Evap & JSND Juice Ach caprisun");
                    IF ("Evap & JSND Juice Ach caprisun" <> 0) And ("Evap&JSND JuiTarget- Caprisun" <> 0)
                        THEN begin
                        //>100
                        IF ("Evap & JSND Juice Ach caprisun" / "Evap&JSND JuiTarget- Caprisun") * 100 >= 100 then
                            "Evap & JSND Juice Dis-caprisun" := "Monthly Total Ach. Value" * 1 * 0.0010
                        else
                            //95-100

                            IF ("Evap & JSND Juice Ach caprisun" / "Evap&JSND JuiTarget- Caprisun") * 100 >= 95 then
                                "Evap & JSND Juice Dis-caprisun" := "Monthly Total Ach. Value" * 0.0006     //changedby G2S from 0.0005 >>110625
                            else
                                //<90-95
                                IF ("Evap & JSND Juice Ach caprisun" / "Evap&JSND JuiTarget- Caprisun") * 100 >= 90 then
                                    "Evap & JSND Juice Dis-caprisun" := "Monthly Total Ach. Value" * 0.0003;
                    end;

                    // jsnd & evap -yoghurt-1liter
                    clear("Evap & JSND Yoghurt Disc-1lit");
                    CalcFields("Evap & JSND Yoghurt Ache-1 Lit");
                    IF ("Evap & JSND Yoghurt Ache-1 Lit" <> 0) And ("Evap&JSND Yoghurt Target-1lit" <> 0)
                        THEN begin
                        //>100
                        IF ("Evap & JSND Yoghurt Ache-1 Lit" / "Evap&JSND Yoghurt Target-1lit") * 100 >= 100 then
                            "Evap & JSND Yoghurt Disc-1lit" := "Monthly Total Ach. Value" * 1 * 0.0015
                        else
                            //95-100
                            IF ("Evap & JSND Yoghurt Ache-1 Lit" / "Evap&JSND Yoghurt Target-1lit") * 100 >= 95 then
                                "Evap & JSND Yoghurt Disc-1lit" := "Monthly Total Ach. Value" * 0.0006 //changedby G2S from 0.0008 >>040625
                            else
                                //<90-95
                                IF ("Evap & JSND Yoghurt Ache-1 Lit" / "Evap&JSND Yoghurt Target-1lit") * 100 >= 90 then
                                    "Evap & JSND Yoghurt Disc-1lit" := "Monthly Total Ach. Value" * 0.0003; //changedby G2S from 0.0004 >>110625
                    end;

                    // jsnd & evap -yoghurt-90ml
                    clear("Evap & JSND Yoghurt Disc-90ml");
                    CalcFields("Evap & JSND Yoghurt Ache-90 ML");
                    IF ("Evap & JSND Yoghurt Ache-90 ML" <> 0) And ("Evap&JSND Yoghurt Target-90ml" <> 0)
                        THEN begin
                        //>100
                        IF ("Evap & JSND Yoghurt Ache-90 ML" / "Evap&JSND Yoghurt Target-90ml") * 100 >= 100 then
                            "Evap & JSND Yoghurt Disc-90ml" := "Monthly Total Ach. Value" * 1 * 0.0010 //changedby G2S from 0.0015 >>110625
                        else
                            IF ("Evap & JSND Yoghurt Ache-90 ML" / "Evap&JSND Yoghurt Target-90ml") * 100 >= 95 then
                                "Evap & JSND Yoghurt Disc-90ml" := "Monthly Total Ach. Value" * 0.0005 //changedby G2S from 0.0008 >>110625
                            else
                                //<90-95
                                IF ("Evap & JSND Yoghurt Ache-90 ML" / "Evap&JSND Yoghurt Target-90ml") * 100 >= 90 then
                                    "Evap & JSND Yoghurt Disc-90ml" := "Monthly Total Ach. Value" * 0.0002; //changedby G2S from 0.0004 >>110625
                    end;

                    // jsnd & evap -HOLLANDIA
                    clear("Evap & JSND Hollan Disc-120g");
                    CalcFields("Evap&JSND Hollandia Ache-120g");
                    IF ("Evap&JSND Hollandia Ache-120g" <> 0) And ("Evap&JSND HollandiaTarget-120g" <> 0)
                        THEN begin
                        //>100
                        IF ("Evap&JSND Hollandia Ache-120g" / "Evap&JSND HollandiaTarget-120g") * 100 >= 100 then
                            "Evap & JSND Hollan Disc-120g" := "Monthly Total Ach. Value" * 1 * 0.0010   //changedby G2S from 0.0015 >>040625
                        else
                            //95-100
                            IF ("Evap&JSND Hollandia Ache-120g" / "Evap&JSND HollandiaTarget-120g") * 100 >= 95 then
                                "Evap & JSND Hollan Disc-120g" := "Monthly Total Ach. Value" * 0.0004  //changedby G2S from 0.0008 >>040625
                            else
                                //<90-95
                                IF ("Evap&JSND Hollandia Ache-120g" / "Evap&JSND HollandiaTarget-120g") * 100 >= 90 then
                                    "Evap & JSND Hollan Disc-120g" := "Monthly Total Ach. Value" * 0.0002;  //changedby G2S from 0.0004 >>040625
                    end;

                    //Addedby G2s>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>EvapJSND>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>290131

                    //evap JSND hollandia -190g 
                    clear("EvapJSND Hollandia Disc-190g");
                    CalcFields("EvapJSND Hollandia Achiv-190g");
                    IF ("EvapJSND Hollandia Achiv-190g" <> 0) And ("EvapJSND Hollandia Target-190g" <> 0)
                        THEN begin
                        //>100
                        IF ("EvapJSND Hollandia Achiv-190g" / "EvapJSND Hollandia Target-190g") * 100 >= 100 then
                            "EvapJSND Hollandia Disc-190g" := "Monthly Total Ach. Value" * 1 * 0.0015 //changedby G2S from 0.003 >>290124
                        else
                            //95-100
                            IF ("EvapJSND Hollandia Achiv-190g" / "EvapJSND Hollandia Target-190g") * 100 >= 95 then
                                "EvapJSND Hollandia Disc-190g" := "Monthly Total Ach. Value" * 0.0008 //changedby G2S from 0.0015 >>290124
                            else
                                //<90-95
                                IF ("EvapJSND Hollandia Achiv-190g" / "EvapJSND Hollandia Target-190g") * 100 >= 90 then
                                    "EvapJSND Hollandia Disc-190g" := "Monthly Total Ach. Value" * 0.0004; //changedby G2S from 0.0008 >>290124
                    end;
                    //Addedby G2s<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<>290131
                    //evap JSND hollandia -UHT 
                    clear("EvapJSND Holla Disc-UHT");
                    CalcFields("EvapJSND Holla Achiv-UHT");
                    IF ("EvapJSND Holla Achiv-UHT" <> 0) And ("EvapJSND Holla Target-UHT" <> 0)
                        THEN begin
                        //>100
                        IF ("EvapJSND Holla Achiv-UHT" / "EvapJSND Holla Target-UHT") * 100 >= 100 then
                            "EvapJSND Holla Disc-UHT" := "Monthly Total Ach. Value" * 1 * 0.0010 //changedby G2S from 0.003 >>290124 >>290124 & from 0.0015 >>040625
                        else
                            //95-100
                            IF ("EvapJSND Holla Achiv-UHT" / "EvapJSND Holla Target-UHT") * 100 >= 95 then
                                "EvapJSND Holla Disc-UHT" := "Monthly Total Ach. Value" * 0.0004  //changedby G2S from 0.0015 >>290124 >>290124 & from 0.0008 >>040625
                            else
                                //<90-95
                                IF ("EvapJSND Holla Achiv-UHT" / "EvapJSND Holla Target-UHT") * 100 >= 90 then
                                    "EvapJSND Holla Disc-UHT" := "Monthly Total Ach. Value" * 0.0002; //changedby G2S from 0.0008 >>290124 & from 0.0004 >>040625
                    end;

                    // jsnd -juice 1lit
                    clear("Excl JSND Juice Discount -1lit");
                    CalcFields("ExclJSND Juice Achiv-1lit");
                    IF ("ExclJSND Juice Achiv-1lit" <> 0) And ("Excl JSND Juice Targe 1-lit" <> 0)
                        THEN begin
                        //>100
                        IF ("ExclJSND Juice Achiv-1lit" / "Excl JSND Juice Targe 1-lit") * 100 >= 100 then
                            "Excl JSND Juice Discount -1lit" := "Monthly Total Ach. Value" * 1 * 0.0025      //changedby G2S from 0.0025 >>290124
                        else
                            //95-100
                            IF ("ExclJSND Juice Achiv-1lit" / "Excl JSND Juice Targe 1-lit") * 100 >= 95 then
                                "Excl JSND Juice Discount -1lit" := "Monthly Total Ach. Value" * 0.0013        //changedby G2S from 0.0013 >>290124
                            else
                                //<90-95
                                IF ("ExclJSND Juice Achiv-1lit" / "Excl JSND Juice Targe 1-lit") * 100 >= 90 then
                                    "Excl JSND Juice Discount -1lit" := "Monthly Total Ach. Value" * 0.0006;     //changedby G2S from 0.0006 >>290124
                    end;


                    //jsnd -juice -cans
                    clear("Excl JSND Juice Disc-cans");
                    CalcFields("ExclJSNDJuice Achiv-cans");
                    IF ("ExclJSNDJuice Achiv-cans" <> 0) And ("Excl JSND JuiceTarget -cans" <> 0)
                        THEN begin
                        //>100
                        IF ("ExclJSNDJuice Achiv-cans" / "Excl JSND JuiceTarget -cans") * 100 >= 100 then
                            "Excl JSND Juice Disc-cans" := "Monthly Total Ach. Value" * 1 * 0.0020
                        else
                            //95-100
                            IF ("ExclJSNDJuice Achiv-cans" / "Excl JSND JuiceTarget -cans") * 100 >= 95 then
                                "Excl JSND Juice Disc-cans" := "Monthly Total Ach. Value" * 0.0010
                            else
                                //<90-95
                                IF ("ExclJSNDJuice Achiv-cans" / "Excl JSND JuiceTarget -cans") * 100 >= 90 then
                                    "Excl JSND Juice Disc-cans" := "Monthly Total Ach. Value" * 0.0005;
                    end;

                    //jsnd -juice -caprisun
                    clear("Excl JSND Juice Disc-caprisun");
                    CalcFields("Excl JSND Juice Achie-caprisun");
                    IF ("Excl JSND Juice Achie-caprisun" <> 0) And ("ExclJSNDJuiceTarget -capricun" <> 0)
                        THEN begin
                        //>100
                        IF ("Excl JSND Juice Achie-caprisun" / "ExclJSNDJuiceTarget -capricun") * 100 >= 100 then
                            "Excl JSND Juice Disc-caprisun" := "Monthly Total Ach. Value" * 1 * 0.0013 //changedby G2S from 0.0013 >>290124
                        else
                            //95-100
                            IF ("Excl JSND Juice Achie-caprisun" / "ExclJSNDJuiceTarget -capricun") * 100 >= 95 then
                                "Excl JSND Juice Disc-caprisun" := "Monthly Total Ach. Value" * 0.0007 //changedby G2S from 0.0007 >>290124
                            else
                                //<90-95
                                IF ("Excl JSND Juice Achie-caprisun" / "ExclJSNDJuiceTarget -capricun") * 100 >= 90 then
                                    "Excl JSND Juice Disc-caprisun" := "Monthly Total Ach. Value" * 0.0003; //changedby G2S from 0.0003 >>290124
                    end;


                    //jsnd -YOGHURT -1LIT
                    clear("Excl JSND Yoghurt Disc-1lit");
                    CalcFields("Excl JSND Yoghurt Achiv-1lit");
                    IF ("Excl JSND Yoghurt Achiv-1lit" <> 0) And ("Excl JSND Yoghurt Target-1lit" <> 0)
                        THEN begin
                        //>100
                        IF ("Excl JSND Yoghurt Achiv-1lit" / "Excl JSND Yoghurt Target-1lit") * 100 >= 100 then
                            "Excl JSND Yoghurt Disc-1lit" := "Monthly Total Ach. Value" * 1 * 0.0020  //changedby G2S from 0.0020 >>2901230.0020
                        else
                            //95-100
                            IF ("Excl JSND Yoghurt Achiv-1lit" / "Excl JSND Yoghurt Target-1lit") * 100 >= 95 then
                                "Excl JSND Yoghurt Disc-1lit" := "Monthly Total Ach. Value" * 0.0010 //changedby G2S from 0.0010 >>290124
                            else
                                //<90-95
                                IF ("Excl JSND Yoghurt Achiv-1lit" / "Excl JSND Yoghurt Target-1lit") * 100 >= 90 then
                                    "Excl JSND Yoghurt Disc-1lit" := "Monthly Total Ach. Value" * 0.0005; //changedby G2S from 0.0005 >>290124
                    end;

                    //jsnd yoghurt 90ml
                    clear("Excl JSND Yoghurt Disc-90ml");
                    CalcFields("Excl JSND Yoghurt Achiv-90ml");
                    IF ("Excl JSND Yoghurt Achiv-90ml" <> 0) And ("Excl JSND Yoghurt Target-90ml" <> 0)
                        THEN begin
                        //>100
                        IF ("Excl JSND Yoghurt Achiv-90ml" / "Excl JSND Yoghurt Target-90ml") * 100 >= 100 then
                            "Excl JSND Yoghurt Disc-90ml" := "Monthly Total Ach. Value" * 1 * 0.0022  //changedby G2S from 0.0022 >>290124
                        else
                            //95-100
                            IF ("Excl JSND Yoghurt Achiv-90ml" / "Excl JSND Yoghurt Target-90ml") * 100 >= 95 then
                                "Excl JSND Yoghurt Disc-90ml" := "Monthly Total Ach. Value" * 0.0011 //changedby G2S from 0.0011 >>290124
                            else
                                //<90-95
                                IF ("Excl JSND Yoghurt Achiv-90ml" / "Excl JSND Yoghurt Target-90ml") * 100 >= 90 then
                                    "Excl JSND Yoghurt Disc-90ml" := "Monthly Total Ach. Value" * 0.0006; //changedby G2S from 0.0006 >>290124
                    end;

                    //evap hollandia
                    clear("Excl EVAP Hollan Disc-120gFc");
                    CalcFields("ExclEVAP Holl Achv -120gFc");
                    IF ("ExclEVAP Holl Achv -120gFc" <> 0) And ("ExclEVAP Holla Target-120g Fc" <> 0)
                        THEN begin
                        //>100
                        IF ("ExclEVAP Holl Achv -120gFc" / "ExclEVAP Holla Target-120g Fc") * 100 >= 100 then
                            "Excl EVAP Hollan Disc-120gFc" := "Monthly Total Ach. Value" * 1 * 0.0035  //changedby G2S from 0.003 >>290124
                        else
                            //95-100
                            IF ("ExclEVAP Holl Achv -120gFc" / "ExclEVAP Holla Target-120g Fc") * 100 >= 95 then
                                "Excl EVAP Hollan Disc-120gFc" := "Monthly Total Ach. Value" * 0.0018  //changedby G2S from 0.0015 >>290124
                            else
                                //<90-95
                                IF ("ExclEVAP Holl Achv -120gFc" / "ExclEVAP Holla Target-120g Fc") * 100 >= 90 then
                                    "Excl EVAP Hollan Disc-120gFc" := "Monthly Total Ach. Value" * 0.0009;   //changedby G2S from 0.0008 >>290124
                    end;

                    //Addedby G2s>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>Evap Hollandia 190g>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>290131
                    //evap hollandia 190g
                    clear("ExclEVAP Holla Disc-190gFc");
                    CalcFields("ExclEVAP Holl Achv -190gFc");
                    IF ("ExclEVAP Holl Achv -190gFc" <> 0) And ("ExclEVAP Holla Target-190g Fc" <> 0)
                        THEN begin
                        //>100
                        IF ("ExclEVAP Holl Achv -190gFc" / "ExclEVAP Holla Target-190g Fc") * 100 >= 100 then
                            "ExclEVAP Holla Disc-190gFc" := "Monthly Total Ach. Value" * 1 * 0.0035
                        else
                            //95-100
                            IF ("ExclEVAP Holl Achv -190gFc" / "ExclEVAP Holla Target-190g Fc") * 100 >= 95 then
                                "ExclEVAP Holla Disc-190gFc" := "Monthly Total Ach. Value" * 0.0018
                            else
                                //<90-95
                                IF ("ExclEVAP Holl Achv -190gFc" / "ExclEVAP Holla Target-190g Fc") * 100 >= 90 then
                                    "ExclEVAP Holla Disc-190gFc" := "Monthly Total Ach. Value" * 0.0009;
                    end;
                    //Addedby G2s>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>290131
                    //UHT Milk
                    clear("ExclEVAP Holla Disc-UHT MILK");
                    CalcFields("ExclEVAP Holl Achv -UHT MILK");
                    IF ("ExclEVAP Holl Achv -UHT MILK" <> 0) And ("ExclEVAP Holla Target-UHT MILK" <> 0)
                        THEN begin
                        //>100
                        IF ("ExclEVAP Holl Achv -UHT MILK" / "ExclEVAP Holla Target-UHT MILK") * 100 >= 100 then
                            "ExclEVAP Holla Disc-UHT MILK" := "Monthly Total Ach. Value" * 1 * 0.0035
                        else
                            //95-100
                            IF ("ExclEVAP Holl Achv -UHT MILK" / "ExclEVAP Holla Target-UHT MILK") * 100 >= 95 then
                                "ExclEVAP Holla Disc-190gFc" := "Monthly Total Ach. Value" * 0.0018
                            else
                                //<90-95
                                IF ("ExclEVAP Holl Achv -UHT MILK" / "ExclEVAP Holla Target-UHT MILK") * 100 >= 90 then
                                    "ExclEVAP Holla Disc-UHT MILK" := "Monthly Total Ach. Value" * 0.0009;
                    end;
                    //G2S >>>>>>>>>>>>>>>>>>>>>110625 CAS-01425-M8J6P2
                    //Juice 315ml
                    clear("EvapJSND Juice Disc-315ml");
                    CalcFields("EvapJSND Juice Achiv-315ml");
                    IF ("EvapJSND Juice Achiv-315ml" <> 0) And ("EvapJSND Juice Target-315ml" <> 0)
                        THEN begin
                        //>100
                        IF ("EvapJSND Juice Achiv-315ml" / "EvapJSND Juice Target-315ml") * 100 >= 100 then
                            "EvapJSND Juice Disc-315ml" := "Monthly Total Ach. Value" * 1 * 0.0010
                        else
                            //95-100
                            IF ("EvapJSND Juice Achiv-315ml" / "EvapJSND Juice Target-315ml") * 100 >= 95 then
                                "EvapJSND Juice Disc-315ml" := "Monthly Total Ach. Value" * 0.0006
                            else
                                //<90-95
                                IF ("EvapJSND Juice Achiv-315ml" / "EvapJSND Juice Target-315ml") * 100 >= 90 then
                                    "EvapJSND Juice Disc-315ml" := "Monthly Total Ach. Value" * 0.0003;
                    end;

                    //Pet Drinks
                    clear("EvapJSND Juice Disc-Pet");
                    CalcFields("EvapJSND Juice Achiv-Pet");
                    IF ("EvapJSND Juice Achiv-Pet" <> 0) And ("EvapJSND Juice Target-Pet" <> 0)
                        THEN begin
                        //>100
                        IF ("EvapJSND Juice Achiv-Pet" / "EvapJSND Juice Target-Pet") * 100 >= 100 then
                            "EvapJSND Juice Disc-Pet" := "Monthly Total Ach. Value" * 1 * 0.0010
                        else
                            //95-100
                            IF ("EvapJSND Juice Achiv-Pet" / "EvapJSND Juice Target-Pet") * 100 >= 95 then
                                "EvapJSND Juice Disc-Pet" := "Monthly Total Ach. Value" * 0.0006
                            else
                                //<90-95
                                IF ("EvapJSND Juice Achiv-Pet" / "EvapJSND Juice Target-Pet") * 100 >= 90 then
                                    "EvapJSND Juice Disc-Pet" := "Monthly Total Ach. Value" * 0.0003;
                    end;

                    //Yoghurt 315ml
                    clear("EvapJSND Yoghurt Disc-315ml");
                    CalcFields("EvapJSND Yoghurt Achiv-315ml");
                    IF ("EvapJSND Yoghurt Achiv-315ml" <> 0) And ("EvapJSND Yoghurt Target-315ml" <> 0)
                        THEN begin
                        //>100
                        IF ("EvapJSND Yoghurt Achiv-315ml" / "EvapJSND Yoghurt Target-315ml") * 100 >= 100 then
                            "EvapJSND Yoghurt Disc-315ml" := "Monthly Total Ach. Value" * 1 * 0.0010
                        else
                            //95-100
                            IF ("EvapJSND Yoghurt Achiv-315ml" / "EvapJSND Yoghurt Target-315ml") * 100 >= 95 then
                                "EvapJSND Yoghurt Disc-315ml" := "Monthly Total Ach. Value" * 0.0005
                            else
                                //<90-95
                                IF ("EvapJSND Yoghurt Achiv-315ml" / "EvapJSND Yoghurt Target-315ml") * 100 >= 90 then
                                    "EvapJSND Yoghurt Disc-315ml" := "Monthly Total Ach. Value" * 0.0003;
                    end;
                    //G2S >>>>>>>>>>>>>>>>>>>>>110625 CAS-01425-M8J6P2


                    //evap hllandia -120g slim
                    clear("Excl EVAP Hollan Disc-120gslim");
                    CalcFields("ExclEVAP Holla Achiv-120gslim");
                    IF ("ExclEVAP Holla Achiv-120gslim" <> 0) And ("ExclEVAP Holla Target-120gslim" <> 0)
                        THEN begin
                        //>100
                        IF ("ExclEVAP Holla Achiv-120gslim" / "ExclEVAP Holla Target-120gslim") * 100 >= 100 then
                            "Excl EVAP Hollan Disc-120gslim" := "Monthly Total Ach. Value" * 1 * 0.003 //changedby G2S from 0.003 >>290124
                        else
                            //95-100
                            IF ("ExclEVAP Holla Achiv-120gslim" / "ExclEVAP Holla Target-120gslim") * 100 >= 95 then
                                "Excl EVAP Hollan Disc-120gslim" := "Monthly Total Ach. Value" * 0.0015 //changedby G2S from 0.0015 >>290124
                            else
                                //<90-95
                                IF ("ExclEVAP Holla Achiv-120gslim" / "ExclEVAP Holla Target-120gslim") * 100 >= 90 then
                                    "Excl EVAP Hollan Disc-120gslim" := "Monthly Total Ach. Value" * 0.0008; //changedby G2S from 0.0008 >>290124
                    end;


                    //evap hollandia 50gfc
                    clear("Excl EVAP Hollan Disc-50gFc");
                    CalcFields("ExclEVAP Holla Achiv-50Fc");
                    IF ("ExclEVAP Holla Achiv-50Fc" <> 0) And ("ExclEVAP Holla Target-50g Fc" <> 0)
                        THEN begin
                        //>100
                        IF ("ExclEVAP Holla Achiv-50Fc" / "ExclEVAP Holla Target-50g Fc") * 100 >= 100 then
                            "Excl EVAP Hollan Disc-50gFc" := "Monthly Total Ach. Value" * 1 * 0.0030 //changedby G2S from 0.002 >>290124
                        else
                            //95-100
                            IF ("ExclEVAP Holla Achiv-50Fc" / "ExclEVAP Holla Target-50g Fc") * 100 >= 95 then
                                "Excl EVAP Hollan Disc-50gFc" := "Monthly Total Ach. Value" * 0.0014 //changedby G2S from 0.0010 >>290124 & from 0.0015 >>110625
                            else
                                //<90-95
                                IF ("ExclEVAP Holla Achiv-50Fc" / "ExclEVAP Holla Target-50g Fc") * 100 >= 90 then
                                    "Excl EVAP Hollan Disc-50gFc" := "Monthly Total Ach. Value" * 0.0007; //changedby G2S from 0.0005 >>290124 & from 0.0008 >>040625
                    end;


                    //evap hollandia 50gslim
                    clear("Excl EVAP Hollan Disc-50gslim");
                    CalcFields("ExclEVAP Holla Achiv-50gslim");
                    IF ("ExclEVAP Holla Achiv-50gslim" <> 0) And ("ExclEVAP Holla Target-50gslim" <> 0)
                        THEN begin
                        //>100
                        IF ("ExclEVAP Holla Achiv-50gslim" / "ExclEVAP Holla Target-50gslim") * 100 >= 100 then
                            "Excl EVAP Hollan Disc-50gslim" := "Monthly Total Ach. Value" * 1 * 0.002 //changedby G2S from 0.002 >>290124
                        else
                            //95-100
                            IF ("ExclEVAP Holla Achiv-50gslim" / "ExclEVAP Holla Target-50gslim") * 100 >= 95 then
                                "Excl EVAP Hollan Disc-50gslim" := "Monthly Total Ach. Value" * 0.0010 //changedby G2S from 0.0010 >>290124
                            else
                                //<90-95
                                IF ("ExclEVAP Holla Achiv-50gslim" / "ExclEVAP Holla Target-50gslim") * 100 >= 90 then
                                    "Excl EVAP Hollan Disc-50gslim" := "Monthly Total Ach. Value" * 0.0005; //changedby G2S from 0.0005 >>290124
                    end;





                    //Addedby G2s>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>DMS Usage>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>290131
                    //DMS Usage No. Of Days
                    clear("DMS_Usage Disc");
                    //CalcFields("DMS_UsageDays Achiev");
                    IF (DMS_UsageDays <> 0)
                    then begin
                        if (DMS_UsageDays >= 92) then
                            "DMS_Usage Disc" := "Monthly Total Ach. Value" * 0.0005;
                    end;

                    //<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<

                    //Addedby G2s>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>StockNorm %>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>290131
                    //StockNorm
                    clear("StockNorm Disc");
                    //CalcFields("StockNorm Achiev");
                    IF ("StockNorm Target" <> 0)
                    then begin
                        if ("StockNorm Target" >= 0.80) then
                            "StockNorm Disc" := "Monthly Total Ach. Value" * 0.0001;
                    end;

                    //<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<

                    //Addedby G2s>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>StockNorm %>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>290124
                    //Distributor financial
                    clear("Working Cap Disc");
                    //PKKeyDistributorTarget.CalcFields("StockNorm Achiev");
                    IF ("Working Cap Target" <> 0) and ("Working Cap Achieve" <> 0)
                    then begin
                        if ("Working Cap Achieve" >= "Working Cap Target") then
                            "Working Cap Disc" := "Monthly Total Ach. Value" * 0.0001;
                    end;

                    //<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<


                    //Addedby G2s>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>Van Target No. of Days>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>290131
                    //StockNorm
                    // clear("Van Move disc");
                    // //PKKeyDistributorTarget.CalcFields("StockNorm Achiev");
                    // IF ("Van Move Target" <> 0) and ("Sec. SalesValue" <> 0)
                    // then begin
                    //     ///15-18days
                    //     if ("Van Move Target" >= 15) and ("Van Move Target" <= 18) then
                    //         "Van Move disc" := "Sec. SalesValue" * 0.0055
                    //     else
                    //         ///19-21days
                    //         if ("Van Move Target" >= 19) and ("Van Move Target" <= 21) then
                    //             "Van Move disc" := "Sec. SalesValue" * 0.0085
                    //         else
                    //             ///22days Above
                    //             if ("Van Move Target" >= 22) then
                    //                 "Van Move disc" := "Sec. SalesValue" * 0.0125;

                    // end;

                    //<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<




                    // Shrinkage Variance 
                    // Clear("Shrinakge Variance");
                    //CalcFields("Shrinkage Base Amount");
                    //IF "Monthly Total Ach. Value" <> 0 THEN
                    //    "Shrinakge Variance" := "Shrinkage Base Amount" * 0.002;


                    //Quarterly
                    clear("Quarterly Rebate");
                    //IF  "Customer No." = 'ENCUST0102' THEN
                    //PKONSE21>>
                    clear("Qtrly Rebate Base Amnt");
                    IF QAPeriodFilter <> '' THEN BEGIN
                        QKeyDistributorTarget.RESET;
                        QKeyDistributorTarget.SETFILTER("Target Period", QAPeriodFilter);
                        QKeyDistributorTarget.SETRANGE("Customer No.", "Customer No.");
                        IF QKeyDistributorTarget.FindSet() then
                            repeat
                                QKeyDistributorTarget.CalcFields("Monthly Total Ach. Value");
                                "Qtrly Rebate Base Amnt" += QKeyDistributorTarget."Monthly Total Ach. Value";
                            until QKeyDistributorTarget.Next = 0;
                    end;

                    //Quarterly 100%
                    IF ("Qtrly Achievement" <> 0) And ("Quarterly Target Value" <> 0) then begin
                        if ("Qtrly Achievement" / "Quarterly Target Value") * 100 >= 100
                             //PKON22J20>>
                             //AND ("Qtrly Rebate Base Amnt" <> 0) THEN
                             //   "Quarterly Rebate" := "Qtrly Rebate Base Amnt" * 0.0075;
                             THEN BEGIN
                            "Quarterly Rebate" := round("Qtrly Achievement", 0.01) * 0.0075;
                            If Customerrec2.GET("Customer No.") THEN;
                            IF Customerrec2."KD Period Joined" <> '' Then Begin

                                clear(PrevMon);
                                clear(PrevYear);
                                clear(TotalMonths);
                                PKKDRebateData.Reset();
                                PKKDRebateData.SetCurrentKey("Posting Date");
                                PKKDRebateData.SetRange("Customer Code", "Customer No.");
                                PKKDRebateData.SetRange(code, 10);
                                PKKDRebateData.Setrange("Rebate Period Code", "Target Period");
                                IF PKKDRebateData.FindSet() then BEGIN
                                    IF KDPeriod.GET(Customerrec2."KD Period Joined") THEN BEGIN
                                        IF PKKDRebateData."Posting Date" > KDPeriod."Start Date" THEN BEGIN
                                            //PrevMon := Date2DMY(PKKDRebateData."Posting Date", 2);
                                            //PrevYear := Date2DMY(PKKDRebateData."Posting Date", 3);
                                            repeat
                                                IF (PrevMon <> Date2DMY(PKKDRebateData."Posting Date", 2)) Then begin
                                                    PrevMon := Date2DMY(PKKDRebateData."Posting Date", 2);
                                                    PrevYear := Date2DMY(PKKDRebateData."Posting Date", 3);
                                                    TotalMonths += 1;
                                                end;
                                            Until PKKDRebateData.Next = 0;
                                            IF (TotalMonths <> 0) and (TotalMonths < 3) then BEGIN
                                                Message('Customer %1 is eligible for %2 months for Qrt Rebate. Total Qtr rebate Amnt %3 and Eligible Amnt %4', "Customer No.", TotalMonths, "Quarterly Rebate", "Quarterly Rebate" * (TotalMonths / 3));
                                                "Quarterly Rebate" := "Quarterly Rebate" * (TotalMonths / 3);
                                            End;
                                        End;
                                    End;
                                End;
                            End;
                        end;
                    end;
                    //PKON22J20<<
                    //PKON22J20.2>>

                    //>>>>>>>>>>>>>>>g2s added new percentage 290124>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>
                    //Quarterly 95-99%
                    IF ("Qtrly Achievement" <> 0) And ("Quarterly Target Value" <> 0) then begin
                        if (("Qtrly Achievement" / "Quarterly Target Value") * 100 >= 95) and
                        (("Qtrly Achievement" / "Quarterly Target Value") * 100 <= 99.99)
                             //PKON22J20>>
                             //AND ("Qtrly Rebate Base Amnt" <> 0) THEN
                             //   "Quarterly Rebate" := "Qtrly Rebate Base Amnt" * 0.0075;
                             THEN BEGIN
                            "Quarterly Rebate" := round("Qtrly Achievement", 0.01) * 0.0050;
                            If Customerrec2.GET("Customer No.") THEN;
                            IF Customerrec2."KD Period Joined" <> '' Then Begin

                                clear(PrevMon);
                                clear(PrevYear);
                                clear(TotalMonths);
                                PKKDRebateData.Reset();
                                PKKDRebateData.SetCurrentKey("Posting Date");
                                PKKDRebateData.SetRange("Customer Code", "Customer No.");
                                PKKDRebateData.SetRange(code, 10);
                                PKKDRebateData.Setrange("Rebate Period Code", "Target Period");
                                IF PKKDRebateData.FindSet() then BEGIN
                                    IF KDPeriod.GET(Customerrec2."KD Period Joined") THEN BEGIN
                                        IF PKKDRebateData."Posting Date" > KDPeriod."Start Date" THEN BEGIN
                                            //PrevMon := Date2DMY(PKKDRebateData."Posting Date", 2);
                                            //PrevYear := Date2DMY(PKKDRebateData."Posting Date", 3);
                                            repeat
                                                IF (PrevMon <> Date2DMY(PKKDRebateData."Posting Date", 2)) Then begin
                                                    PrevMon := Date2DMY(PKKDRebateData."Posting Date", 2);
                                                    PrevYear := Date2DMY(PKKDRebateData."Posting Date", 3);
                                                    TotalMonths += 1;
                                                end;
                                            Until PKKDRebateData.Next = 0;
                                            IF (TotalMonths <> 0) and (TotalMonths < 3) then BEGIN
                                                Message('Customer %1 is eligible for %2 months for Qrt Rebate. Total Qtr rebate Amnt %3 and Eligible Amnt %4', "Customer No.", TotalMonths, "Quarterly Rebate", "Quarterly Rebate" * (TotalMonths / 3));
                                                "Quarterly Rebate" := "Quarterly Rebate" * (TotalMonths / 3);
                                            End;
                                        End;
                                    End;
                                End;
                            End;
                        end;
                    end;
                    //PKON22J20<<
                    //PKON22J20.2>>

                    //>>>>>>>>>>>>>>>g2s added new percentage 290124>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>
                    //Quarterly 90-94%
                    IF ("Qtrly Achievement" <> 0) And ("Quarterly Target Value" <> 0) then begin
                        if (("Qtrly Achievement" / "Quarterly Target Value") * 100 >= 90)
                        and
                        (("Qtrly Achievement" / "Quarterly Target Value") * 100 <= 94.99)
                             //PKON22J20>>
                             //AND ("Qtrly Rebate Base Amnt" <> 0) THEN
                             //   "Quarterly Rebate" := "Qtrly Rebate Base Amnt" * 0.0075;
                             THEN BEGIN
                            "Quarterly Rebate" := round("Qtrly Achievement", 0.01) * 0.0025;
                            If Customerrec2.GET("Customer No.") THEN;
                            IF Customerrec2."KD Period Joined" <> '' Then Begin

                                clear(PrevMon);
                                clear(PrevYear);
                                clear(TotalMonths);
                                PKKDRebateData.Reset();
                                PKKDRebateData.SetCurrentKey("Posting Date");
                                PKKDRebateData.SetRange("Customer Code", "Customer No.");
                                PKKDRebateData.SetRange(code, 10);
                                PKKDRebateData.Setrange("Rebate Period Code", "Target Period");
                                IF PKKDRebateData.FindSet() then BEGIN
                                    IF KDPeriod.GET(Customerrec2."KD Period Joined") THEN BEGIN
                                        IF PKKDRebateData."Posting Date" > KDPeriod."Start Date" THEN BEGIN
                                            //PrevMon := Date2DMY(PKKDRebateData."Posting Date", 2);
                                            //PrevYear := Date2DMY(PKKDRebateData."Posting Date", 3);
                                            repeat
                                                IF (PrevMon <> Date2DMY(PKKDRebateData."Posting Date", 2)) Then begin
                                                    PrevMon := Date2DMY(PKKDRebateData."Posting Date", 2);
                                                    PrevYear := Date2DMY(PKKDRebateData."Posting Date", 3);
                                                    TotalMonths += 1;
                                                end;
                                            Until PKKDRebateData.Next = 0;
                                            IF (TotalMonths <> 0) and (TotalMonths < 3) then BEGIN
                                                Message('Customer %1 is eligible for %2 months for Qrt Rebate. Total Qtr rebate Amnt %3 and Eligible Amnt %4', "Customer No.", TotalMonths, "Quarterly Rebate", "Quarterly Rebate" * (TotalMonths / 3));
                                                "Quarterly Rebate" := "Quarterly Rebate" * (TotalMonths / 3);
                                            End;
                                        End;
                                    End;
                                End;
                            End;
                        end;
                    end;
                    //PKON22J20<<
                    //PKON22J20.2>>



                    //PKON22J20<<
                    //PKON22J20.2>>

                    /* IF "DMS Usage Days" > 20 THEN
                        "Quarterly Rebate" -= "Quarterly Rebate" * 0.25; */
                    //PKON22J20.2<< 

                    kdCustFocus.Reset();
                    kdCustFocus.Setrange("Customer No.", "Customer No.");
                    if kdCustFocus.FindFirst() then begin
                        If kdcustfocus."Customer Type" = kdCustfocus."Customer Type"::KD then begin
                            Clear("Total Rebate");
                            "Total Rebate" := "Flat Discount" +

                            "Evap & JSND Juice Dis-1Ltr" + "Evap & JSND Juice Dis-cans" + "Evap & JSND Juice Dis-caprisun" +
                            "Evap & JSND Yoghurt Disc-1lit" + "Evap & JSND Yoghurt Disc-90ml" +
                            "Evap & JSND Hollan Disc-120g" + "EvapJSND Hollandia Disc-190g" +  //G2S Added "EvapJSND Hollandia Disc-190g"
                            "EvapJSND Holla Disc-UHT" + "ExclEVAP Holla Disc-UHT MILK" +
                            +"EvapJSND Juice Disc-315ml" + "EvapJSND Juice Disc-Pet" + "EvapJSND Yoghurt Disc-315ml" + //G2S>>>>>110625 CAS-01425-M8J6P2
                            "Excl JSND Juice Discount -1lit" + "Excl JSND Juice Disc-cans" + "Excl JSND Juice Disc-caprisun" +
                            "Excl JSND Yoghurt Disc-1lit" + "Excl JSND Yoghurt Disc-90ml" +
                            "DMS_Usage Disc" + "StockNorm Disc" + "Working Cap Disc" + "Van Move disc" +  //G2S Added "Discounts"

                            //G2s>>>>>>>Added 190gFC into the formula>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>020224
                            "ExclEVAP Holla Disc-190gFc" + "Excl EVAP Hollan Disc-120gFc" + "Excl EVAP Hollan Disc-120gslim" + "Excl EVAP Hollan Disc-50gFc" + "Excl EVAP Hollan Disc-50gslim" +
                            //G2s>>>>>>>Added 190gFC into the formula>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>020224

                            "Shrinakge Variance" + "Quarterly Rebate" + "Deduction On Qtr. Disc";
                        end else
                            if kdcustfocus."Customer Type" = kdCustfocus."Customer Type"::"WD-L1" then begin
                                Clear("Total Rebate");
                                "Total Rebate" := "Flat Discount" +

                                "Evap & JSND Juice Dis-1Ltr" + "Evap & JSND Juice Dis-cans" + "Evap & JSND Juice Dis-caprisun" +
                                "Evap & JSND Yoghurt Disc-1lit" + "Evap & JSND Yoghurt Disc-90ml" +
                                "Evap & JSND Hollan Disc-120g" + "EvapJSND Hollandia Disc-190g" +  //G2S Added "EvapJSND Hollandia Disc-190g"

                                "Excl JSND Juice Discount -1lit" + "Excl JSND Juice Disc-cans" + "Excl JSND Juice Disc-caprisun" +
                                "Excl JSND Yoghurt Disc-1lit" + "Excl JSND Yoghurt Disc-90ml" +
                                "EvapJSND Holla Disc-UHT" + "ExclEVAP Holla Disc-UHT MILK" +
                                "EvapJSND Juice Disc-315ml" + "EvapJSND Juice Disc-Pet" + "EvapJSND Yoghurt Disc-315ml" + //G2S>>>>>110625 CAS-01425-M8J6P2
                                //G2s>>>>>>>Added 190gFC into the formula>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>020224
                                "ExclEVAP Holla Disc-190gFc" + "Excl EVAP Hollan Disc-120gFc" + "Excl EVAP Hollan Disc-120gslim" + "Excl EVAP Hollan Disc-50gFc" + "Excl EVAP Hollan Disc-50gslim" +
                                //G2s>>>>>>>Added 190gFC into the formula>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>020224

                                "Shrinakge Variance" + "Quarterly Rebate" + "Deduction On Qtr. Disc";

                            end else
                                if kdcustfocus."Customer Type" = kdCustfocus."Customer Type"::"WD-L2" then begin
                                    Clear("Total Rebate");
                                    "Total Rebate" := "Flat Discount" +

                                    "Evap & JSND Juice Dis-1Ltr" + "Evap & JSND Juice Dis-cans" + "Evap & JSND Juice Dis-caprisun" +
                                    "Evap & JSND Yoghurt Disc-1lit" + "Evap & JSND Yoghurt Disc-90ml" +
                                    "Evap & JSND Hollan Disc-120g" + "EvapJSND Hollandia Disc-190g" +  //G2S Added "EvapJSND Hollandia Disc-190g"

                                    "Excl JSND Juice Discount -1lit" + "Excl JSND Juice Disc-cans" + "Excl JSND Juice Disc-caprisun" +
                                    "Excl JSND Yoghurt Disc-1lit" + "Excl JSND Yoghurt Disc-90ml" +
                                    "DMS_Usage Disc" + "StockNorm Disc" + "Van Move disc" +  //G2S Added "Discounts"
                                    "EvapJSND Holla Disc-UHT" + "ExclEVAP Holla Disc-UHT MILK" +
                                    "EvapJSND Juice Disc-315ml" + "EvapJSND Juice Disc-Pet" + "EvapJSND Yoghurt Disc-315ml" + //G2S>>>>>110625 CAS-01425-M8J6P2
                                    //G2s>>>>>>>Added 190gFC into the formula>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>020224
                                    "ExclEVAP Holla Disc-190gFc" + "Excl EVAP Hollan Disc-120gFc" + "Excl EVAP Hollan Disc-120gslim" + "Excl EVAP Hollan Disc-50gFc" + "Excl EVAP Hollan Disc-50gslim" +
                                    //G2s>>>>>>>Added 190gFC into the formula>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>020224

                                    "Shrinakge Variance" + "Quarterly Rebate" + "Deduction On Qtr. Disc";
                                end;
                    end;
                end;
                // >>>>G2S>>>9805_CAS-01427-T0Z6Y9>>>>070425
                Clear(MonthlySales45);
                Clear(Shrinkage02);
                // >>>>G2S>>>9805_CAS-01427-T0Z6Y9>>>>070425
                FD := "Flat Discount";
                DIscA := "Disc On Category A";
                DiscB := "Disc On Category B";

                EvapD := "Evap & JSND Juice Dis-1Ltr" + "Evap & JSND Juice Dis-cans" + "Evap & JSND Juice Dis-caprisun" +
                "Evap & JSND Yoghurt Disc-1lit" + "Evap & JSND Yoghurt Disc-90ml" +
                "Evap & JSND Hollan Disc-120g" + "EvapJSND Hollandia Disc-190g"
                + "EvapJSND Holla Disc-UHT" +  //G2S Added "EvapJSND Hollandia Disc-190g"
                "EvapJSND Juice Disc-315ml" + "EvapJSND Juice Disc-Pet" + "EvapJSND Yoghurt Disc-315ml"; //G2S>>>>>110625 CAS-01425-M8J6P2


                MixdD := "Excl JSND Juice Discount -1lit" + "Excl JSND Juice Disc-cans" + "Excl JSND Juice Disc-caprisun" +
                "Excl JSND Yoghurt Disc-1lit" + "Excl JSND Yoghurt Disc-90ml";

                //G2s>>>>>>>Added 190gFC into the formula>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>020224
                JSNDDic := "ExclEVAP Holla Disc-190gFc" + "Excl EVAP Hollan Disc-120gFc" + "Excl EVAP Hollan Disc-120gslim" + "Excl EVAP Hollan Disc-50gFc" + "Excl EVAP Hollan Disc-50gslim" +
                //G2s>>>>>>>Added 190gFC into the formula>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>020224
                "ExclEVAP Holla Disc-UHT MILK";
                TR := "Total Rebate";
                DD := "Deduction On Qtr. Disc";
                QR := "Quarterly Rebate";


                //EVAP & JSND
                CalcFields("Evap & JSND Juice Ach-1lit", "Evap & JSND Juice Ach-cans", "Evap & JSND Juice Ach caprisun");
                CalcFields("Evap & JSND Yoghurt Ache-1 Lit", "Evap & JSND Yoghurt Ache-90 ML", "Evap&JSND Hollandia Ache-120g", "EvapJSND Hollandia Achiv-190g"); //G2S Added "EvapJSND Hollandia Achiv-190g"
                CalcFields("Evap&JSND Hollandia Ache-120g", "EvapJSND Hollandia Achiv-190g", "EvapJSND Holla Achiv-UHT"); //G2S Added "EvapJSND Hollandia Achiv-190g"
                CalcFields("EvapJSND Juice Achiv-315ml", "EvapJSND Juice Achiv-Pet", "EvapJSND Yoghurt Achiv-315ml"); //G2S>>>>>110625 CAS-01425-M8J6P2
                Clear(MixedPer);
                IF ("Evap & JSND JuiceTarget - 1Ltr" + "Evap & JSND JuiceTarget - Cans" + "Evap&JSND JuiTarget- Caprisun" +
                "Evap&JSND Yoghurt Target-1lit" + "Evap&JSND Yoghurt Target-90ml" +
                "Evap&JSND HollandiaTarget-120g" + "EvapJSND Hollandia Target-190g" + "EvapJSND Holla Target-UHT" + //G2S Added "EvapJSND Hollandia Target-190g"
                "EvapJSND Juice Target-315ml" + "EvapJSND Juice Target-Pet" + "EvapJSND Yoghurt Target-315ml" <> 0) //G2S>>>>>110625 CAS-01425-M8J6P2
                and
                ("Evap & JSND Juice Ach-1lit" + "Evap & JSND Juice Ach-cans" + "Evap & JSND Juice Ach caprisun" +
                "Evap & JSND Yoghurt Ache-1 Lit" + "Evap & JSND Yoghurt Ache-90 ML" + "Evap&JSND Hollandia Ache-120g" +
                "Evap&JSND Hollandia Ache-120g" + "EvapJSND Hollandia Achiv-190g" + "EvapJSND Holla Achiv-UHT" +
                "EvapJSND Juice Achiv-315ml" + "EvapJSND Juice Achiv-Pet" + "EvapJSND Yoghurt Achiv-315ml" <> 0) //G2S Added "EvapJSND Hollandia Achiv-190g" added Juice Achiv-Pet Yoghurt Achiv-315ml Juice Achiv-315ml 110625
                then
                    MixedPer := (("Evap & JSND Juice Ach-1lit" + "Evap & JSND Juice Ach-cans" + "Evap & JSND Juice Ach caprisun" +
                "Evap & JSND Yoghurt Ache-1 Lit" + "Evap & JSND Yoghurt Ache-90 ML" + "Evap&JSND Hollandia Ache-120g" + "EvapJSND Hollandia Achiv-190g" + //G2S Added "EvapJSND Hollandia Achiv-190g"
                "Evap&JSND Hollandia Ache-120g" + "EvapJSND Holla Achiv-UHT" + "EvapJSND Juice Achiv-315ml" + "EvapJSND Juice Achiv-Pet" + "EvapJSND Yoghurt Achiv-315ml") / ("Evap & JSND JuiceTarget - 1Ltr" + "Evap & JSND JuiceTarget - Cans" + "Evap&JSND JuiTarget- Caprisun" +
                "Evap&JSND Yoghurt Target-1lit" + "Evap&JSND Yoghurt Target-90ml" +
                "Evap&JSND HollandiaTarget-120g" + "EvapJSND Hollandia Target-190g" + "EvapJSND Holla Target-UHT" + "EvapJSND Juice Target-315ml" + "EvapJSND Juice Target-Pet" + "EvapJSND Yoghurt Target-315ml"));  //G2S Added "EvapJSND Hollandia Target-190g"

                //Excl JSND
                Clear(EvaPer);

                CalcFields("ExclJSND Juice Achiv-1lit", "ExclJSNDJuice Achiv-cans", "Excl JSND Juice Achie-caprisun");
                CalcFields("Excl JSND Yoghurt Achiv-1lit", "Excl JSND Yoghurt Achiv-90ml");
                IF ("ExclJSND Juice Achiv-1lit" + "ExclJSNDJuice Achiv-cans" + "Excl JSND Juice Achie-caprisun" + "Excl JSND Yoghurt Achiv-1lit" + "Excl JSND Yoghurt Achiv-90ml" <> 0)
                AND ("Excl JSND Juice Targe 1-lit" + "Excl JSND JuiceTarget -cans" + "ExclJSNDJuiceTarget -capricun" + "Excl JSND Yoghurt Target-1lit" + "Excl JSND Yoghurt Target-90ml" <> 0) then
                    EvaPer := (("ExclJSND Juice Achiv-1lit" + "ExclJSNDJuice Achiv-cans" + "Excl JSND Juice Achie-caprisun" + "Excl JSND Yoghurt Achiv-1lit" + "Excl JSND Yoghurt Achiv-90ml") / ("Excl JSND Juice Targe 1-lit" + "Excl JSND JuiceTarget -cans" + "ExclJSNDJuiceTarget -capricun" + "Excl JSND Yoghurt Target-1lit" + "Excl JSND Yoghurt Target-90ml"));

                //EXCL EVAP
                clear(JSNPer);   //G2s>>>>>>>Added 190gFC into the formula>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>020224
                CalcFields("ExclEVAP Holl Achv -120gFc", "ExclEVAP Holl Achv -190gFc", "ExclEVAP Holla Achiv-120gslim", "ExclEVAP Holla Achiv-50Fc", "ExclEVAP Holla Achiv-50gslim",
                "ExclEVAP Holl Achv -UHT MILK");

                IF (("ExclEVAP Holla Target-120g Fc" + "ExclEVAP Holla Target-190g Fc" + "ExclEVAP Holla Target-120gslim" + "ExclEVAP Holla Target-50g Fc" + "ExclEVAP Holla Target-50gslim" <> 0) AND ("ExclEVAP Holl Achv -190gFc" + "ExclEVAP Holl Achv -120gFc"
                + "Excl EVAP Hollan Disc-120gslim" + "ExclEVAP Holla Achiv-50Fc" + "ExclEVAP Holla Achiv-50gslim" + "ExclEVAP Holl Achv -UHT MILK" <> 0)) then
                    JSNPer := (("ExclEVAP Holl Achv -120gFc" + "ExclEVAP Holl Achv -190gFc" + "ExclEVAP Holla Achiv-120gslim" + "ExclEVAP Holla Achiv-50Fc" + "ExclEVAP Holla Achiv-50gslim" + "ExclEVAP Holl Achv -UHT MILK") / ("ExclEVAP Holla Target-120g Fc" + "ExclEVAP Holla Target-190g Fc" + "ExclEVAP Holla Target-120gslim"
                     + "ExclEVAP Holla Target-50g Fc" + "ExclEVAP Holla Target-50gslim" + "ExclEVAP Holla Target-UHT MILK"));
                //G2s>>>>>>>Added 190gFC into the formula>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>020224


                /* Clear(QtrPer);
                IF ("Qtrly Achievement" <> 0) AND ("Quarterly Target Value" <> 0) then
                    QtrPer := "Qtrly Achievement" / "Quarterly Target Value";
 */
                Clear(JSDAch);
                JSDAch := "Evap & JSND Juice Ach-1lit" + "Evap & JSND Juice Ach-cans" + "Evap & JSND Juice Ach caprisun" +
                "Evap & JSND Yoghurt Ache-1 Lit" + "Evap & JSND Yoghurt Ache-90 ML" + "Evap&JSND Hollandia Ache-120g" +
                "Evap&JSND Hollandia Ache-120g" + "EvapJSND Hollandia Achiv-190g"; //G2S Added "EvapJSND Hollandia Achiv-190g" 

                Clear(EvapAch);
                EvapAch := "ExclJSND Juice Achiv-1lit" + "ExclJSNDJuice Achiv-cans" + "Excl JSND Juice Achie-caprisun" + "Excl JSND Yoghurt Achiv-1lit" + "Excl JSND Yoghurt Achiv-90ml";

                //G2s>>>>>>>Added 190gFC into the formula>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>020224
                Clear(MixdAch);
                MixdAch := "ExclEVAP Holl Achv -190gFc" + "ExclEVAP Holl Achv -120gFc" + "ExclEVAP Holla Achiv-120gslim" + "ExclEVAP Holla Achiv-50Fc" + "ExclEVAP Holla Achiv-50gslim" +
                "ExclEVAP Holl Achv -UHT MILK";
                //G2s>>>>>>>Added 190gFC into the formula>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>020224

                //G2s>>>>>>>Added EvapJSND 190g into the formula>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>020224
                MidEvapAch := "Evap & JSND Juice Ach-1lit" + "Evap & JSND Juice Ach-cans" + "EvapJSND Hollandia Achiv-190g" + "Evap & JSND Juice Ach caprisun" + "Evap & JSND Yoghurt Ache-1 Lit" + "Evap & JSND Yoghurt Ache-90 ML"
                + "Evap&JSND Hollandia Ache-120g" + "Evap&JSND Hollandia Ache-120g" + "EvapJSND Holla Achiv-UHT" + "EvapJSND Juice Achiv-315ml" + "EvapJSND Juice Achiv-Pet" + "EvapJSND Yoghurt Achiv-315ml"; //G2S added Juice Achiv-Pet Yoghurt Achiv-315ml Juice Achiv-315ml 110625
                //G2s<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<020224

                ExclJsndAch := "ExclJSND Juice Achiv-1lit" + "ExclJSNDJuice Achiv-cans" + "Excl JSND Juice Achie-caprisun" + "Excl JSND Yoghurt Achiv-1lit" + "Excl JSND Yoghurt Achiv-90ml";

                //G2s>>>>>>>Added 190gFC into the formula>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>020224
                ExclEvapAch := "ExclEVAP Holl Achv -190gFc" + "ExclEVAP Holl Achv -120gFc" + "ExclEVAP Holla Achiv-120gslim" + "ExclEVAP Holla Achiv-50Fc" + "ExclEVAP Holla Achiv-50gslim" + "ExclEVAP Holl Achv -UHT MILK";
                //G2s>>>>>>>Added 190gFC into the formula>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>020224

                MidEvapTarget := "Evap & JSND JuiceTarget - 1Ltr" + "Evap & JSND JuiceTarget - Cans" + "Evap&JSND JuiTarget- Caprisun" +
                "Evap&JSND Yoghurt Target-1lit" + "Evap&JSND Yoghurt Target-90ml" +
                "Evap&JSND HollandiaTarget-120g" + "EvapJSND Hollandia Target-190g" + "EvapJSND Holla Target-UHT" + "EvapJSND Juice Target-315ml" + "EvapJSND Juice Target-Pet" + "EvapJSND Yoghurt Target-315ml"; //G2S Added "EvapJSND Hollandia Target-190g" Aadded Juice Target-Pet Yoghurt Target-315ml Juice Target-315ml 110625

                ExclJsndTarget := "Excl JSND Juice Targe 1-lit" + "Excl JSND JuiceTarget -cans" + "ExclJSNDJuiceTarget -capricun" + "Excl JSND Yoghurt Target-1lit" + "Excl JSND Yoghurt Target-90ml";

                //G2s>>>>>>>Added 190gFC into the formula>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>020224
                ExclEvapTarget := "ExclEVAP Holla Target-120g Fc" + "ExclEVAP Holla Target-190g Fc" + "ExclEVAP Holla Target-120gslim" + "ExclEVAP Holla Target-50g Fc" + "ExclEVAP Holla Target-50gslim" + "ExclEVAP Holla Target-UHT MILK";
                //G2s>>>>>>>Added 190gFC into the formula>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>020224

                MidEvapDisc := "Evap & JSND Juice Dis-1Ltr" + "Evap & JSND Juice Dis-cans" + "Evap & JSND Juice Dis-caprisun" +
                "Evap & JSND Yoghurt Disc-1lit" + "Evap & JSND Yoghurt Disc-90ml" + "Evap & JSND Hollan Disc-120g" + "EvapJSND Hollandia Disc-190g" + "EvapJSND Holla Disc-UHT" + "EvapJSND Juice Disc-315ml" + "EvapJSND Juice Disc-Pet" + "EvapJSND Yoghurt Disc-315ml";  //Added G2S "EvapJSND Hollandia Disc-190g" Added Juice Disc-Pet Yoghurt Disc-315ml Juice Disc-315ml 110625

                ExclJsndDisc := "Excl JSND Juice Discount -1lit" + "Excl JSND Juice Disc-cans" + "Excl JSND Juice Disc-caprisun" + "Excl JSND Yoghurt Disc-1lit" + "Excl JSND Yoghurt Disc-90ml";

                //G2s>>>>>>>Added 190gFC into the formula>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>020224
                ExclEvapDisc := "Excl EVAP Hollan Disc-120gFc" + "ExclEVAP Holla Disc-190gFc" + "Excl EVAP Hollan Disc-120gslim" + "Excl EVAP Hollan Disc-50gFc" + "Excl EVAP Hollan Disc-50gslim" + "ExclEVAP Holla Disc-UHT MILK";
                //G2s>>>>>>>Added 190gFC into the formula>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>020224

                // >>>>G2S>>>9805_CAS-01427-T0Z6Y9>>>>070425
                //4.5% on monthly Sales achieved
                MonthlySales45 := "Monthly Total Ach. Value" * 0.045;
                TotalMonthlySales45 += MonthlySales45;

                //Populate 0.2% on monthly Sales achieved (Shrinkage allowance)
                Shrinkage02 := "Shrinkage Base Amount" * 0.002;
                TotalShrinkage02 += Shrinkage02;
                // >>>>G2S>>>9805_CAS-01427-T0Z6Y9>>>>070425
            end;

        }
    }

    trigger OnInitReport()
    begin
        CompInf.GET();
        CompInf.CalcFields(Picture);
    end;




    var
        CompInf: Record "Company Information";
        kdCustFocus: Record "KD Cust. Focus Brands ";
        ParTargetPeriod: Text[20];
        MixedPer: Decimal;
        EvaPer: Decimal;
        JSNPer: Decimal;
        QtrPer: Decimal;
        JSDAch: Decimal;
        EvapAch: Decimal;
        MixdAch: Decimal;
        FD: Decimal;
        DiscA: Decimal;
        DiscB: Decimal;
        MixdD: Decimal;
        JSNDDic: Decimal;
        EvapD: Decimal;
        QR: Decimal;
        DD: Decimal;
        TR: Decimal;
        MonthlySales45, TotalMonthlySales45 : Decimal;
        Shrinkage02, TotalShrinkage02 : Decimal;
        QKeyDistributorTarget: Record "Key Distributor Targets";
        QRebateperiod2: Record "Rebate Period Codes";//PKONSE21
        QAPeriodFilter: Text[30];//PKONSE21
        PKKDRebateData: Record KDRebateData;
        PrevMon: Integer;
        PrevYear: Integer;
        TotalMonths: Integer;
        Customerrec2: Record customer;
        KDPeriod: Record "Rebate Period Codes";
        MidEvapAch: Decimal;
        ExclJsndAch: Decimal;
        ExclEvapAch: Decimal;
        MidEvapTarget: Decimal;
        ExclJsndTarget: Decimal;
        ExclEvapTarget: Decimal;
        MidEvapDisc: Decimal;
        ExclJsndDisc: Decimal;
        ExclEvapDisc: Decimal;
        DMS_UsageDisc: Decimal;
        StockNRMDisc: Decimal;
        WorkingCAPDisc: Decimal;
        VanMOVEDisc: Decimal;

    procedure GetTarget(Lpartarget: Code[20])
    Begin
        ParTargetPeriod := Lpartarget;
    End;

    procedure CalPeriod()//PKONSE21
    begin
        CLEAR(QAPeriodFilter);
        if COPYSTR(parTargetPeriod, 6) in ['03', '06', '09', '12'] then begin
            QRebateperiod2.GET(parTargetPeriod);
            QAPeriodFilter := QRebateperiod2.Code;
            QRebateperiod2.FIND('<');
            QAPeriodFilter += '|' + QRebateperiod2.Code;
            QRebateperiod2.FIND('<');
            QAPeriodFilter += '|' + QRebateperiod2.Code;
        end;
    end;
}