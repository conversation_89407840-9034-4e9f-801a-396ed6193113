page 50208 "Capex Budget"
{
    DeleteAllowed = false;
    PageType = Document;
    SourceTable = "Budget Header";
    SourceTableView = SORTING("No.", Status)
                      ORDER(Ascending)
                      WHERE(Status = FILTER(<> Released));
    UsageCategory = Documents;
    ApplicationArea = All;

    layout
    {
        area(content)
        {
            group(General)
            {
                Caption = 'General';
                field("No."; "No.")
                {

                    trigger OnAssistEdit();
                    begin
                        if AssistEdit(xRec) then
                            CurrPage.UPDATE;
                    end;
                }
                field(Description; Description)
                {
                }
                field("Description 2"; "Description 2")
                {
                }
                field("Document Date"; "Document Date")
                {
                }
                field("No. of Revisions"; "No. of Revisions")
                {
                }
                field("Utilisation Purpose"; "Utilisation Purpose")
                {
                    MultiLine = true;
                }
                field("Budget Name"; "Budget Name")
                {
                }
                field("Shortcut Dimension 1 Code"; "Shortcut Dimension 1 Code")
                {
                    trigger OnValidate()
                    Var
                        BudLne: Record "Budget Line";
                    BEGIN
                        BudLne.Reset;
                        BudLne.SetRange("Document Type", "Document Type");
                        BudLne.SetRange("Document No.", "No.");
                        IF BudLne.findset then
                            repeat
                                BudLne."Shortcut Dimension 1 Code" := "Shortcut Dimension 1 Code";
                                BudLne.Modify();
                            until BudLne.next = 0;
                    END;

                }
                field("Shortcut Dimension 2 Code"; "Shortcut Dimension 2 Code")
                {
                    trigger OnValidate()
                    Var
                        BudLne: Record "Budget Line";
                    BEGIN
                        BudLne.Reset;
                        BudLne.SetRange("Document Type", "Document Type");
                        BudLne.SetRange("Document No.", "No.");
                        IF BudLne.findset then
                            repeat
                                BudLne."Shortcut Dimension 2 Code" := "Shortcut Dimension 2 Code";
                                BudLne.Modify();
                            until BudLne.next = 0;
                    END;
                }
                field(Status; Status)
                {
                }
                field("Shortcut Dimension 4 Code"; "Shortcut Dimension 4 Code")
                {
                    trigger OnValidate()
                    Var
                        BudLne: Record "Budget Line";
                    BEGIN
                        BudLne.Reset;
                        BudLne.SetRange("Document Type", "Document Type");
                        BudLne.SetRange("Document No.", "No.");
                        IF BudLne.findset then
                            repeat
                                BudLne."Shortcut Dimension 4 Code" := "Shortcut Dimension 4 Code";
                                BudLne.Modify();
                            until BudLne.next = 0;
                    END;
                }
            }
            part(CapexLines; "Capex Budget Subform")
            {
                SubPageLink = "Document Type" = FIELD("Document Type"),
                              "Document No." = FIELD("No.");
            }
        }
        area(factboxes)
        {
            part(CapexBudgetListFactbox; "Capex Budget List Factbox")
            {
                ApplicationArea = Suite;
                Provider = CapexLines;
                SubPageLink = "Document No." = field("Document No.");
            }
            //>>>>>> G2S 122023
            part("Attached Documents"; "Document Attachment Factbox")
            {
                ApplicationArea = All;
                Caption = 'Attachments';
                SubPageLink = "Table ID" = CONST(50100),
                                "No." = FIELD("No.");
                // Type = FIELD("Voucher Type");
            }
            systempart(PyamentTermsLinks; Links)
            {
                ApplicationArea = RecordLinks;
            }

            systempart(Notes; Notes)
            {
                ApplicationArea = Notes;

            }
            //<<<<<< G2S 122023
        }

    }

    actions
    {
        area(navigation)
        {
            group("&Capex Budget")
            {
                Caption = '&Capex Budget';
                action(Dimensions)
                {
                    Caption = 'Dimensions';
                    ShortCutKey = 'Shift+Ctrl+D';

                    trigger OnAction();
                    begin
                        ShowDocDim;
                    end;
                }
                separator(Separator1102152037)
                {
                }
                action("&Approvals")
                {
                    Caption = '&Approvals';

                    trigger OnAction();
                    var
                        ApprovalEntries: Page "Approval Entries";
                    begin
                        ApprovalEntries.Setfilters(DATABASE::"Budget Header", 12, "No.");
                        ApprovalEntries.RUN;
                    end;
                }
            }
            group("F&unctions")
            {
                Caption = 'F&unctions';
                action("Send A&pproval Request")
                {
                    Caption = 'Send A&pproval Request';

                    trigger OnAction();
                    var
                    begin
                        if "Capex Status" = "Capex Status"::Closed then
                            ERROR('%1 %2 is already closed', "Document Type", "No.");

                        IF allinoneCU.CheckCPXApprovalsWorkflowEnabled(Rec) then
                            allinoneCU.OnSendCPXForApproval(Rec);

                    end;
                }
                action("Cancel Approval Re&quest")
                {
                    Caption = 'Cancel Approval Re&quest';

                    trigger OnAction();
                    begin
                        if "Capex Status" = "Capex Status"::Closed then
                            ERROR('%1 %2 is already closed', "Document Type", "No.");
                        allinoneCU.OnCancelCPXForApproval(Rec);

                    end;
                }
                separator(Separator1102152024)
                {
                }
                action("Re&lease")
                {
                    Caption = 'Re&lease';
                    ShortCutKey = 'Ctrl+F11';
                    //Visible = false;

                    trigger OnAction();
                    begin
                        if "Capex Status" = "Capex Status"::Closed then
                            ERROR('%1 %2 is already closed', "Document Type", "No.");

                        //Balu on Oct 27>>
                        IF WorkflowManagement.CanExecuteWorkflow(Rec, allinoneCU.RunworkflowOnSendCPXforApprovalCode()) then
                            error('Workflow is enabled. You can not release manually.');//Balu on Oct 27<<

                        //PerformManualRelease;
                        Release();
                    end;
                }

                action("Re&open")
                {
                    ApplicationArea = all;
                    Caption = 'Re&open';
                    Image = ReOpen;
                    trigger OnAction();
                    BEGIN
                        //Balu on Oct 27>>
                        if Status = Status::"Pending for Approval" then
                            Error('Approvals is Enabled Please Cancel Approvals');//Balu on Oct 27<<
                        Reopen();
                    END;
                }
                action("&Revise Capex Budget")
                {
                    Caption = '&Revise Capex Budget';

                    trigger OnAction();
                    begin
                        if "Capex Status" = "Capex Status"::Closed then
                            ERROR('%1 %2 is already closed', "Document Type", "No.");

                        PerformManualReopen;
                    end;
                }
                separator(Separator1102152033)
                {
                }
                action("Send Close Request")
                {
                    Caption = 'Send Close Request';

                    trigger OnAction();
                    begin
                        if "Capex Status" = "Capex Status"::Closed then
                            ERROR('%1 %2 is already closed', "Document Type", "No.");

                        //IF ApprovalMgt.SendBudgetClApprovalRequest(Rec) THEN;
                    end;
                }
                action("Close Cape&x Budget")
                {
                    Caption = 'Close Cape&x Budget';

                    trigger OnAction();
                    begin
                        CloseDocument;
                    end;
                }
                action("Create &FRS")
                {
                    Caption = 'Create &FRS';
                    Visible = false;

                    trigger OnAction();
                    begin
                        TESTFIELD(Status, Status::Released);
                        //FRSHeaderRec.CreateFRS("No.");//CLARITY
                    end;
                }
            }
            group("&Print")
            {
                Caption = '&Print';
                action("Print Capex Budget")
                {
                    Caption = 'Print Capex Budget';

                    trigger OnAction();
                    begin
                        /*
                        CapexBudgetHeaderRec.SETRANGE(CapexBudgetHeaderRec."No.","No.");
                        IF CapexBudgetHeaderRec.FINDFIRST THEN
                          IF CapexBudgetHeaderRec.Status = CapexBudgetHeaderRec.Status::Released THEN
                            REPORT.RUN(50474,TRUE,FALSE,CapexBudgetHeaderRec)
                          ELSE
                            ERROR(Text0002,CapexBudgetHeaderRec."No.");
                        */
                        ReportPrinted := false;
                        CapexBudgetHeaderRec.SETRANGE(CapexBudgetHeaderRec."No.", "No.");
                        if CapexBudgetHeaderRec.FINDFIRST then
                            if CapexBudgetHeaderRec.Status <> CapexBudgetHeaderRec.Status::Open then begin
                                REPORT.RUN(50474, false, false, CapexBudgetHeaderRec);
                                ReportPrinted := true;
                            end else
                                ERROR(Text0003);

                        if ReportPrinted = true then begin
                            "No. of Reprint" := "No. of Reprint" + 1;
                            MODIFY;
                        end;

                    end;
                }
                action(Preview)
                {
                    Caption = 'Preview';

                    trigger OnAction();
                    begin
                        ReportPrinted1 := false;
                        CapexBudgetHeaderRec.SETRANGE(CapexBudgetHeaderRec."No.", "No.");
                        if CapexBudgetHeaderRec.FINDFIRST then
                            if CapexBudgetHeaderRec.Status <> CapexBudgetHeaderRec.Status::Open then begin
                                REPORT.RUN(50474, true, false, CapexBudgetHeaderRec);
                                ReportPrinted1 := true;
                            end else
                                ERROR(Text0003);
                    end;
                }

                /*action("Re&lease")
                {
                    ApplicationArea = all;
                    Caption = 'Re&lease';
                    ShortCutKey = 'Ctrl+F11';
                    Image = ReleaseDoc;
                    trigger OnAction()

                    begin

                        IF Status <> Status::Released then BEGIN
                            Status := Status::Released;
                            Modify();
                            Message('Document has been Released.');
                        end;
                    end;
                }
                action("Re&open")
                {
                    ApplicationArea = all;
                    Caption = 'Re&open';
                    Image = ReOpen;
                    trigger OnAction();
                    begin
                        RecordRest.Reset();
                        RecordRest.SetRange(ID, 50018);
                        RecordRest.SetRange("Record ID", Rec.RecordId());
                        IF RecordRest.FindFirst() THEN
                            error('This record is under in workflow process. Please cancel approval request if not required.');
                        IF "Approval Status" <> "Approval Status"::Open then BEGIN
                            "Approval Status" := "Approval Status"::Open;
                            Modify();
                            Message('Document has been Reopened.');
                        end;
                    end;
                }*/

            }
        }
    }


    trigger OnAfterGetRecord()
    BEGIN
        OpenAppEntrExistsForCurrUser := approvalmngmt.HasOpenApprovalEntriesForCurrentUser(RecordId());
        OpenApprEntrEsists := approvalmngmt.HasOpenApprovalEntries(RecordId());
        CanCancelapprovalforrecord := approvalmngmt.CanCancelApprovalForRecord(RecordId());
        workflowwebhookmangt.GetCanRequestAndCanCancel(RecordId(), CanrequestApprovForFlow, CanCancelapprovalforflow);
    END;

    trigger OnModifyRecord(): Boolean
    Var
        BudLne: Record "Budget Line";
    BEGIN
        //        TestField(Status, Status::Open);

        BudLne.Reset;
        BudLne.SetRange("Document Type", "Document Type");
        BudLne.SetRange("Document No.", "No.");
        IF BudLne.findset then
            repeat
                BudLne."Shortcut Dimension 1 Code" := "Shortcut Dimension 1 Code";
                BudLne."Shortcut Dimension 2 Code" := "Shortcut Dimension 2 Code";
                BudLne."Shortcut Dimension 4 Code" := "Shortcut Dimension 4 Code";
                BudLne.Modify();
            until BudLne.next = 0;
    END;

    trigger OnDeleteRecord(): Boolean;
    begin
        DelDocNoArchive.ArchiveNo("No.", 10, TODAY, TIME, USERID, DATABASE::"Budget Header"); //HO1.0
    end;

    trigger OnNewRecord(BelowxRec: Boolean);
    begin
        "Document Type" := "Document Type"::Capex;
        "Dim. Document Type" := "Dim. Document Type"::Capex;
        "Document Date" := WORKDATE;
    end;

    var
        allinoneCU: Codeunit Codeunit1;
        approvalmngmt: Codeunit "Approvals Mgmt.";
        OpenAppEntrExistsForCurrUser: Boolean;
        OpenApprEntrEsists: Boolean;
        CanCancelapprovalforrecord: Boolean;
        CanCancelapprovalforflow: Boolean;
        CanrequestApprovForFlow: Boolean;
        workflowwebhookmangt: Codeunit "Workflow Webhook Management";
        ApprovalMgt: Codeunit 1535;
        ArchiveMgt: Codeunit ArchiveManagement;
        DelDocNoArchive: Codeunit "Deleted Doc. No. Archive";
        Text0001: Label '''%1'' FA Requisition Created Successfully';
        //FRSHeaderRec: Record "FRS Header";//CLARITY
        CapexBudgetHeaderRec: Record "Budget Header";
        Text0002: Label 'Un Approved Capex Cannot be Printed.  Capex No. ''%1'' not approved.';
        Text0003: Label 'Capex No. ''%1'' must be in Pending Approval before you can get a Print.';
        ReportPrinted: Boolean;
        ReportPrinted1: Boolean;
        BudgetLn: Record "Budget Line";
        WorkflowManagement: Codeunit "Workflow Management";
}

