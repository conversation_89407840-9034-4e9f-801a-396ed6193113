page 50435 "Posted Sales JV"
{
    // version CHI6.0

    // PROJECT : CHI 6.0
    // **********************************************************************************
    // SIGN
    // **********************************************************************************
    // CHI     :  CHI 6.0 Developments
    // UNL     :  Univision Nigeria Limited
    // SAA     :  SAHEED ADIO ADEOSUN
    // **********************************************************************************
    // VER      SIGN         DATE          DESCRIPTION
    // **********************************************************************************
    // 1.0      UNL      06-Dec-11      -> Form Created to display Posted Jnl Voucher.

    DeleteAllowed = false;
    Editable = false;
    InsertAllowed = false;
    ModifyAllowed = false;
    PageType = Document;
    SourceTable = "Posted Voucher Header";
    SourceTableView = WHERE("Voucher Type" = FILTER(JV),
                            "JV Type" = FILTER(Sales));
    layout
    {
        area(content)
        {
            group(General)
            {
                Caption = 'General';
                field("Document No."; "Document No.")
                {
                    ApplicationArea = all;
                }
                field("Document Date"; "Document Date")
                {
                    ApplicationArea = all;
                }
                field("JV Type"; "JV Type")
                {
                    ApplicationArea = all;
                }
                field(Narration; Narration)
                {
                    ApplicationArea = all;
                }
                field("Responsibility Center"; "Responsibility Center")
                {
                    ApplicationArea = all;
                }
            }
            part(VoucherLines; "Posted Journal Voucher Subform")
            {
                SubPageLink = "Document No." = FIELD("Document No.");
                SubPageView = SORTING("Document No.");
            }
            group(Usertrail)
            {
                Caption = 'Usertrail';
                field("Created By"; "Created By")
                {
                    ApplicationArea = all;
                }
                field("Created By Name"; "Created By Name")
                {
                    ApplicationArea = all;
                }
                field("Created Date"; "Created Date")
                {
                    ApplicationArea = all;
                }
                field("Created Time"; "Created Time")
                {
                    ApplicationArea = all;
                }
                field("Posted By"; "Posted By")
                {
                    ApplicationArea = all;
                }
                field("Posted By Name"; "Posted By Name")
                {
                    ApplicationArea = all;
                }
                field("Posted Date"; "Posted Date")
                {
                    ApplicationArea = all;
                }
                field("Posted Time"; "Posted Time")
                {
                    ApplicationArea = all;
                }
                field("Modified By"; "Modified By")
                {
                    ApplicationArea = all;
                }
                field("Modified By Name"; "Modified By Name")
                {
                    ApplicationArea = all;
                }
                field("Modified Date"; "Modified Date")
                {
                    ApplicationArea = all;
                }
                field("Modified Time"; "Modified Time")
                {
                    ApplicationArea = all;
                }
            }
        }
        area(FactBoxes)
        {
            part("Attached Documents"; "Document Attachment Factbox")
            {
                ApplicationArea = All;
                Caption = 'Attachments';
                SubPageLink = "Table ID" = CONST(50118),
                                "No." = FIELD("Document No.");
                // Type = FIELD("Voucher Type");
            }
            systempart(PyamentTermsLinks; Links)
            {
                ApplicationArea = RecordLinks;
            }
            systempart(PyamentTermsNotes; Notes)
            {
                ApplicationArea = Notes;
            }
        }
        //g2s29Dev23
    }

    actions
    {
        area(navigation)
        {
            group("&Voucher")
            {
                Caption = '&Voucher';
                action(Dimensions)
                {
                    Caption = 'Dimensions';

                    trigger OnAction();
                    begin
                        ShowDocDim;
                    end;
                }
                action(Approvals)
                {
                    Caption = 'Approvals';

                    trigger OnAction();
                    var
                        PostedApprovalEntries: Page "Posted Approval Entries";
                    begin
                        //PostedApprovalEntries.Setfilters(DATABASE::"Posted Voucher Header","Document No.");//CHI2018
                        PostedApprovalEntries.RUN;
                    end;
                }
            }
            group("&Line")
            {
                Caption = '&Line';
                action(Action1000000062)
                {
                    Caption = 'Dimensions';
                    ShortCutKey = 'Shift+Ctrl+D';

                    trigger OnAction();
                    begin
                        CurrPage.VoucherLines.PAGE.ShowLineDimensions;
                    end;
                }
                separator(Separator1000000070)
                {
                }
            }
        }
        area(processing)
        {
            action("&Print")
            {
                Caption = '&Print';
                Ellipsis = true;
                Promoted = true;
                PromotedCategory = Process;

                trigger OnAction();
                begin
                    VoucherHeader.SETRANGE("Voucher Type", "Voucher Type");
                    VoucherHeader.SETRANGE("Document No.", "Document No.");
                    if VoucherHeader.FINDFIRST then
                        REPORT.RUN(Report::"Posted Voucher", true, false, VoucherHeader);
                end;
            }
            action("&Navigate")
            {
                Caption = '&Navigate';
                Promoted = true;
                PromotedCategory = Process;

                trigger OnAction();
                begin
                    Navigate;
                end;
            }
        }
    }

    var
        PostedApprovalEntries: Page "Posted Approval Entries";
        VoucherHeader: Record "Posted Voucher Header";
}

