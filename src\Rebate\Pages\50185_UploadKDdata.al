report 50185 "Update Targets And DMS Data" //CR220036-PKON22MA21 Whole Object //PKON22J20
//B2BSP0N200722
{
    // version CR018

    Caption = 'Update Targets And DMS Data';
    ProcessingOnly = true;
    UseRequestPage = true;
    dataset
    {

    }

    requestpage
    {



        actions
        {

        }

        trigger OnQueryClosePage(CloseAction: Action): Boolean;
        begin
            IF CloseAction = ACTION::OK THEN BEGIN
                ServerFileNme := FileMgt.UploadFile(Text000, ExcelExtensionTok);
                IF ServerFileNme = '' THEN
                    EXIT(FALSE);

                SheetName := ExcelBuf.SelectSheetsName(ServerFileNme);
                IF SheetName = '' THEN
                    EXIT(FALSE);
            END;
        end;
    }

    labels
    {
    }

    trigger OnPreReport();
    begin
        ExcelBuf.LOCKTABLE;
        ExcelBuf.OpenBook(ServerFileNme, SheetName);
        ExcelBuf.ReadSheet;
        GetLastRowandColumn;

        FOR X := 2 TO TotalRows DO
            InsertData(X);


        ExcelBuf.DELETEALL;

        MESSAGE('Import Completed');
    end;

    var

        ExcelBuf: Record "Excel Buffer";
        ServerFileNme: Text[250];
        SheetName: Text[250];
        FileName: Text[250];
        TotalColumns: Integer;
        TotalRows: Integer;
        X: Integer;
        ExcelBuf1: Record "Excel Buffer";
        FileMgt: Codeunit "File Management";
        Text000: Label 'Import Data';
        ExcelExtensionTok: Text[250];
        Text005: Label 'Imported from Excel';
        Text006: Label 'Import Excel File';
        Text00005: Label '.xlsx';
        Text029: Label 'You must enter a file name.';
        DocumentNumber: code[20];
        TemName: Code[20];
        BatcName: Code[20];
        Cust: Record Customer;

    procedure GetLastRowandColumn();
    begin
        ExcelBuf.SETRANGE(ExcelBuf."Row No.", 1);
        TotalColumns := ExcelBuf.COUNT;

        ExcelBuf.RESET;
        IF ExcelBuf.FINDLAST THEN
            TotalRows := ExcelBuf."Row No.";
    end;

    procedure InsertData(RowNo: Integer);
    var
        ContractNo: Code[20];
        linen: Integer;
        KeyDistTarg: Record "Key Distributor Targets";
        Cust: Record Customer;
    begin
        /* KeyDistTarg.RESET;
         KeyDistTarg.SetRange("Target Period", GetValueAtCell(RowNo, 1));
         KeyDistTarg.SetRange("Customer No.", GetValueAtCell(RowNo, 2));
         KeyDistTarg.SetRange("Resposibility Center", GetValueAtCell(RowNo, 3));
         IF Not KeyDistTarg.FindFirst() THEN BEGIN
        Evaluate(KeyDistTarg."Target Period", GetValueAtCell(RowNo, 1));
        Evaluate(KeyDistTarg."Customer No.", GetValueAtCell(RowNo, 2));
        Evaluate(KeyDistTarg."Resposibility Center", GetValueAtCell(RowNo, 3));
        IF KeyDistTarg.Insert() THEN begin*/
        KeyDistTarg.Init();
        IF GetValueAtCell(RowNo, 1) <> '' THEN
            Evaluate(KeyDistTarg."Target Period", GetValueAtCell(RowNo, 1));
        IF GetValueAtCell(RowNo, 2) <> '' THEN
            Evaluate(KeyDistTarg."Customer No.", GetValueAtCell(RowNo, 2));
        IF GetValueAtCell(RowNo, 3) <> '' THEN
            Evaluate(KeyDistTarg."Resposibility Center", GetValueAtCell(RowNo, 3));
        KeyDistTarg.Insert;
        Cust.Get(KeyDistTarg."Customer No.");
        KeyDistTarg."Customer Name" := Cust.Name;
        IF GetValueAtCell(RowNo, 4) <> '' THEN
            Evaluate(KeyDistTarg."Evap & JSND JuiceTarget - 1Ltr", GetValueAtCell(RowNo, 4));
        IF GetValueAtCell(RowNo, 5) <> '' THEN
            Evaluate(KeyDistTarg."Evap & JSND JuiceTarget - Cans", GetValueAtCell(RowNo, 5));
        IF GetValueAtCell(RowNo, 6) <> '' THEN
            Evaluate(KeyDistTarg."Evap&JSND JuiTarget- Caprisun", GetValueAtCell(RowNo, 6));
        IF GetValueAtCell(RowNo, 7) <> '' THEN
            Evaluate(KeyDistTarg."Evap&JSND Yoghurt Target-1lit", GetValueAtCell(RowNo, 7));
        IF GetValueAtCell(RowNo, 8) <> '' THEN
            Evaluate(KeyDistTarg."Evap&JSND Yoghurt Target-90ml", GetValueAtCell(RowNo, 8));
        IF GetValueAtCell(RowNo, 9) <> '' THEN
            Evaluate(KeyDistTarg."Evap&JSND HollandiaTarget-120g", GetValueAtCell(RowNo, 9));
        //--------------------------------------------G2s Added EvapJSND Hollandia 190g

        //G2S>>>>>>>>>>>>>>>>#9593_CAS-01425-M8J6P2>>>>>>>>>>>>>>>>>>>>>>>>>>>>>030625 
        IF GetValueAtCell(RowNo, 10) <> '' THEN
            Evaluate(KeyDistTarg."EvapJSND Juice Target-315ml", GetValueAtCell(RowNo, 10));

        IF GetValueAtCell(RowNo, 11) <> '' THEN
            Evaluate(KeyDistTarg."EvapJSND Juice Target-Pet", GetValueAtCell(RowNo, 11));

        IF GetValueAtCell(RowNo, 12) <> '' THEN
            Evaluate(KeyDistTarg."EvapJSND Yoghurt Target-315ml", GetValueAtCell(RowNo, 12));
        //G2S>>>>>>>>>>>>>>>>#9593_CAS-01425-M8J6P2>>>>>>>>>>>>>>>>>>>>>>>>>>>>>030625 
        IF GetValueAtCell(RowNo, 13) <> '' THEN
            Evaluate(KeyDistTarg."EvapJSND Hollandia Target-190g", GetValueAtCell(RowNo, 13));

        IF GetValueAtCell(RowNo, 14) <> '' THEN
            Evaluate(KeyDistTarg."EvapJSND Holla Target-UHT", GetValueAtCell(RowNo, 14));


        IF GetValueAtCell(RowNo, 15) <> '' THEN
            Evaluate(KeyDistTarg."Excl JSND Juice Targe 1-lit", GetValueAtCell(RowNo, 15));
        IF GetValueAtCell(RowNo, 16) <> '' THEN
            Evaluate(KeyDistTarg."Excl JSND JuiceTarget -cans", GetValueAtCell(RowNo, 16));

        IF GetValueAtCell(RowNo, 17) <> '' THEN
            Evaluate(KeyDistTarg."ExclJSNDJuiceTarget -capricun", GetValueAtCell(RowNo, 17));

        IF GetValueAtCell(RowNo, 18) <> '' THEN
            Evaluate(KeyDistTarg."Excl JSND Yoghurt Target-1lit", GetValueAtCell(RowNo, 18));

        IF GetValueAtCell(RowNo, 19) <> '' THEN
            Evaluate(KeyDistTarg."Excl JSND Yoghurt Target-90ml", GetValueAtCell(RowNo, 19));

        //Added by G2S.... New ExclEvap 190gfc >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>290123     
        IF GetValueAtCell(RowNo, 20) <> '' THEN
            Evaluate(KeyDistTarg."ExclEVAP Holla Target-190g Fc", GetValueAtCell(RowNo, 20));

        IF GetValueAtCell(RowNo, 21) <> '' THEN
            Evaluate(KeyDistTarg."ExclEVAP Holla Target-120g Fc", GetValueAtCell(RowNo, 21));
        IF GetValueAtCell(RowNo, 22) <> '' THEN
            Evaluate(KeyDistTarg."ExclEVAP Holla Target-120gslim", GetValueAtCell(RowNo, 22));
        IF GetValueAtCell(RowNo, 23) <> '' THEN
            Evaluate(KeyDistTarg."ExclEVAP Holla Target-50g Fc", GetValueAtCell(RowNo, 23));
        IF GetValueAtCell(RowNo, 24) <> '' THEN
            Evaluate(KeyDistTarg."ExclEVAP Holla Target-50gslim", GetValueAtCell(RowNo, 24));

        IF GetValueAtCell(RowNo, 25) <> '' THEN
            Evaluate(KeyDistTarg."ExclEVAP Holla Target-UHT MILK", GetValueAtCell(RowNo, 25));

        IF GetValueAtCell(RowNo, 26) <> '' THEN
            Evaluate(KeyDistTarg."Van Move Target", GetValueAtCell(RowNo, 26));
        // IF GetValueAtCell(RowNo, 22) <> '' THEN
        //     Evaluate(KeyDistTarg.DMS_UsageDays, GetValueAtCell(RowNo, 22));
        IF GetValueAtCell(RowNo, 27) <> '' THEN
            Evaluate(KeyDistTarg."StockNorm Target", GetValueAtCell(RowNo, 27));
        IF GetValueAtCell(RowNo, 28) <> '' THEN
            Evaluate(KeyDistTarg."Sec. SalesValue", GetValueAtCell(RowNo, 28));
        IF GetValueAtCell(RowNo, 29) <> '' THEN
            Evaluate(KeyDistTarg."Working Cap Target", GetValueAtCell(RowNo, 29));
        IF GetValueAtCell(RowNo, 30) <> '' THEN
            Evaluate(KeyDistTarg."Working Cap Achieve", GetValueAtCell(RowNo, 30));

        IF GetValueAtCell(RowNo, 31) <> '' THEN
            Evaluate(KeyDistTarg.DMS_UsageDays, GetValueAtCell(RowNo, 31));
        //>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>290123


        //KeyDistTarg."Evap&JSND HollandiaTarget-120g" := KeyDistTarg."ExclEVAP Holla Target-120g Fc" + KeyDistTarg."ExclEVAP Holla Target-120gslim";
        //   IF GetValueAtCell(RowNo, 19) <> '' THEN
        //     Evaluate(KeyDistTarg."DMS Usage Days", GetValueAtCell(RowNo, 19));

        IF GetValueAtCell(RowNo, 32) <> '' THEN
            Evaluate(KeyDistTarg."Global Dimension 1 Code", GetValueAtCell(RowNo, 32));
        IF GetValueAtCell(RowNo, 33) <> '' THEN
            Evaluate(KeyDistTarg."Monthly Target Value", GetValueAtCell(RowNo, 33));
        IF GetValueAtCell(RowNo, 34) <> '' THEN
            Evaluate(KeyDistTarg."Quarterly Target Value", GetValueAtCell(RowNo, 34));


        KeyDistTarg.Modify();
        /*end;else begin
                KeyDistTarg.Init();
                Evaluate(KeyDistTarg."Target Period", GetValueAtCell(RowNo, 1));
                Evaluate(KeyDistTarg."Customer No.", GetValueAtCell(RowNo, 2));
                Evaluate(KeyDistTarg."Resposibility Center", GetValueAtCell(RowNo, 3));
                IF KeyDistTarg.Insert() THEN begin
                    IF GetValueAtCell(RowNo, 4) <> '' THEN
                        Evaluate(KeyDistTarg."Evap & JSND JuiceTarget - 1Ltr", GetValueAtCell(RowNo, 4));
                    IF GetValueAtCell(RowNo, 5) <> '' THEN
                        Evaluate(KeyDistTarg."Evap & JSND JuiceTarget - Cans", GetValueAtCell(RowNo, 5));
                    IF GetValueAtCell(RowNo, 6) <> '' THEN
                        Evaluate(KeyDistTarg."Evap&JSND JuiTarget- Caprisun", GetValueAtCell(RowNo, 6));
                    IF GetValueAtCell(RowNo, 7) <> '' THEN
                        Evaluate(KeyDistTarg."Evap&JSND Yoghurt Target-1lit", GetValueAtCell(RowNo, 7));
                    IF GetValueAtCell(RowNo, 8) <> '' THEN
                        Evaluate(KeyDistTarg."Evap&JSND Yoghurt Target-90ml", GetValueAtCell(RowNo, 8));
                    IF GetValueAtCell(RowNo, 9) <> '' THEN
                        Evaluate(KeyDistTarg."Evap&JSND HollandiaTarget-120g", GetValueAtCell(RowNo, 9));
                    IF GetValueAtCell(RowNo, 10) <> '' THEN
                        Evaluate(KeyDistTarg."Excl JSND Juice Targe 1-lit", GetValueAtCell(RowNo, 10));
                    IF GetValueAtCell(RowNo, 11) <> '' THEN
                        Evaluate(KeyDistTarg."Excl JSND JuiceTarget -cans", GetValueAtCell(RowNo, 11));

                    IF GetValueAtCell(RowNo, 12) <> '' THEN
                        Evaluate(KeyDistTarg."ExclJSNDJuiceTarget -capricun", GetValueAtCell(RowNo, 12));

                    IF GetValueAtCell(RowNo, 13) <> '' THEN
                        Evaluate(KeyDistTarg."Excl JSND Yoghurt Target-1lit", GetValueAtCell(RowNo, 13));

                    IF GetValueAtCell(RowNo, 14) <> '' THEN
                        Evaluate(KeyDistTarg."Excl JSND Yoghurt Target-90ml", GetValueAtCell(RowNo, 14));
                    IF GetValueAtCell(RowNo, 15) <> '' THEN
                        Evaluate(KeyDistTarg."ExclEVAP Holla Target-120g Fc", GetValueAtCell(RowNo, 15));
                    IF GetValueAtCell(RowNo, 16) <> '' THEN
                        Evaluate(KeyDistTarg."ExclEVAP Holla Target-120gslim", GetValueAtCell(RowNo, 16));
                    IF GetValueAtCell(RowNo, 17) <> '' THEN
                        Evaluate(KeyDistTarg."ExclEVAP Holla Target-50g Fc", GetValueAtCell(RowNo, 17));
                    IF GetValueAtCell(RowNo, 18) <> '' THEN
                        Evaluate(KeyDistTarg."ExclEVAP Holla Target-50gslim", GetValueAtCell(RowNo, 18));
                    KeyDistTarg."Evap&JSND HollandiaTarget-120g" := KeyDistTarg."ExclEVAP Holla Target-120g Fc" + KeyDistTarg."ExclEVAP Holla Target-120gslim";

                    IF GetValueAtCell(RowNo, 19) <> '' THEN
                        Evaluate(KeyDistTarg."DMS Usage Days", GetValueAtCell(RowNo, 19));
                    IF GetValueAtCell(RowNo, 20) <> '' THEN
                        Evaluate(KeyDistTarg."Global Dimension 1 Code", GetValueAtCell(RowNo, 20));
                    IF GetValueAtCell(RowNo, 21) <> '' THEN
                        Evaluate(KeyDistTarg."Monthly Target Value", GetValueAtCell(RowNo, 21));
                    IF GetValueAtCell(RowNo, 22) <> '' THEN
                        Evaluate(KeyDistTarg."Quarterly Target Value", GetValueAtCell(RowNo, 22));
                    KeyDistTarg.Modify();

                end;
        end;
        end;*/
    end;//B2BSPON20JU22

    procedure GetValueAtCell(RowNo: Integer; ColNo: Integer): Text;
    begin
        IF ExcelBuf1.GET(RowNo, ColNo) THEN
            EXIT(ExcelBuf1."Cell Value as Text");
    end;

    procedure FileNameOnAfterValidate()
    begin
        RequestFile();
    end;

    procedure RequestFile()
    begin
        IF FileName <> '' THEN
            ServerFileNme := FileMgt.UploadFile(Text006, FileName)
        ELSE
            ServerFileNme := FileMgt.UploadFile(Text006, Text00005);

        ValidateServerFileName;
        FileName := FileMgt.GetFileName(ServerFileNme);
    end;

    procedure ValidateServerFileName()
    begin
        IF ServerFileNme = '' THEN BEGIN
            FileName := '';
            SheetName := '';
            ERROR(Text029);
        END;
    end;

    procedure SetValues(DocNo: code[20]; TemplateName: code[20]; BatchName: code[20])
    begin
        DocumentNumber := DocNo;
        TemName := TemplateName;
        BatcName := BatchName;
    end;
}

