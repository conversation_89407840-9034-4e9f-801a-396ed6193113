page 50292 "Bank Receipt Voucher New"
{
    // version CHI6.0

    // PROJECT : CHI 6.0
    // **********************************************************************************
    // SIGN
    // **********************************************************************************
    // CHI     :  CHI 6.0 Developments
    // UNL     :  Univision Nigeria Limited
    // SAA     :  SAHEED ADIO ADEOSUN
    // HO      :  Henry <PERSON>
    // **********************************************************************************
    // VER    SIGN   DATE       DESCRIPTION
    // **********************************************************************************
    // 1.0    UNL    06-Dec-11  -> Form Created to display Bank Rcpt Document Details.
    // 
    // 3.0    SAA    30-Jan-12  -> New Functions "TellerChequeDateValidate","PayModeValidate","TellerBankNameValidate",
    //                             "BankNameValidate","ChequeTellerNoValidate" added to form.
    //                          -> New TAB "Receipt Details" created.
    // 
    //              13-Mar-12   -> Added codes to "Form-OnOpenForm" to filter out Responsibility Centres in Vouchers.
    //                          -> Added the "Responsibility Centre" field to the form
    // 
    // 1.0    HO    07-Sep-12   -> Code added to "Form-OnDeleteRecord()" to allow Archive of deleted Bank Receipt Voucher Document No.
    // 
    // 3.0    SAA   18-May-17   -> Code added to OnOpenForm() to filter responsibility centers for users.

    DeleteAllowed = false;
    //InsertAllowed = false;//PK
    PageType = Document;
    SourceTable = "Voucher Header";
    SourceTableView = SORTING("Voucher Type", "Document No.")
                      WHERE("Voucher Type" = CONST(BRV),
                            Status = FILTER(<> Released),
                            "Bank Receipt Type" = FILTER(Indirect | " "));

    layout
    {
        area(content)
        {
            group(General)
            {
                Caption = 'General';
                field("Document No."; "Document No.")
                {
                    ApplicationArea = all;
                    Editable = false;

                    trigger OnAssistEdit();
                    begin
                        if AssistEdit(xRec) then
                            CurrPage.UPDATE;
                    end;
                }
                field("Posting Date"; "Posting Date")
                {
                    ApplicationArea = all;
                    trigger OnValidate();
                    begin
                        // SAA 3.0 >>
                        PostingDateValidate;
                        // SAA 3.0 <<
                    end;
                }
                field("Account No."; "Account No.")
                {
                    ApplicationArea = all;
                    Caption = 'Debit Account No.';
                }
                field("Account Name"; "Account Name")
                {
                    ApplicationArea = all;
                }
                field("WHT Applicable"; "WHT Applicable")
                {
                    ApplicationArea = all;
                    trigger OnValidate()
                    var
                        genjoulrec: Record "Gen. Journal Line 2";//PK-GJ
                    begin
                        IF "WHT Applicable" then begin
                            genjoulrec.Reset();
                            genjoulrec.Setrange("Journal Template Name", 'BRV');
                            genjoulrec.Setrange("Journal Batch Name", 'BRV');
                            genjoulrec.Setrange("Voucher Type", genjoulrec."Voucher Type"::BRV);
                            genjoulrec.Setrange("Document No.", "Document No.");
                            IF genjoulrec.FindFirst() then
                                Error('You cannot insert more than one line if WHT is Applicable')
                        end Else Begin
                            genjoulrec.Reset();
                            genjoulrec.Setrange("Journal Template Name", 'BRV');
                            genjoulrec.Setrange("Journal Batch Name", 'BRV');
                            genjoulrec.Setrange("Voucher Type", genjoulrec."Voucher Type"::BRV);
                            genjoulrec.Setrange("Document No.", "Document No.");
                            IF genjoulrec.FindFirst() then
                                repeat
                                    Clear(genjoulrec."WHT %");
                                    Clear(genjoulrec."WHT Account");
                                    Clear(genjoulrec."WHT Amount");
                                    Clear(genjoulrec."WHT Amount(LCY)");
                                    Clear(genjoulrec."WHT Group");
                                    genjoulrec.Modify();
                                until genjoulrec.Next = 0;
                        End;
                    end;

                }
                field(Status; Status)
                {
                    ApplicationArea = all;
                }
                field(Narration; Narration)
                {
                    ApplicationArea = all;
                }
                field("Currency Code"; "Currency Code")
                {
                    ApplicationArea = all;
                    Editable = false;

                    trigger OnAssistEdit();
                    begin
                        ChangeExchangeRate.SetParameter("Currency Code", "Currency Factor", "Posting Date");
                        if ChangeExchangeRate.RUNMODAL = ACTION::OK then begin
                            VALIDATE("Currency Factor", ChangeExchangeRate.GetParameter);
                        end;
                        CLEAR(ChangeExchangeRate);
                    end;
                }
                field("Amount (LCY)"; "Amount (LCY)")
                {
                    ApplicationArea = all;
                }
                field("Shortcut Dimension 1 Code"; "Shortcut Dimension 1 Code")
                {
                    ApplicationArea = all;
                }
                field("Shortcut Dimension 2 Code"; "Shortcut Dimension 2 Code")
                {
                    ApplicationArea = all;
                }
                field("Bank Receipt Type"; "Bank Receipt Type")
                {
                    ApplicationArea = all;
                }

            }
            part(VoucherLines; "Bank Receipt Voucher Subform")
            {
                ApplicationArea = all;
                SubPageLink = "Journal Template Name" = FIELD("Journal Template Code"),
                              "Journal Batch Name" = FIELD("Journal Batch Name"),
                              "Document No." = FIELD("Document No.");
            }
            group(Usertrail)
            {
                Caption = 'Usertrail';
                field("Created By"; "Created By")
                {
                    ApplicationArea = all;
                }
                field("Created By Name"; "Created By Name")
                {
                    ApplicationArea = all;
                }
                field("Created Date"; "Created Date")
                {
                    ApplicationArea = all;
                }
                field("Created Time"; "Created Time")
                {
                    ApplicationArea = all;
                }
                field("Modified By"; "Modified By")
                {
                    ApplicationArea = all;
                }
                field("Modified By Name"; "Modified By Name")
                {
                    ApplicationArea = all;
                }
                field("Modified Date"; "Modified Date")
                {
                    ApplicationArea = all;
                }
                field("Modified Time"; "Modified Time")
                {
                    ApplicationArea = all;
                }
            }
            group("Receipt Details")
            {
                Caption = 'Receipt Details';
                field("Payment Mode"; "Payment Mode")
                {
                    ApplicationArea = all;
                    Caption = 'Receipt Mode';

                    trigger OnValidate();
                    begin
                        //  SAA 3.0 >>
                        PayModeValidate;
                        // SAA 3.0  <<
                    end;
                }
                field("Teller / Cheque Date"; "Teller / Cheque Date")
                {
                    ApplicationArea = all;
                    Editable = "Teller / Cheque DateEditable";

                    trigger OnValidate();
                    begin
                        // SAA 3.0 >>
                        TellerChequeDateValidate;
                        // SAA 3.0 <<
                    end;
                }
                field("Teller Bank Name"; "Teller Bank Name")
                {
                    ApplicationArea = all;
                    Editable = "Teller Bank NameEditable";

                    trigger OnValidate();
                    begin
                        // SAA 3.0 >>
                        TellerBankNameValidate;
                        // SAA 3.0 <<
                    end;
                }
                field("Bank Name"; "Bank Name")
                {
                    ApplicationArea = all;
                    Editable = "Bank NameEditable";

                    trigger OnValidate();
                    begin
                        // SAA 3.0 >>
                        BankNameValidate;
                        // SAA 3.0 <<
                    end;
                }
                field("Teller / Cheque No."; "Teller / Cheque No.")
                {
                    ApplicationArea = all;
                    Editable = "Teller / Cheque No.Editable";

                    trigger OnValidate();
                    begin
                        // SAA 3.0 >>
                        if "Teller / Cheque No." <> '' then
                            ChequeTellerNoValidate;
                        // SAA 3.0 <<
                    end;
                }
                field("Responsibility Center"; "Responsibility Center")
                {
                    ApplicationArea = all;
                }
                field("Receiving Type"; "Receiving Type")
                {
                    ApplicationArea = all;
                }
                field("Receiving Code"; "Receiving Code")
                {
                    ApplicationArea = all;
                }
                field("Received From"; "Received From")
                {
                    ApplicationArea = all;
                }
            }
        }
        area(FactBoxes)
        {
            part("Attached Documents"; "Document Attachment Factbox")
            {
                ApplicationArea = All;
                Caption = 'Attachments';
                SubPageLink = "Table ID" = CONST(50117),
                                "No." = FIELD("Document No.");
                // Type = FIELD("Voucher Type");
            }
            systempart(PyamentTermsLinks; Links)
            {
                ApplicationArea = RecordLinks;
            }
            systempart(PyamentTermsNotes; Notes)
            {
                ApplicationArea = Notes;
            }
        }
        //g2s29Dev23
    }

    actions
    {
        area(navigation)
        {
            group("&Voucher")
            {
                Caption = '&Voucher';
                separator(Separator1000000067)
                {
                }
                action(Dimensions)
                {
                    ApplicationArea = all;
                    Caption = 'Dimensions';
                    ShortCutKey = 'Shift+Ctrl+D';

                    trigger OnAction();
                    begin
                        ShowDocDim;
                    end;
                }
                separator(Separator1000000069)
                {
                }
                action(Approvals)
                {
                    ApplicationArea = all;
                    Caption = 'Approvals';

                    trigger OnAction();
                    begin
                        approvalmngmt.ApproveRecordApprovalRequest(RecordId());
                    end;
                }
                /*action(InsertLine)
                {
                    ApplicationArea = all;
                    Caption = 'Insert Line';
                    Promoted = true;
                    PromotedCategory = Process;
                    ShortcutKey = 'Ctrl+N';
                    trigger OnAction();
                    var
                        genjoulrec: Record "Gen. Journal Line";
                        linen: Integer;
                    begin
                        genjoulrec.RESET();
                        genjoulrec.SetRange("Journal Template Name", 'BRV');
                        genjoulrec.SetRange("Journal Batch Name", 'BRV');
                        //genjoulrec.SetRange("Document No.", "Document No.");
                        IF genjoulrec.FINDLAST THEN
                            linen := 10000 + genjoulrec."Line No."
                        else
                            linen := 10000;
                        IF "WHT Applicable" then begin
                            genjoulrec.Reset();
                            genjoulrec.Setrange("Journal Template Name", 'BRV');
                            genjoulrec.Setrange("Journal Batch Name", 'BRV');
                            genjoulrec.Setrange("Voucher Type", genjoulrec."Voucher Type"::BRV);
                            genjoulrec.Setrange("Document No.", "Document No.");
                            IF genjoulrec.FindFirst() then
                                Error('You cannot insert more than one line if WHT is Applicable')
                        end;
                        clear(genjoulrec);
                        genjoulrec.init();
                        genjoulrec."Journal Template Name" := 'BRV';
                        genjoulrec."Journal Batch Name" := 'BRV';
                        genjoulrec."Voucher Type" := genjoulrec."Voucher Type"::BRV;
                        genjoulrec."Document No." := "Document No.";
                        genjoulrec."Line No." := linen;
                        genjoulrec."Posting Date" := Today; //PJ                        
                        genjoulrec.insert(True);
                        Message('Line Inserted %1..%2', genjoulrec."Journal Template Name", genjoulrec."Journal Batch Name");
                        //insert(true);
                        CurrPage.Update();
                    end;
                }*/
            }
            group("F&unctions")
            {
                Caption = 'F&unctions';
                action("Send Approval Request")
                {
                    ApplicationArea = All;
                    Image = SendApprovalRequest;
                    Visible = Not OpenApprEntrEsists and CanrequestApprovForFlow;
                    Promoted = true;
                    PromotedIsBig = true;
                    PromotedCategory = Process;
                    PromotedOnly = true;
                    trigger OnAction()
                    var
                        GenLineLRec: Record "Gen. Journal Line";
                    begin
                        GenLineLRec.reset;
                        GenLineLRec.SetRange("Journal Template Name", "Journal Template Code");
                        GenLineLRec.SetRange("Journal Batch Name", "Journal Batch Name");
                        GenLineLRec.SetRange("Document No.", "Document No.");
                        GenLineLRec.SetRange(Quantity, 0);
                        IF GenLineLRec.findset then
                            repeat
                                error('quantity is zero in Gen Journal for Line No. %1', GenLineLRec."Line No.");
                            until GenLineLRec.next = 0;
                        TestField("Posting Date");
                        TestField("Account Type", "Account Type"::"Bank Account");
                        CheckDimensions();
                        ClearValues();
                        IF allinoneCU.CheckJournalVoucherApprovalsWorkflowEnabled(Rec) then
                            allinoneCU.OnSendJournalVoucherForApproval(Rec);
                    end;
                }
                action("Cancel Approval Request")
                {
                    ApplicationArea = All;
                    Image = CancelApprovalRequest;
                    Visible = CanCancelapprovalforrecord or CanCancelapprovalforflow;
                    Promoted = true;
                    PromotedIsBig = true;
                    PromotedCategory = Process;
                    PromotedOnly = true;
                    trigger OnAction()
                    begin
                        allinoneCU.OnCancelJournalVoucherForApproval(Rec);
                    end;
                }
                separator(Separator1000000049)
                {
                }
                action("Re&lease")
                {
                    ApplicationArea = all;
                    Caption = 'Re&lease';
                    ShortCutKey = 'Ctrl+F11';
                    Image = ReleaseDoc;
                    trigger OnAction()
                    begin
                        CheckDimensions();
                        TestField("Posting Date");
                        IF WorkflowManagement.CanExecuteWorkflow(Rec, allinoneCU.RunworkflowOnSendJournalVoucherforApprovalCode()) then
                            error('Workflow is enabled. You can not release manually.');
                        TestField("Bank Receipt Type");
                        IF Status <> Status::Released then BEGIN
                            Status := Status::Released;
                            Modify();
                            Message('Document has been Released.');
                        end;
                    end;
                }
                action("Re&open")
                {
                    ApplicationArea = all;
                    Caption = 'Re&open';
                    Image = ReOpen;
                    trigger OnAction();
                    begin
                        RecordRest.Reset();
                        RecordRest.SetRange(ID, 50117);
                        RecordRest.SetRange("Record ID", Rec.RecordId());
                        IF RecordRest.FindFirst() THEN
                            error('This record is under in workflow process. Please cancel approval request if not required.');
                        IF Status <> Status::Open then BEGIN
                            Status := Status::Open;
                            Modify();
                            Message('Document has been Reopened.');
                        end;
                    end;
                }
                separator("----")
                {
                    Caption = '----';
                }
                action(Comments)
                {
                    ApplicationArea = all;
                    Caption = 'Comments';
                    RunObject = Page "Approval Comments";
                    RunPageLink = "Document Type" = FILTER('BRV'),
                                  "Document No." = FIELD("Document No.");
                }
            }
            group("P&osting")
            {
                Caption = 'P&osting';
                action("P&ost")
                {
                    ApplicationArea = all;
                    Caption = 'P&ost';
                    ShortCutKey = 'F11';
                    Visible = false;

                    trigger OnAction();
                    begin
                        // SAA 3.0 >>
                        CheckHeaderLines(Rec);
                        // SAA 3.0 <<
                        VoucherPost.RUN(Rec);
                    end;
                }
                action(Preview)
                {
                    ApplicationArea = all;
                    Caption = 'Preview';
                    ShortCutKey = 'Shift+F2';

                    trigger OnAction();
                    var
                        GeneralJrnlBtchLRec: Record "Gen. Journal Batch";
                        DocumentNo: Code[20];
                        GLRegGRec: Record "G/L Register";
                        genJounlin: Record "Gen. Journal Line 2";
                        VoucherPreview: Codeunit "Voucher Preview Posting";
                    begin
                        DocumentNo := "Document No.";
                        ClearValues();
                        VoucherPreview.RUN(Rec);
                    end;
                }
                action("Post and &Print")
                {
                    ApplicationArea = all;
                    Caption = 'Post and &Print';
                    ShortCutKey = 'Shift+F11';

                    trigger OnAction();
                    begin
                        // SAA 3.0 >>
                        CheckHeaderLines(Rec);
                        // SAA 3.0 <<                        
                        VoucherPost.RUN(Rec);

                    end;
                }
                action("Bank Receipt Voucher Test Report")
                {
                    trigger OnAction()
                    var
                        VouHeader: Record "Voucher Header";
                    BEGIN
                        VouHeader.RESET;
                        VouHeader.SETRANGE("Voucher Type", "Voucher Type");
                        VouHeader.SETRANGE("Document No.", "Document No.");
                        if VouHeader.FINDFIRST then
                            REPORT.RUN(50563, true, false, VouHeader);
                    END;
                }
                //Balu ********>>
                action("Open Excel")
                {
                    ApplicationArea = all;
                    Caption = 'Open Excel';
                    Image = Open;
                    trigger OnAction()
                    var
                        GlLine2: Record "Gen. Journal Line 2";
                    begin
                        GlLine2.CreateExcel(Rec);
                    end;
                }
                //Balu ********<<
            }
        }
        area(processing)
        {
            action("&Print")
            {
                ApplicationArea = all;
                Caption = '&Print';
                Ellipsis = true;
                Promoted = true;
                PromotedCategory = Process;

                trigger OnAction();
                begin
                    VoucherHeader.SETRANGE("Voucher Type", "Voucher Type");
                    VoucherHeader.SETRANGE("Document No.", "Document No.");
                    if VoucherHeader.FINDFIRST then
                        REPORT.RUN(50082, true, false, VoucherHeader);
                end;
            }
        }
    }

    trigger OnDeleteRecord(): Boolean;
    begin
        DelDocNoArchive.ArchiveNo("Document No.", 21, TODAY, TIME, USERID, DATABASE::"Voucher Header"); //HO1.0
    end;

    trigger OnInit();
    begin
        "Teller Bank NameEditable" := true;
        "Teller / Cheque DateEditable" := true;
        "Bank NameEditable" := true;
        "Teller / Cheque No.Editable" := true;
    end;

    trigger OnNewRecord(BelowxRec: Boolean);
    begin
        "Dim. Document Type" := "Dim. Document Type"::BRV;
        // SAA 3.0 >>
        "Responsibility Center" := UserMg.GetVoucherFilter();
        // SAA 3.0 <<
    end;

    trigger OnOpenPage();
    begin
        /*// SAA 3.0 >>
        IF UserMgt.GetVoucherFilter() <> '' THEN BEGIN
          FILTERGROUP(2);
          SETRANGE("Responsibility Center",UserMgt.GetVoucherFilter());
          FILTERGROUP(0);
        END;
        // SAA 3.0 <<*/

        // SAA 3.0 >>
        Usersetup.GET(USERID);
        /*IF Usersetup.FilterResponsibilityCenter <>'' THEN BEGIN
          FILTERGROUP(2);
          SETFILTER("Responsibility Center",Usersetup.FilterResponsibilityCenter);
          FILTERGROUP(0);
        END; */
        // SAA 3.0 <<

        //saa 3.0 18/05/17   >>

        /*BuildFilter := RespCentFilter.BuildRespCentFilter;
        IF BuildFilter <>'' THEN BEGIN
          BuildFilter:= QT+QT+'|'+BuildFilter;
          FILTERGROUP(2);
          SETFILTER("Responsibility Center",BuildFilter);
          FILTERGROUP(0);
        END; */ //commented by saa3.0 18/05/17
                // <<

    end;

    trigger OnModifyRecord(): Boolean
    BEGIN
        TestField(Status, Status::Open);
    END;

    trigger OnAfterGetRecord()
    BEGIN
        OpenAppEntrExistsForCurrUser := approvalmngmt.HasOpenApprovalEntriesForCurrentUser(RecordId());
        OpenApprEntrEsists := approvalmngmt.HasOpenApprovalEntries(RecordId());
        CanCancelapprovalforrecord := approvalmngmt.CanCancelApprovalForRecord(RecordId());
        workflowwebhookmangt.GetCanRequestAndCanCancel(RecordId(), CanrequestApprovForFlow, CanCancelapprovalforflow);
    END;

    var
        VoucherHeader: Record "Voucher Header";
        ApprovalMgt: Codeunit 1535;
        ApprovalEntry: Record "Approval Entry";
        ReleaseVoucher: Codeunit "Voucher Release";
        ApprovalEntries: Page 658;
        ChangeExchangeRate: Page "Change Exchange Rate";
        GenJrnlLine: Record "Gen. Journal Line";
        ReportPrint: Codeunit "Test Report-Print";
        VoucherPost: Codeunit "Voucher Post";
        UserMgt: Codeunit "User Setup Management";
        UserMg: Codeunit "User Setup Management Ext";
        DelDocNoArchive: Codeunit "Deleted Doc. No. Archive";
        Text50000: Label 'Do wish to the Approval Request?';
        Usersetup: Record "User Setup";
        Text50001: Label 'Receipt Mode should not be Balnk';
        QT: Label '''';
        BuildFilter: Text[200];
        RespCentFilter: Codeunit "Responsibility Center Filter";
        [InDataSet]
        "Teller / Cheque No.Editable": Boolean;
        [InDataSet]
        "Bank NameEditable": Boolean;
        [InDataSet]
        "Teller / Cheque DateEditable": Boolean;
        [InDataSet]
        "Teller Bank NameEditable": Boolean;
        WorkflowManagement: Codeunit "Workflow Management";
        allinoneCU: codeunit IJLSubEvents;
        RecordRest: record "Restricted Record";
        approvalmngmt: Codeunit "Approvals Mgmt.";
        OpenApprEntrEsists: Boolean;
        CanrequestApprovForFlow: Boolean;
        CanCancelapprovalforrecord: Boolean;
        CanCancelapprovalforflow: Boolean;
        OpenAppEntrExistsForCurrUser: Boolean;
        workflowwebhookmangt: Codeunit "Workflow Webhook Management";

    procedure TellerChequeDateValidate();
    var
        Text50200: Label 'Cheque Date cannot be a future date';
    begin
        if "Teller / Cheque Date" > WORKDATE then
            ERROR(Text50200);
    end;

    procedure PayModeValidate();
    var
        Text50200: Label 'Payment Mode must not be blank in Bank Receipt';
    begin
        case "Payment Mode" of
            "Payment Mode"::" ":
                begin
                    ERROR(Text50200, "Payment Mode");
                end;
            "Payment Mode"::Cheque:
                begin
                    "Teller / Cheque No.Editable" := true;
                    "Bank NameEditable" := true;
                    "Teller / Cheque DateEditable" := true;
                    "Teller Bank Name" := 0;
                end;
            "Payment Mode"::Teller:
                begin
                    "Teller / Cheque No.Editable" := true;
                    "Bank Name" := '';
                    "Teller Bank Name" := 0;
                    "Bank NameEditable" := false;
                    "Teller / Cheque DateEditable" := true;
                    "Teller Bank NameEditable" := true;
                end;
            "Payment Mode"::PDC:
                begin
                    "Teller / Cheque No.Editable" := true;
                    "Bank NameEditable" := true;
                    "Teller / Cheque DateEditable" := true;
                    "Teller Bank Name" := 0;
                end;
        end;
    end;

    procedure TellerBankNameValidate();
    var
        Text50200: Label 'You cannot enter Teller Bank Name for Payment Type Cheque.';
    begin
        if "Payment Mode" = "Payment Mode"::Cheque then
            ERROR(Text50200);

        TESTFIELD("Teller / Cheque Date");
    end;

    procedure BankNameValidate();
    begin
        TESTFIELD("Teller / Cheque Date");
    end;

    procedure ChequeTellerNoValidate();
    var
        CustLedgRec: Record "Cust. Ledger Entry";
        Text50200: Label 'Teller Bank Name must not be blank.';
        Text50201: Label 'The Teller No. %1 already exist.';
        Text50202: Label 'Bank Name must not be blank.';
        Nonezero: Code[20];
        Text50203: Label 'You cannot start a Teller No. with zero (0)';
        TellerNo: Code[20];
        CustLedgRec2: Record "Cust. Ledger Entry";
    begin
        TESTFIELD("Payment Mode");

        Nonezero := COPYSTR("Teller / Cheque No.", 1, 1);
        if Nonezero = FORMAT(0) then
            ERROR(Text50203);

        case "Payment Mode" of
            "Payment Mode"::Teller:
                begin
                    /*
                    if "Teller Bank Name" = "Teller Bank Name" ::"0" then
                      ERROR(Text50200);
                    *///CHI 9.0

                    TellerNo := FORMAT("Teller Bank Name") + "Teller / Cheque No.";
                    if ("Teller Bank Name" = "Teller Bank Name"::UBA) or
                       ("Teller Bank Name" = "Teller Bank Name"::ZB) then begin
                        CustLedgRec.RESET;
                        CustLedgRec.SETCURRENTKEY("Teller / Cheque No.", "Posting Date");
                        CustLedgRec.SETRANGE(CustLedgRec."Teller / Cheque No.", TellerNo);
                        CustLedgRec.SETRANGE(CustLedgRec."Global Dimension 1 Code", "Shortcut Dimension 1 Code");
                        CustLedgRec.SETRANGE("Posting Date", CALCDATE('-60D', "Posting Date"), "Posting Date");
                        if CustLedgRec.FINDFIRST then begin
                            //IF CustLedgRec2.GET(CustLedgRec."Closed by Entry No.") THEN BEGIN
                            CustLedgRec2.RESET;
                            CustLedgRec2.SETRANGE("Customer No.", CustLedgRec."Customer No.");
                            CustLedgRec2.SETRANGE("Closed by Entry No.", CustLedgRec."Entry No.");
                            if CustLedgRec2.FINDFIRST then begin
                                if (CustLedgRec2."Document Type" <> CustLedgRec2."Document Type"::" ") then
                                    ERROR(Text50201, "Teller / Cheque No.");
                            end else
                                ERROR(Text50201, "Teller / Cheque No.");
                        end
                    end else begin
                        CustLedgRec.RESET;
                        CustLedgRec.SETCURRENTKEY("Teller / Cheque No.");
                        CustLedgRec.SETRANGE(CustLedgRec."Teller / Cheque No.", TellerNo);
                        if CustLedgRec.FINDFIRST then
                            ERROR(Text50201, "Teller / Cheque No.");
                    end;
                end;
            "Payment Mode"::Cheque:
                begin
                    if "Bank Name" = '' then
                        ERROR(Text50202);
                end;
        end;

        if "Teller / Cheque No." = '' then
            TellerNo := '';
    end;

    procedure CheckHeaderLines(VoucherHeaderRec: Record "Voucher Header");
    var
        GenJnlLine: Record "Gen. Journal Line 2";//PK-GJ2
        VendorLedgerEntry: Record "Vendor Ledger Entry";
    begin
        with VoucherHeaderRec do begin
            TESTFIELD("Account No.");
            TESTFIELD("Shortcut Dimension 1 Code");
            TESTFIELD("Shortcut Dimension 2 Code");
            TESTFIELD("Posting Date");
            TESTFIELD(Narration);
            if "Payment Mode" = "Payment Mode"::" " then
                ERROR(Text50001);
            //TESTFIELD("Payment Mode");
            TESTFIELD("Teller / Cheque Date");
            TESTFIELD("Bank Receipt Type");//SAA3.0

            if "Payment Mode" = VoucherHeader."Payment Mode"::Teller then
                TESTFIELD("Teller Bank Name") else
                TESTFIELD("Bank Name");

            TESTFIELD("Teller / Cheque No.");
            TESTFIELD("Receiving Type");
            if "Receiving Type" <> "Receiving Type"::Others then
                TESTFIELD("Receiving Code");

            //PayModeValidate;
            TESTFIELD(Narration);
            //ChequeTellerNoValidate;
            //TellerChequeDateValidate;
            //TellerBankNameValidate;
            //BankNameValidate;
            //PostingDateValidate;

            GenJnlLine.SETRANGE(GenJnlLine."Journal Template Name", "Journal Template Code");
            GenJnlLine.SETRANGE(GenJnlLine."Journal Batch Name", "Journal Batch Name");
            GenJnlLine.SETRANGE(GenJnlLine."Document No.", "Document No.");
            if GenJnlLine.FIND('-') then begin
                repeat
                    GenJnlLine.TESTFIELD("Account No.");
                    GenJnlLine.TESTFIELD("Posting Date");
                    GenJnlLine.TESTFIELD(Amount);
                    GenJnlLine.TESTFIELD("Shortcut Dimension 1 Code");
                    GenJnlLine.TESTFIELD("Shortcut Dimension 2 Code");
                    GenJnlLine.TESTFIELD("Description 2");
                    if GenJnlLine."Account Type" = GenJnlLine."Account Type"::Customer then
                        GenJnlLine.TESTFIELD("Responsibility Center");
                    //GenJnlLine.TESTFIELD(Cleared,TRUE);
                    // IF (GenJnlLine."Account Type" = GenJnlLine."Account Type"::Customer)
                    //OR (GenJnlLine."Bal. Account Type" = GenJnlLine."Bal. Account Type"::Customer) THEN
                    // GenJnlLine.TESTFIELD("Ship to Code");
                    if (GenJnlLine."Account Type" = GenJnlLine."Account Type"::Customer)
                      or (GenJnlLine."Bal. Account Type" = GenJnlLine."Bal. Account Type"::Customer) then
                        GenJnlLine.TESTFIELD("Responsibility Center");
                    //Nyo
                    if GenJrnlLine."Account Type" = GenJrnlLine."Account Type"::"Fixed Asset" then
                        GenJnlLine.TESTFIELD("FA Posting Type");
                    if (GenJnlLine."Account Type" = GenJnlLine."Account Type"::Customer)
                      or (GenJnlLine."Bal. Account Type" = GenJnlLine."Bal. Account Type"::Customer) then
                        GenJnlLine.TESTFIELD("Responsibility Center");
                    /*if GenJnlLine."FA Posting Type" = GenJnlLine."FA Posting Type" ::"capital work in progress" then begin
                      //TESTFIELD("Shortcut Dimension 4 Code");
                      GenJnlLine.TESTFIELD("Capex No.");
                      GenJnlLine.TESTFIELD("Capex Line No.");
                    end;*///CHI 9.0
                    if GenJnlLine."FA Posting Type" = GenJnlLine."FA Posting Type"::"Acquisition Cost" then begin
                        GenJnlLine.TESTFIELD("Capex No.");
                        GenJnlLine.TESTFIELD("Capex Line No.");
                    end;
                //Nyo


                //GenJnlLine."Posting Date" := TODAY;

                //GenJnlLine.MODIFY;
                until GenJnlLine.NEXT = 0;
            end;
        end;
    end;

    procedure PostingDateValidate();
    var
        Text50201: Label 'Posting Date must not be a future date';
    begin
        if "Posting Date" > TODAY then
            ERROR(Text50201);
    end;

    local procedure CheckDimensions()
    var
        GenJnlLine: Record "Gen. Journal Line 2"; //PKON22AP19
    begin
        TestField("Shortcut Dimension 1 Code");
        TestField("Shortcut Dimension 2 Code");
        GenJnlLine.Reset();
        GenJnlLine.SetRange("Journal Batch Name", "Journal Template Code");
        GenJnlLine.SetRange("Journal Batch Name", "Journal Batch Name");
        GenJnlLine.SetRange("Document No.", "Document No.");
        GenJnlLine.SetRange("Voucher type", GenJnlLine."Voucher type"::BRV);
        if GenJnlLine.FindSet() then
            repeat
                GenJnlLine.TestField("Shortcut Dimension 1 Code");
                GenJnlLine.TestField("Shortcut Dimension 2 Code");
                GenJnlLine.TestField(Amount);
            until GenJnlLine.Next() = 0;
    end;
}

