page 50282 "Approved Bank Receipt Vouchers"
{
    // version CHI6.0

    // PROJECT : CHI 6.0
    // **********************************************************************************
    // SIGN
    // **********************************************************************************
    // CHI     :  CHI 6.0 Developments
    // UNL     :  Univision Nigeria Limited
    // SAA     :  SAHEED ADIO ADEOSUN
    // HO      :  <PERSON>
    // **********************************************************************************
    // VER    SIGN   DATE       DESCRIPTION
    // **********************************************************************************
    // 1.0    UNL    06-Dec-11  -> Form Created to display Bank Rcpt Document Details.
    // 
    // 3.0    SAA    30-Jan-12  -> New Functions "TellerChequeDateValidate","PayModeValidate","TellerBankNameValidate",
    //                             "BankNameValidate","ChequeTellerNoValidate" added to PAGE.
    //                          -> New TAB "Receipt Details" created.
    // 
    //              13-Mar-12   -> Added codes to "Form-OnOpenForm" to filter out Responsibility Centres in Vouchers.
    //                          -> Added the "Responsibility Centre" field to the form
    // 
    // 1.0    HO    07-Sep-12   -> Code added to "Form-OnDelereRecord()" to allow Archive of deleted Bank Receipt Voucher Document No.

    Editable = false;
    PageType = Document;
    SourceTable = "Voucher Header";
    SourceTableView = SORTING("Voucher Type", "Document No.")
                      WHERE("Voucher Type" = CONST(BRV),
                            Status = CONST(Released),
                            "Bank Receipt Type" = FILTER(Indirect));
    layout
    {
        area(content)
        {
            group(General)
            {
                Caption = 'General';
                field("Document No."; "Document No.")
                {
                    ApplicationArea = all;
                    Editable = false;

                    trigger OnAssistEdit();
                    begin
                        if AssistEdit(xRec) then
                            CurrPage.UPDATE;
                    end;
                }
                field("Posting Date"; "Posting Date")
                {
                    ApplicationArea = all;
                    Editable = false;

                    trigger OnValidate();
                    begin
                        // SAA 3.0 >>
                        PostingDateValidate;
                        // SAA 3.0 <<
                    end;
                }
                field("Account No."; "Account No.")
                {
                    ApplicationArea = all;
                    Caption = 'Debit Account No.';
                    Editable = false;
                }
                field("Account Name"; "Account Name")
                {
                    ApplicationArea = all;
                }
                field(Status; Status)
                {
                    ApplicationArea = all;
                }
                field(Narration; Narration)
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Currency Code"; "Currency Code")
                {
                    ApplicationArea = all;
                    Editable = false;

                    trigger OnAssistEdit();
                    begin
                        ChangeExchangeRate.SetParameter("Currency Code", "Currency Factor", "Posting Date");
                        if ChangeExchangeRate.RUNMODAL = ACTION::OK then begin
                            VALIDATE("Currency Factor", ChangeExchangeRate.GetParameter);
                        end;
                        CLEAR(ChangeExchangeRate);
                    end;
                }
                field("Amount (LCY)"; "Amount (LCY)")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Shortcut Dimension 1 Code"; "Shortcut Dimension 1 Code")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Shortcut Dimension 2 Code"; "Shortcut Dimension 2 Code")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Responsibility Center"; "Responsibility Center")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Bank Receipt Type"; "Bank Receipt Type")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
            }
            part(VoucherLines; "Bank Receipt Voucher Subform")
            {
                ApplicationArea = all;
                SubPageLink = "Journal Template Name" = FIELD("Journal Template Code"),
                              "Journal Batch Name" = FIELD("Journal Batch Name"),
                              "Document No." = FIELD("Document No.");
            }
            group(Usertrail)
            {
                Caption = 'Usertrail';
                field("Created By"; "Created By")
                {
                    ApplicationArea = all;
                }
                field("Created By Name"; "Created By Name")
                {
                    ApplicationArea = all;
                }
                field("Created Date"; "Created Date")
                {
                    ApplicationArea = all;
                }
                field("Created Time"; "Created Time")
                {
                    ApplicationArea = all;
                }
                field("Modified By"; "Modified By")
                {
                    ApplicationArea = all;
                }
                field("Modified By Name"; "Modified By Name")
                {
                    ApplicationArea = all;
                }
                field("Modified Date"; "Modified Date")
                {
                    ApplicationArea = all;
                }
                field("Modified Time"; "Modified Time")
                {
                    ApplicationArea = all;
                }
            }
            group("Receipt Details")
            {
                Caption = 'Receipt Details';
                field("Payment Mode"; "Payment Mode")
                {
                    ApplicationArea = all;
                    Caption = 'Receipt Mode';

                    trigger OnValidate();
                    begin
                        //  SAA 3.0 >>
                        PayModeValidate;
                        // SAA 3.0  <<
                    end;
                }
                field("Teller / Cheque Date"; "Teller / Cheque Date")
                {
                    ApplicationArea = all;
                    Editable = "Teller / Cheque DateEditable";

                    trigger OnValidate();
                    begin
                        // SAA 3.0 >>
                        TellerChequeDateValidate;
                        // SAA 3.0 <<
                    end;
                }
                field("Teller Bank Name"; "Teller Bank Name")
                {
                    ApplicationArea = all;
                    Editable = "Teller Bank NameEditable";

                    trigger OnValidate();
                    begin
                        // SAA 3.0 >>
                        TellerBankNameValidate;
                        // SAA 3.0 <<
                    end;
                }
                field("Bank Name"; "Bank Name")
                {
                    ApplicationArea = all;
                    Editable = "Bank NameEditable";

                    trigger OnValidate();
                    begin
                        // SAA 3.0 >>
                        BankNameValidate;
                        // SAA 3.0 <<
                    end;
                }
                field("Teller / Cheque No."; "Teller / Cheque No.")
                {
                    ApplicationArea = all;
                    Editable = "Teller / Cheque No.Editable";

                    trigger OnValidate();
                    begin
                        // SAA 3.0 >>
                        ChequeTellerNoValidate;
                        // SAA 3.0 <<
                    end;
                }
                field("Receiving Type"; "Receiving Type")
                {
                    ApplicationArea = all;
                }
                field("Receiving Code"; "Receiving Code")
                {
                    ApplicationArea = all;
                }
                field("Received From"; "Received From")
                {
                    ApplicationArea = all;
                }
            }
        }
        area(FactBoxes)
        {
            part("Attached Documents"; "Document Attachment Factbox")
            {
                ApplicationArea = All;
                Caption = 'Attachments';
                SubPageLink = "Table ID" = CONST(50117),
                                "No." = FIELD("Document No.");
                // Type = FIELD("Voucher Type");
            }
            systempart(PyamentTermsLinks; Links)
            {
                ApplicationArea = RecordLinks;
            }
            systempart(PyamentTermsNotes; Notes)
            {
                ApplicationArea = Notes;
            }
        }
        //g2s29Dev23
    }

    actions
    {
        area(navigation)
        {
            group("&Voucher")
            {
                Caption = '&Voucher';
                separator(Separator1000000067)
                {
                }
                action(Dimensions)
                {
                    ApplicationArea = all;
                    Caption = 'Dimensions';
                    ShortCutKey = 'Shift+Ctrl+D';

                    trigger OnAction();
                    begin
                        ShowDocDim;
                    end;
                }
                separator(Separator1000000069)
                {
                }
                action(Approvals)
                {
                    ApplicationArea = all;
                    Caption = 'Approvals';

                    trigger OnAction();
                    begin
                        ApprovalEntries.Setfilters(DATABASE::"Voucher Header", 25, "Document No.");
                        ApprovalEntries.RUN;
                    end;
                }
            }
            group("&Line")
            {
                Caption = '&Line';
                action(Action1000000062)
                {
                    ApplicationArea = all;
                    Caption = 'Dimensions';
                    ShortCutKey = 'Shift+Ctrl+D';

                    trigger OnAction();
                    begin
                        CurrPage.VoucherLines.PAGE.ShowLineDimensions;
                    end;
                }
            }
            group("F&unctions")
            {
                Caption = 'F&unctions';
                action("Send A&pproval Request")
                {
                    ApplicationArea = all;
                    Caption = 'Send A&pproval Request';

                    trigger OnAction();
                    begin
                        CheckHeaderLines(Rec);
                        //IF ApprovalMgt.SendVoucherApprovalRequest(Rec) THEN;
                    end;
                }
                action("Cancel Approval Re&quest")
                {
                    ApplicationArea = all;
                    Caption = 'Cancel Approval Re&quest';

                    trigger OnAction();
                    begin
                        //IF ApprovalMgt.CancelVoucherApprovalRequest(Rec,TRUE,TRUE) THEN;
                    end;
                }
                separator(Separator1000000049)
                {
                }
                action("Re&lease")
                {
                    ApplicationArea = all;
                    Caption = 'Re&lease';
                    ShortCutKey = 'Ctrl+F11';
                    Visible = false;

                    trigger OnAction();
                    begin
                        CheckHeaderLines(Rec);
                        PerformManualRelease;
                    end;
                }
                action("Re&open")
                {
                    ApplicationArea = all;
                    Caption = 'Re&open';

                    trigger OnAction();
                    begin
                        if "Confirmed BY" <> '' then
                            ERROR(Text50001)
                        else begin
                            RecordRest.Reset();
                            RecordRest.SetRange(ID, 50117);
                            RecordRest.SetRange("Record ID", Rec.RecordId());
                            IF RecordRest.FindFirst() THEN
                                error('This record is under in workflow process. Please cancel approval request if not required.');
                            IF Status <> Status::Open then BEGIN
                                Status := Status::Open;
                                Modify();
                                Message('Document has been Reopened.');
                            end;
                        end;

                    end;
                }
            }
            group("P&osting")
            {
                Caption = 'P&osting';
                action("P&ost")
                {
                    ApplicationArea = all;
                    Caption = 'P&ost';
                    ShortCutKey = 'F11';
                    Visible = false;

                    trigger OnAction();
                    var
                        GeneralJrnlBtchLRec: Record "Gen. Journal Batch";
                        DocAttmtExt: Codeunit "Document Attachment Ext";
                    begin
                        GeneralJrnlBtchLRec.Reset();
                        GeneralJrnlBtchLRec.SetRange("Journal Template Name", 'BRV');
                        GeneralJrnlBtchLRec.SetRange(Name, 'BRV');
                        if GeneralJrnlBtchLRec.FindFirst() then begin
                            GeneralJrnlBtchLRec.TestField("No. Series", '');
                            GeneralJrnlBtchLRec.TestField("Posting No. Series", '');
                        end;
                        VoucherPost.RUN(Rec);
                        //g2s>>>>> TransferAttachment  150124 `
                        IF DocAttmtExt.IsAttachmentsEnabled() THEN BEGIN
                            MoveAttachmentPage.MoveAttachment(Rec);

                            MoveAttachmentPage.CopyLinksAndNotes(Rec);
                        END
                        //g2s>>>>>>>>>>>>>>>>>>>> 150124
                    end;
                }
                action(Preview)
                {
                    ApplicationArea = all;
                    Caption = 'Preview';
                    ShortCutKey = 'Shift+F2';

                    trigger OnAction();
                    var
                        GeneralJrnlBtchLRec: Record "Gen. Journal Batch";
                        DocumentNo: Code[20];
                        GLRegGRec: Record "G/L Register";
                        genJounlin: Record "Gen. Journal Line 2";
                        VoucherPreview: Codeunit "Voucher Preview Posting";
                    begin
                        DocumentNo := "Document No.";
                        ClearValues();
                        VoucherPreview.RUN(Rec);
                    end;
                }
                action("Post and &Print")
                {
                    ApplicationArea = all;
                    Caption = 'Post and &Print';
                    ShortCutKey = 'Shift+F11';

                    trigger OnAction();
                    var
                        GeneralJrnlBtchLRec: Record "Gen. Journal Batch";
                        DocAttmtExt: Codeunit "Document Attachment Ext";
                    begin
                        GeneralJrnlBtchLRec.Reset();
                        GeneralJrnlBtchLRec.SetRange("Journal Template Name", 'BRV');
                        GeneralJrnlBtchLRec.SetRange(Name, 'BRV');
                        if GeneralJrnlBtchLRec.FindFirst() then begin
                            GeneralJrnlBtchLRec.TestField("No. Series", '');
                            GeneralJrnlBtchLRec.TestField("Posting No. Series", '');
                        end;
                        VoucherPost.RUN(Rec);
                        //g2s>>>>> TransferAttachment  150124 `
                        IF DocAttmtExt.IsAttachmentsEnabled() THEN BEGIN
                            MoveAttachmentPage.MoveAttachment(Rec);

                            MoveAttachmentPage.CopyLinksAndNotes(Rec);
                        END;
                        //g2s>>>>>>>>>>>>>>>>>>>> 150124
                    end;
                }
                action("Bank Receipt Voucher Test Report")
                {
                    trigger OnAction()
                    var
                        VouHeader: Record "Voucher Header";
                    BEGIN
                        VouHeader.RESET;
                        VouHeader.SETRANGE("Voucher Type", "Voucher Type");
                        VouHeader.SETRANGE("Document No.", "Document No.");
                        if VouHeader.FINDFIRST then
                            REPORT.RUN(50563, true, false, VouHeader);
                    END;
                }
                //Balu ********>>
                action("Open Excel")
                {
                    ApplicationArea = all;
                    Caption = 'Open Excel';
                    Image = Open;
                    trigger OnAction()
                    var
                        GlLine2: Record "Gen. Journal Line 2";
                    begin
                        GlLine2.CreateExcel(Rec);
                    end;
                }
                //Balu ********<<
            }
        }
        area(processing)
        {
            action("&Print")
            {
                ApplicationArea = all;
                Caption = '&Print';
                Ellipsis = true;
                Promoted = true;
                PromotedCategory = Process;

                trigger OnAction();
                begin
                    VoucherHeader.SETRANGE("Voucher Type", "Voucher Type");
                    VoucherHeader.SETRANGE("Document No.", "Document No.");
                    if VoucherHeader.FINDFIRST then
                        REPORT.RUN(50194, true, false, VoucherHeader);
                end;
            }
        }
    }

    trigger OnDeleteRecord(): Boolean;
    begin
        DelDocNoArchive.ArchiveNo("Document No.", 21, TODAY, TIME, USERID, DATABASE::"Voucher Header"); //HO1.0
    end;

    trigger OnInit();
    begin
        "Teller Bank NameEditable" := true;
        "Teller / Cheque DateEditable" := true;
        "Bank NameEditable" := true;
        "Teller / Cheque No.Editable" := true;
    end;

    trigger OnNewRecord(BelowxRec: Boolean);
    begin
        "Dim. Document Type" := "Dim. Document Type"::BRV;
        // SAA 3.0 >>
        "Responsibility Center" := UserMgt.GetVoucherFilter();
        // SAA 3.0 <<
    end;

    trigger OnOpenPage();
    begin
        // SAA 3.0 >>
        /*IF UserMg.GetVoucherFilter() <> '' THEN BEGIN
          FILTERGROUP(2);
          SETRANGE("Responsibility Center",UserMg.GetVoucherFilter());
          FILTERGROUP(0);
        END;*/
        Usersetup.GET(USERID);

        // SAA 3.0 >>

        FILTERGROUP(2);
        SETFILTER("Responsibility Center", Usersetup.FilterResponsibilityCenter);
        FILTERGROUP(0);
        // SAA 3.0 <<

        if not Usersetup."View Indirect Bank Receipts" then
            ERROR(Text50000, Usersetup.FIELDCAPTION("View Indirect Bank Receipts"));

        // SAA 3.0 <<

    end;

    var
        VoucherHeader: Record "Voucher Header";
        ApprovalMgt: Codeunit 1535;
        ApprovalEntry: Record "Approval Entry";
        ReleaseVoucher: Codeunit "Voucher Release";
        ApprovalEntries: Page 658;
        ChangeExchangeRate: Page "Change Exchange Rate";
        GenJrnlLine: Record "Gen. Journal Line";
        ReportPrint: Codeunit "Test Report-Print";
        VoucherPost: Codeunit "Voucher Post";
        UserMgt: Codeunit "User Setup Management Ext";
        DelDocNoArchive: Codeunit "Deleted Doc. No. Archive";
        Usersetup: Record "User Setup";
        Text50000: Label 'You do not have permission to %1.';
        Text50001: Label 'You do not have permission to reopen the document.';
        [InDataSet]
        "Teller / Cheque No.Editable": Boolean;
        [InDataSet]
        "Bank NameEditable": Boolean;
        [InDataSet]
        "Teller / Cheque DateEditable": Boolean;
        [InDataSet]
        "Teller Bank NameEditable": Boolean;
        RecordRest: record "Restricted Record";

    procedure TellerChequeDateValidate();
    var
        Text50200: Label 'Cheque Date cannot be a future date';
    begin
        if "Teller / Cheque Date" > WORKDATE then
            ERROR(Text50200);
    end;

    procedure PayModeValidate();
    var
        Text50200: Label 'Payment Mode must not be blank in Bank Receipt';
    begin
        case "Payment Mode" of
            "Payment Mode"::" ":
                begin
                    ERROR(Text50200, "Payment Mode");
                end;
            "Payment Mode"::Cheque:
                begin
                    "Teller / Cheque No.Editable" := true;
                    "Bank NameEditable" := true;
                    "Teller / Cheque DateEditable" := true;
                    "Teller Bank Name" := 0;
                end;
            "Payment Mode"::Teller:
                begin
                    "Teller / Cheque No.Editable" := true;
                    "Bank Name" := '';
                    "Teller Bank Name" := 0;
                    "Bank NameEditable" := false;
                    "Teller / Cheque DateEditable" := true;
                    "Teller Bank NameEditable" := true;
                end;
            "Payment Mode"::PDC:
                begin
                    "Teller / Cheque No.Editable" := true;
                    "Bank NameEditable" := true;
                    "Teller / Cheque DateEditable" := true;
                    "Teller Bank Name" := 0;
                end;
        end;
    end;

    procedure TellerBankNameValidate();
    var
        Text50200: Label 'You cannot enter Teller Bank Name for Payment Type Cheque.';
    begin
        if "Payment Mode" = "Payment Mode"::Cheque then
            ERROR(Text50200);

        TESTFIELD("Teller / Cheque Date");
    end;

    procedure BankNameValidate();
    begin
        TESTFIELD("Teller / Cheque Date");
    end;

    procedure ChequeTellerNoValidate();
    var
        CustLedgRec: Record "Cust. Ledger Entry";
        Text50200: Label 'Teller Bank Name must not be blank.';
        Text50201: Label 'The Teller No. %1 already exist.';
        Text50202: Label 'Bank Name must not be blank.';
        Nonezero: Code[20];
        Text50203: Label 'You cannot start a Teller No. with zero (0)';
        TellerNo: Code[20];
        CustLedgRec2: Record "Cust. Ledger Entry";
    begin
        TESTFIELD("Payment Mode");

        Nonezero := COPYSTR("Teller / Cheque No.", 1, 1);
        if Nonezero = FORMAT(0) then
            ERROR(Text50203);

        case "Payment Mode" of
            "Payment Mode"::Teller:
                begin
                    /*
                    if "Teller Bank Name" = "Teller Bank Name" ::"0" then
                      ERROR(Text50200);*/ //CHI1.0

                    TellerNo := FORMAT("Teller Bank Name") + "Teller / Cheque No.";
                    if ("Teller Bank Name" = "Teller Bank Name"::UBA) or
                       ("Teller Bank Name" = "Teller Bank Name"::ZB) then begin
                        CustLedgRec.RESET;
                        CustLedgRec.SETCURRENTKEY("Teller / Cheque No.", "Posting Date");
                        CustLedgRec.SETRANGE(CustLedgRec."Teller / Cheque No.", TellerNo);
                        CustLedgRec.SETRANGE(CustLedgRec."Global Dimension 1 Code", "Shortcut Dimension 1 Code");
                        CustLedgRec.SETRANGE("Posting Date", CALCDATE('-60D', "Posting Date"), "Posting Date");
                        if CustLedgRec.FINDFIRST then begin
                            //IF CustLedgRec2.GET(CustLedgRec."Closed by Entry No.") THEN BEGIN
                            CustLedgRec2.RESET;
                            CustLedgRec2.SETRANGE("Customer No.", CustLedgRec."Customer No.");
                            CustLedgRec2.SETRANGE("Closed by Entry No.", CustLedgRec."Entry No.");
                            if CustLedgRec2.FINDFIRST then begin
                                if (CustLedgRec2."Document Type" <> CustLedgRec2."Document Type"::" ") then
                                    ERROR(Text50201, "Teller / Cheque No.");
                            end else
                                ERROR(Text50201, "Teller / Cheque No.");
                        end
                    end else begin
                        CustLedgRec.RESET;
                        CustLedgRec.SETCURRENTKEY("Teller / Cheque No.");
                        CustLedgRec.SETRANGE(CustLedgRec."Teller / Cheque No.", TellerNo);
                        if CustLedgRec.FINDFIRST then
                            ERROR(Text50201, "Teller / Cheque No.");
                    end;
                end;
            "Payment Mode"::Cheque:
                begin
                    if "Bank Name" = '' then
                        ERROR(Text50202);
                end;
        end;


        if "Teller / Cheque No." = '' then
            TellerNo := '';


        /*
        Nonezero:=COPYSTR("Teller / Cheque No.",1,1);
        IF Nonezero = FORMAT(0) THEN
          ERROR(Text50203);
        
        CASE "Payment Mode" OF
          "Payment Mode"::Teller:
            BEGIN
              IF "Teller Bank Name" = 0 THEN
                ERROR(Text50200);
        
              TellerNo:=FORMAT("Teller Bank Name") + "Teller / Cheque No.";
              CustLedgRec.SETCURRENTKEY("Teller / Cheque No.");
              CustLedgRec.SETRANGE(CustLedgRec."Teller / Cheque No.",TellerNo);
              IF CustLedgRec.FINDFIRST THEN
                //BEGIN
                  //IF CustLegRec."Teller No."="Teller No." THEN
                ERROR(Text50201,"Teller / Cheque No.")
                //END;
            END;
          "Payment Mode"::Cheque:
            BEGIN
              IF "Bank Name" ='' THEN
                ERROR(Text50202);
            END;
        END;
        
        IF "Teller / Cheque No." = '' THEN
          TellerNo := '';
        */

    end;

    procedure CheckHeaderLines(VoucherHeaderRec: Record "Voucher Header");
    var
        GenJnlLine: Record "Gen. Journal Line 2";//PK-GJ2
        VendorLedgerEntry: Record "Vendor Ledger Entry";
    begin
        with VoucherHeaderRec do begin
            TESTFIELD("Account No.");
            TESTFIELD("Shortcut Dimension 2 Code");
            TESTFIELD("Shortcut Dimension 1 Code");
            TESTFIELD("Posting Date");
            TESTFIELD(Narration);
            TESTFIELD("Payment Mode");
            TESTFIELD("Teller / Cheque Date");

            if "Payment Mode" = VoucherHeader."Payment Mode"::Teller then
                TESTFIELD("Teller Bank Name") else
                TESTFIELD("Bank Name");

            TESTFIELD("Teller / Cheque No.");
            TESTFIELD("Receiving Type");
            TESTFIELD("Receiving Code");

            //PayModeValidate;
            TESTFIELD(Narration);
            //ChequeTellerNoValidate;
            //TellerChequeDateValidate;
            //TellerBankNameValidate;
            //BankNameValidate;
            //PostingDateValidate;

            GenJnlLine.SETRANGE(GenJnlLine."Journal Template Name", "Journal Template Code");
            GenJnlLine.SETRANGE(GenJnlLine."Journal Batch Name", "Journal Batch Name");
            GenJnlLine.SETRANGE(GenJnlLine."Document No.", "Document No.");
            if GenJnlLine.FIND('-') then begin
                repeat
                    GenJnlLine.TESTFIELD("Account No.");
                    GenJnlLine.TESTFIELD("Posting Date");
                    GenJnlLine.TESTFIELD(Amount);
                    GenJnlLine.TESTFIELD("Shortcut Dimension 1 Code");
                    GenJnlLine.TESTFIELD("Shortcut Dimension 2 Code");
                //GenJnlLine.TESTFIELD("Description 2");
                //GenJnlLine.TESTFIELD(Cleared,TRUE);

                /*IF (GenJnlLine."Account Type" = GenJnlLine."Account Type"::Customer)
                  OR (GenJnlLine."Bal. Account Type" = GenJnlLine."Bal. Account Type"::Customer) THEN
                    GenJnlLine.TESTFIELD("Document Type",1);
                 */

                //GenJnlLine."Posting Date" := TODAY;

                //GenJnlLine.MODIFY;
                until GenJnlLine.NEXT = 0;
            end;
        end;

    end;

    procedure PostingDateValidate();
    var
        Text50201: Label 'Posting Date must not be a future date';
    begin
        if "Posting Date" > TODAY then
            ERROR(Text50201);
    end;

    var
        MoveAttachmentPage: Page "Approved General JVs";
}

