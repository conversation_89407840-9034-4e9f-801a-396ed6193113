report 50147 "Bank Confirmation FileCreation"
{
    // **********************************************************************************
    // SIGN
    // **********************************************************************************
    // HO      :  <PERSON>
    // **********************************************************************************
    // VER       SIGN       DATE          DESCRIPTION
    // **********************************************************************************
    // 1.0        HO     21-JAN-16     -> Code Added to G/L Entry GroupFooter (5) - OnPresection to generate for G/L Transaction related to
    //                                    staff.
    // CRF:2019-0068 NYO 14-08-19      -> Code added in Report - OnPreReport(), G/L Entry, GroupFooter (4) - OnPreSection() &
    //                                    G/L Entry, GroupFooter (6) - OnPreSection()

    ProcessingOnly = true;
    ApplicationArea = all;
    UsageCategory = ReportsAndAnalysis;
    Caption = 'Bank Confirmation FileCreation_50147';
    dataset
    {
        dataitem("G/L Entry"; "G/L Entry")
        {
            DataItemTableView = SORTING("Source Type", "Source No.", "Document No.", "Posting Date")
                                WHERE("Source Type" = FILTER("Bank Account" | Vendor | Customer | ' ' | "Fixed Asset"), Amount = filter(> 0));
            RequestFilterFields = "Document No.";

            trigger OnAfterGetRecord();
            var
                GLEntry: Record "G/L Entry";
                AmountLvar: Decimal;
            begin
                VendorName := '';
                VendorAccNo := '';
                VendorRtBkCode := '';
                BkAccNo := '';

                //G/L Entry, Body (4) - OnPreSection()

                CalcSums(Amount);
                IF (PrintGL) THEN BEGIN
                    DimMgt.GetShortcutDimensions("G/L Entry"."Dimension Set ID", ShortCutDimCode);
                    if (ShortCutDimCode[7] = PrevEmployee) then
                        CurrReport.Skip();
                    PrevEmployee := ShortCutDimCode[7];
                    PrevSourceNo := "Source No.";
                    PrevSourceType := "Source Type";
                    /*GLEntry.Reset();
                    GLEntry.SetRange("Document No.", "Document No.");
                    GLEntry.SetRange("Source No.", "Source No.");
                    GLEntry.SetFilter("Credit Amount", '<>%1', 0);
                    if GLEntry.FindSet() then begin
                        GLEntry.CalcSums(Amount);
                        AmountLvar := ABS(GLEntry.Amount);
                    end;*/
                    GLEntry.RESET;
                    GLEntry.SETRANGE(GLEntry."Document No.", "Document No.");
                    //VendLed.SETRANGE("Source No.", VendNo);
                    GLEntry.SetFilter(GLEntry."Source Type", '<>%1', GLEntry."Source Type"::"Bank Account");
                    IF GLEntry.FINDSET THEN BEGIN
                        REPEAT
                            DimMgt.GetShortcutDimensions(GLEntry."Dimension Set ID", ShortCutDimCode);
                            EmployeeDim := ShortCutDimCode[7];
                            if EmployeeDim = PrevEmployee then
                                AmountLvar += ABS(GLEntry.Amount);
                        UNTIL GLEntry.NEXT = 0;
                    END;
                    glentry.SETRANGE(glentry."Document No.", "Document No.");
                    glentry.SETRANGE(glentry."Source Type", glentry."Source Type"::"Bank Account");
                    IF glentry.FINDFIRST THEN
                        IF BankRec.GET(glentry."Source No.") THEN BEGIN
                            //IF BankRec."Teller Bank Name" <> BankRec."Teller Bank Name" ::ZB THEN  Comented by NYO
                            IF BankRec."Teller Bank Name" <> BankNames THEN
                                ERROR(text001, FORMAT(BankRec."Teller Bank Name"));
                            BkAccNo := BankRec."Bank Account No.";
                        END;

                    IF EmpRec.GET(PrevEmployee) THEN BEGIN
                        BankRoutingRec.RESET;
                        BankRoutingRec.SETRANGE("Bank Code", EmpRec."Bank Name");
                        IF BankRoutingRec.FINDFIRST THEN
                            RoutingCode := BankRoutingRec."Routing Code";
                    End;
                    //HO<<
                    if EmpRec."Bank Name" = EmpRec."Bank Name"::Polaris then begin
                        BankRoutingRec.RESET;
                        BankRoutingRec.SETRANGE("Bank Code", BankRoutingRec."Bank Code"::Polaris);
                        IF BankRoutingRec.FINDFIRST THEN
                            RoutingCode := BankRoutingRec."Routing Code";
                    end;
                    IF (PrintExcel) AND (BankNames = BankNames::ZB)
                          THEN BEGIN
                        RowNo += 1;
                        EnterCell(RowNo, 1, FORMAT("Document No.") + '_' + FORMAT("Description 2"), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 2, FORMAT(EmpRec."First Name" + ' ' + EmpRec."Middle Name" + ' ' + EmpRec."Last Name"), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 3, FORMAT(AmountLvar, 15, 1), TRUE, FALSE, FALSE, CellType::Number);
                        EnterCell(RowNo, 4, FORMAT("Posting Date", 10, '<Day,2>/<Month,2>/<Year4>'), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 5, FORMAT(EmpRec."No."), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 6, FORMAT(EmpRec."Bank Account No."), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 7, FORMAT(RoutingCode), TRUE, FALSE, FALSE, CellType::Text);
                        //IF COPYSTR(FORMAT(BkAccNo),1,1) = '0'  THEN
                        //  EnterCell(RowNo,8,QT+FORMAT(BkAccNo),TRUE,FALSE,FALSE)
                        //ELSE 
                        EnterCell(RowNo, 8, FORMAT(BkAccNo), TRUE, FALSE, FALSE, CellType::Text);
                    END;
                    IF (PrintExcel) AND (BankNames = BankNames::FBN)
                     THEN BEGIN
                        RowNo += 1;
                        EnterCell(RowNo, 1, FORMAT(COMPANYNAME), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 2, FORMAT(PrevEmployee), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 3, FORMAT(AmountLvar, 15, 1), TRUE, FALSE, FALSE, CellType::Number);
                        EnterCell(RowNo, 4, '**********', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 5, '011', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 6, FORMAT(EmpRec."Bank Account No."), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 7, COPYSTR(RoutingCode, 1, 3), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 8, FORMAT(EmpRec."First Name" + ' ' + EmpRec."Middle Name" + ' ' + EmpRec."Last Name"), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 9, FORMAT(TODAY), TRUE, FALSE, FALSE, CellType::Text);
                        //EnterCell(RowNo, 10, "Shortcut Dimension 12 Code" + '_' + "Document No." + Narration, TRUE, FALSE, FALSE);
                    END;
                    IF (PrintExcel) AND (BankNames = BankNames::GTB)
                            THEN BEGIN
                        RowNo += 1;
                        EnterCell(RowNo, 1, FORMAT(AmountLvar), TRUE, FALSE, FALSE, CellType::Number);
                        EnterCell(RowNo, 2, FORMAT(PostingDate), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 3, FORMAT("Document No.") + '/' + FORMAT(EmpRec."Search Name"), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 4, FORMAT("Document No.") + '/' + FORMAT("Description 2"), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 5, FORMAT(EmpRec."No."), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 6, FORMAT(EmpRec."First Name" + ' ' + EmpRec."Middle Name" + ' ' + EmpRec."Last Name"), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 7, FORMAT(EmpRec."Bank Account No."), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 8, FORMAT(RoutingCode), TRUE, FALSE, FALSE, CellType::Text);
                    END;
                    IF (PrintExcel) AND (BankNames = BankNames::STANBIC) THEN BEGIN
                        RowNo += 1;
                        EnterCell(RowNo, 1, FORMAT('P'), TRUE, FALSE, FALSE, CellType::Text);
                        //EnterCell(RowNo,2,FORMAT(PostingDate),TRUE,FALSE,FALSE);
                        EnterCell(RowNo, 2, FORMAT(TODAY, 10, '<Day,2>/<Month,2>/<Year4>'), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 3, FORMAT(PaymentProduct), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 4, FORMAT('CHI_LIMITED'), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 5, FORMAT(BkAccNo), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 6, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 7, FORMAT(EmpRec."First Name" + ' ' + EmpRec."Middle Name" + ' ' + EmpRec."Last Name"), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 8, FORMAT(ROUND(AmountLvar, 0.01, '=')), TRUE, FALSE, FALSE, CellType::Number);
                        EnterCell(RowNo, 9, FORMAT('NGN'), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 10, FORMAT(EmpRec."Bank Account No."), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 11, FORMAT(RoutingCode), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 12, FORMAT("Document No.") + '_' + FORMAT("Description 2"), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 13, FORMAT("Document No.") + '_' + FORMAT("Description 2" + ' Trf from CHI Ltd'), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 14, FORMAT(UserSetup."E-Mail"), TRUE, FALSE, FALSE, CellType::Text);
                    END;

                    IF (PrintExcel) AND (BankNames = BankNames::UBA)
                    THEN BEGIN

                        DateDay := DATE2DMY(TODAY, 1);
                        DateMonth := FORMAT(DATE2DMY(TODAY, 2));
                        DateYear := DATE2DMY(TODAY, 3);
                        PostingDate := FORMAT(DateYear) + FORMAT(DateMonth) + FORMAT(DateDay);

                        RowNo += 1;
                        EnterCell(RowNo, 1, FORMAT('CHI'), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 2, FORMAT('STAFFPAY'), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 3, FORMAT(BkAccNo), TRUE, FALSE, FALSE, CellType::Text);
                        //EnterCell(RowNo,3,FORMAT(TODAY,10,'<Day,2>/<Month,2>/<Year4>'),TRUE,FALSE,FALSE);
                        EnterCell(RowNo, 4, FORMAT(PostingDate), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 5, FORMAT(PostingDate) + FORMAT("Document No."), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 6, FORMAT(PostingDate) + FORMAT("Document No.") + FORMAT(RowNo), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 7, FORMAT(ROUND(AmountLvar, 0.01, '=')), TRUE, FALSE, FALSE, CellType::Number);
                        EnterCell(RowNo, 8, FORMAT('NGN'), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 9, FORMAT(EmpRec."No."), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 10, FORMAT(EmpRec."First Name" + ' ' + EmpRec."Middle Name" + ' ' + EmpRec."Last Name"), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 11, FORMAT(RoutingCode), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 12, FORMAT(EmpRec."Bank Name"), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 13, FORMAT(EmpRec."Bank Account No."), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 14, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 15, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 16, FORMAT("G/L Account Name"), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 17, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 18, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 19, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 20, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 21, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 22, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 23, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 24, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 25, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 26, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 27, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 28, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 29, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 30, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 31, FORMAT("Document No.") + '_' + FORMAT("Description 2"), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 32, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 33, FORMAT("Document No.") + '_' + FORMAT(Narration + ' Trf from CHI Ltd'), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 34, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 35, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 36, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 37, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 38, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 39, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 40, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 41, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                    END;

                    //CRF:2019-0068 NYO 14-08-19 >>
                    IF (PrintExcel) AND (BankNames = BankNames::CITI)
                    THEN BEGIN

                        NetPayments := '';
                        DateText := '';
                        DateText := FORMAT(TODAY, 10, '<YEAR4><MONTH,2><DAY,2>');
                        DateText := DELCHR(DateText, '=', ' ');
                        NetPayments := FORMAT(AmountLvar);
                        NetPayments := DELCHR(NetPayments, '=', ',');
                        TXTLINES := KT + 'NG' + KT + 'DFT' + KT + DateText + KT + KT + KT + KT + KT + 'NGN' + KT + FORMAT(NetPayments) + KT + KT +
                                  '**********' + KT + KT + KT + KT + KT + KT + KT + KT + KT + KT + KT + KT + KT + 'PAYMEN' + KT + KT + KT + KT + KT + KT + KT + KT + KT + KT + KT + KT + KT + KT + KT + KT + KT + KT + KT +
                                  EmpRec."Bank Account No." + KT + EmpRec."First Name" + ' ' + EmpRec."Middle Name" + ' ' + EmpRec."Last Name" + KT + KT + KT + KT + KT + KT + FORMAT(RoutingCode) + KT + KT + KT + KT + KT + KT +
                                  KT + KT + KT + KT + KT + KT + KT + KT + KT + KT + KT + KT + KT + KT + KT + 'PAYMENT' + KT + KT + KT + KT + KT + KT + KT + KT + KT + KT + KT + KT + KT;
                        if FilePath = '' then begin
                            GLSetup.Get();
                            FilePath := GLSetup."Others Bank file path" + Format("Document No.") + '.txt';
                            CreateFile.CREATE(FilePath);
                            CreateFile.CreateOutStream(FileOutstream);
                        end;
                        FileOutstream.WriteText(TXTLINES);
                        FileOutstream.WriteText();

                    END;
                    //CRF:2019-0068 NYO 14-08-19 <<
                    //G/L Entry, Body (4) - OnPreSection()END

                    //G/L Entry, GroupFooter (6) - OnPreSection()
                end;
                IF (NOT PrintGL) THEN BEGIN
                    if (PrevSourceNo = "Source No.") and (PrevSourceType = "Source Type") then
                        CurrReport.Skip();
                    PrevSourceNo := "Source No.";
                    PrevSourceType := "Source Type";
                    GLEntry.Reset();
                    GLEntry.SetRange("Document No.", "Document No.");
                    GLEntry.SetRange("Source No.", "Source No.");
                    GLEntry.SetFilter("G/L Account No.", '<>%1', '411200');
                    GLEntry.SetFilter(Amount, '>%1', 0);
                    if GLEntry.FindSet() then begin
                        GLEntry.CalcSums(Amount);
                        AmountLvar := ABS(GLEntry.Amount);
                    end;
                    WHTLedgerEntry.Reset();
                    WHTLedgerEntry.SetRange("DocNo.", "Document No.");
                    WHTLedgerEntry.SetRange("Party Type", WHTLedgerEntry."Party Type"::Vendor);
                    WHTLedgerEntry.SetRange("Party No.", "Source No.");
                    WHTLedgerEntry.SetRange("WHT Account", '411200');
                    if WHTLedgerEntry.FindSet() then begin
                        WHTLedgerEntry.CalcSums("WHT Amount");
                        AmountLvar := AmountLvar - Abs(WHTLedgerEntry."WHT Amount");
                    end;
                    IF VendorRec.GET("Source No.") THEN BEGIN
                        VendorName := VendorRec.Name;
                        VendorAccNo := VendorRec."Bank No.";
                        VendorRtBkCode := VendorRec."Bank Routing Code";
                    END;
                    if "Source Type" = "Source Type"::"Fixed Asset" then begin
                        DimMgt.GetShortcutDimensions("G/L Entry"."Dimension Set ID", ShortCutDimCode);
                        if EmpRec.Get(ShortCutDimCode[7]) then begin
                            VendorName := EmpRec."First Name" + ' ' + EmpRec."Middle Name" + ' ' + EmpRec."Last Name";
                            VendorAccNo := EmpRec."Bank Account No.";
                            BankRoutingRec.RESET;
                            BankRoutingRec.SETRANGE("Bank Code", EmpRec."Bank Name");
                            IF BankRoutingRec.FINDFIRST THEN
                                VendorRtBkCode := BankRoutingRec."Routing Code";
                            //VendorRtBkCode := EmpRec."Bank Routing Code";
                        end;
                    end;
                    glentry.Reset();
                    glentry.SETRANGE(glentry."Document No.", "Document No.");
                    glentry.SETRANGE(glentry."Source Type", glentry."Source Type"::"Bank Account");
                    IF glentry.FINDFIRST THEN
                        IF BankRec.GET(glentry."Source No.") THEN BEGIN
                            BkAccNo := BankRec."Bank Account No.";
                            IF BankRec."Teller Bank Name" <> BankNames THEN
                                ERROR(text001, FORMAT(BankRec."Teller Bank Name"));
                        END;

                    //HO<<
                    DimMgt.GetShortcutDimensions("G/L Entry"."Dimension Set ID", ShortCutDimCode);
                    if EmpRec.Get(ShortCutDimCode[7]) then begin
                        BankRoutingRec.RESET;
                        BankRoutingRec.SETRANGE("Bank Code", EmpRec."Bank Name");
                        IF BankRoutingRec.FINDFIRST THEN
                            RoutingCode := BankRoutingRec."Routing Code";
                    end;
                    IF (PrintExcel) AND (BankNames = BankNames::ZB) AND ("Source Type" = "Source Type"::Vendor) THEN BEGIN
                        RowNo += 1;
                        EnterCell(RowNo, 1, FORMAT("Document No.") + '_' + FORMAT("Description 2"), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 2, FORMAT(VendorName), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 3, FORMAT(AmountLvar, 15, 1), TRUE, FALSE, FALSE, CellType::Number);
                        EnterCell(RowNo, 4, FORMAT("Posting Date", 10, '<Day,2>/<Month,2>/<Year4>'), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 5, FORMAT("Source No."), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 6, FORMAT(VendorAccNo), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 7, FORMAT(VendorRtBkCode), TRUE, FALSE, FALSE, CellType::Text);
                        //IF COPYSTR(FORMAT(BkAccNo),1,1) = '0'  THEN
                        //  EnterCell(RowNo,8,QT+FORMAT(BkAccNo),TRUE,FALSE,FALSE)
                        //ELSE 
                        EnterCell(RowNo, 8, FORMAT(BkAccNo), TRUE, FALSE, FALSE, CellType::Text);
                    END;
                    IF (PrintExcel) AND (BankNames = BankNames::ZB) AND ("Source Type" = "Source Type"::"Fixed Asset") THEN BEGIN
                        RowNo += 1;
                        EnterCell(RowNo, 1, FORMAT("Document No.") + '_' + FORMAT("Description 2"), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 2, FORMAT(VendorName), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 3, FORMAT(AmountLvar, 15, 1), TRUE, FALSE, FALSE, CellType::Number);
                        EnterCell(RowNo, 4, FORMAT("Posting Date", 10, '<Day,2>/<Month,2>/<Year4>'), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 5, FORMAT("Source No."), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 6, FORMAT(VendorAccNo), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 7, FORMAT(VendorRtBkCode), TRUE, FALSE, FALSE, CellType::Text);
                        //IF COPYSTR(FORMAT(BkAccNo),1,1) = '0'  THEN
                        //  EnterCell(RowNo,8,QT+FORMAT(BkAccNo),TRUE,FALSE,FALSE)
                        //ELSE 
                        EnterCell(RowNo, 8, FORMAT(BkAccNo), TRUE, FALSE, FALSE, CellType::Text);
                    END;
                    //HO<<
                    IF (PrintExcel) AND (BankNames = BankNames::ZB) AND ("Source Type" = "Source Type"::Customer)
                      THEN BEGIN
                        RowNo += 1;
                        EnterCell(RowNo, 1, FORMAT("Document No.") + '_' + FORMAT("Description 2"), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 2, FORMAT(EmpRec."Search Name"), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 3, FORMAT(AmountLvar, 15, 1), TRUE, FALSE, FALSE, CellType::Number);
                        EnterCell(RowNo, 4, FORMAT("Posting Date", 10, '<Day,2>/<Month,2>/<Year4>'), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 5, FORMAT(EmpRec."No."), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 6, FORMAT(EmpRec."Bank Account No."), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 7, FORMAT(RoutingCode), TRUE, FALSE, FALSE, CellType::Text);
                        //IF COPYSTR(FORMAT(BkAccNo),1,1) = '0'  THEN
                        //  EnterCell(RowNo,8,QT+FORMAT(BkAccNo),TRUE,FALSE,FALSE)
                        //ELSE 
                        EnterCell(RowNo, 8, FORMAT(BkAccNo), TRUE, FALSE, FALSE, CellType::Text);
                    END;
                    //HO>>

                    IF (PrintExcel) AND (BankNames = BankNames::FBN) AND (("Source Type" = "Source Type"::Vendor))
                      THEN BEGIN

                        RowNo += 1;
                        EnterCell(RowNo, 1, FORMAT(COMPANYNAME), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 2, FORMAT("Source No."), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 3, FORMAT(AmountLvar, 15, 1), TRUE, FALSE, FALSE, CellType::Number);
                        EnterCell(RowNo, 4, '**********', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 5, '011', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 6, FORMAT(VendorAccNo), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 7, COPYSTR(VendorRtBkCode, 1, 3), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 8, FORMAT(VendorName), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 9, FORMAT("Posting Date", 10, '<Day,2>/<Month,2>/<Year4>'), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 10, "Document No." + "Description 2", TRUE, FALSE, FALSE, CellType::Text);
                    END;
                    IF (PrintExcel) AND (BankNames = BankNames::FBN) AND (("Source Type" = "Source Type"::"Fixed Asset"))
                      THEN BEGIN

                        RowNo += 1;
                        EnterCell(RowNo, 1, FORMAT(COMPANYNAME), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 2, FORMAT("Source No."), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 3, FORMAT(AmountLvar, 15, 1), TRUE, FALSE, FALSE, CellType::Number);
                        EnterCell(RowNo, 4, '**********', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 5, '011', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 6, FORMAT(VendorAccNo), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 7, COPYSTR(VendorRtBkCode, 1, 3), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 8, FORMAT(VendorName), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 9, FORMAT("Posting Date", 10, '<Day,2>/<Month,2>/<Year4>'), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 10, "Document No." + "Description 2", TRUE, FALSE, FALSE, CellType::Text);
                    END;

                    IF (PrintExcel) AND (BankNames = BankNames::FBN) AND (("Source Type" = "Source Type"::Customer))
                      THEN BEGIN

                        RowNo := 1;
                        EnterCell(RowNo, 1, FORMAT(COMPANYNAME), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 2, FORMAT(EmpRec."No."), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 3, FORMAT(AmountLvar, 15, 1), TRUE, FALSE, FALSE, CellType::Number);
                        EnterCell(RowNo, 4, '**********', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 5, '011', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 6, FORMAT(EmpRec."Bank Account No."), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 7, COPYSTR(RoutingCode, 1, 3), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 8, FORMAT(EmpRec."Search Name"), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 9, FORMAT("Posting Date", 10, '<Day,2>/<Month,2>/<Year4>'), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 10, "Source No." + '_' + "Document No." + "Description 2", TRUE, FALSE, FALSE, CellType::Text);
                    END;

                    IF (PrintExcel) AND (BankNames = BankNames::GTB) AND ("Source Type" = "Source Type"::Vendor) THEN BEGIN
                        RowNo += 1;
                        EnterCell(RowNo, 1, FORMAT(AmountLvar), TRUE, FALSE, FALSE, CellType::Number);
                        EnterCell(RowNo, 2, FORMAT(PostingDate), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 3, FORMAT("Document No.") + '/' + FORMAT(VendorName), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 4, FORMAT("Document No.") + '/' + FORMAT("Description 2"), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 5, FORMAT("Source No."), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 6, FORMAT(VendorName), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 7, FORMAT(VendorAccNo), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 8, FORMAT(VendorRtBkCode), TRUE, FALSE, FALSE, CellType::Text);
                    END;
                    IF (PrintExcel) AND (BankNames = BankNames::GTB) AND ("Source Type" = "Source Type"::"Fixed Asset") THEN BEGIN
                        RowNo += 1;
                        EnterCell(RowNo, 1, FORMAT(AmountLvar), TRUE, FALSE, FALSE, CellType::Number);
                        EnterCell(RowNo, 2, FORMAT(PostingDate), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 3, FORMAT("Document No.") + '/' + FORMAT(VendorName), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 4, FORMAT("Document No.") + '/' + FORMAT("Description 2"), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 5, FORMAT("Source No."), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 6, FORMAT(VendorName), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 7, FORMAT(VendorAccNo), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 8, FORMAT(VendorRtBkCode), TRUE, FALSE, FALSE, CellType::Text);
                    END;
                    //HO<<
                    IF (PrintExcel) AND (BankNames = BankNames::GTB) AND ("Source Type" = "Source Type"::Customer)
                      THEN BEGIN
                        RowNo += 1;
                        EnterCell(RowNo, 1, FORMAT(AmountLvar), TRUE, FALSE, FALSE, CellType::Number);
                        EnterCell(RowNo, 2, FORMAT(PostingDate), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 3, FORMAT("Document No.") + '/' + FORMAT(EmpRec."Search Name"), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 4, FORMAT("Document No.") + '/' + FORMAT("Description 2"), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 5, FORMAT(EmpRec."No."), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 6, FORMAT(EmpRec."Search Name"), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 7, FORMAT(EmpRec."Bank Account No."), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 8, FORMAT(RoutingCode), TRUE, FALSE, FALSE, CellType::Text);
                    END;
                    IF (PrintExcel) AND (BankNames = BankNames::STANBIC) AND ("Source Type" = "Source Type"::Vendor)
                      THEN BEGIN
                        RowNo += 1;
                        EnterCell(RowNo, 1, FORMAT('P'), TRUE, FALSE, FALSE, CellType::Text);
                        //EnterCell(RowNo,2,FORMAT(PostingDate),TRUE,FALSE,FALSE);
                        EnterCell(RowNo, 2, FORMAT(TODAY, 10, '<Day,2>/<Month,2>/<Year4>'), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 3, FORMAT(PaymentProduct), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 4, FORMAT('CHI_LIMITED'), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 5, FORMAT(BkAccNo), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 6, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 7, FORMAT(VendorName), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 8, FORMAT(ROUND(AmountLvar, 0.01, '=')), TRUE, FALSE, FALSE, CellType::Number);
                        EnterCell(RowNo, 9, FORMAT('NGN'), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 10, FORMAT(VendorAccNo), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 11, FORMAT(VendorRtBkCode), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 12, FORMAT("Document No.") + '_' + FORMAT("Description 2"), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 13, FORMAT("Document No.") + '_' + FORMAT("Description 2" + ' Trf from CHI Ltd'), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 14, FORMAT(VendorRec."E-Mail"), TRUE, FALSE, FALSE, CellType::Text);
                    END;

                    IF (PrintExcel) AND (BankNames = BankNames::STANBIC) AND ("Source Type" = "Source Type"::"Fixed Asset")
                      THEN BEGIN
                        RowNo += 1;
                        EnterCell(RowNo, 1, FORMAT('P'), TRUE, FALSE, FALSE, CellType::Text);
                        //EnterCell(RowNo,2,FORMAT(PostingDate),TRUE,FALSE,FALSE);
                        EnterCell(RowNo, 2, FORMAT(TODAY, 10, '<Day,2>/<Month,2>/<Year4>'), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 3, FORMAT(PaymentProduct), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 4, FORMAT('CHI_LIMITED'), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 5, FORMAT(BkAccNo), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 6, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 7, FORMAT(VendorName), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 8, FORMAT(ROUND(AmountLvar, 0.01, '=')), TRUE, FALSE, FALSE, CellType::Number);
                        EnterCell(RowNo, 9, FORMAT('NGN'), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 10, FORMAT(VendorAccNo), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 11, FORMAT(VendorRtBkCode), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 12, FORMAT("Document No.") + '_' + FORMAT("Description 2"), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 13, FORMAT("Document No.") + '_' + FORMAT("Description 2" + ' Trf from CHI Ltd'), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 14, FORMAT(VendorRec."E-Mail"), TRUE, FALSE, FALSE, CellType::Text);
                    END;
                    //HO<<
                    IF (PrintExcel) AND (BankNames = BankNames::STANBIC) AND ("Source Type" = "Source Type"::Customer)
                      THEN BEGIN
                        RowNo += 1;
                        EnterCell(RowNo, 1, FORMAT('P'), TRUE, FALSE, FALSE, CellType::Text);
                        //EnterCell(RowNo,2,FORMAT(PostingDate),TRUE,FALSE,FALSE);
                        EnterCell(RowNo, 2, FORMAT(TODAY, 10, '<Day,2>/<Month,2>/<Year4>'), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 3, FORMAT(PaymentProduct), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 4, FORMAT('CHI_LIMITED'), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 5, FORMAT(BkAccNo), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 6, FORMAT("Source No."), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 7, FORMAT(EmpRec."Search Name"), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 8, FORMAT(ROUND(AmountLvar, 0.01, '=')), TRUE, FALSE, FALSE, CellType::Number);
                        EnterCell(RowNo, 9, FORMAT('NGN'), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 10, FORMAT(EmpRec."Bank Account No."), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 11, FORMAT(RoutingCode), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 12, FORMAT("Document No.") + '_' + FORMAT("Description 2"), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 13, FORMAT("Document No.") + '_' + FORMAT("Description 2" + ' Trf from CHI Ltd'), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 14, FORMAT(UserSetup."E-Mail"), TRUE, FALSE, FALSE, CellType::Text);
                    END;

                    IF (PrintExcel) AND (BankNames = BankNames::UBA) AND ("Source Type" = "Source Type"::Vendor)
                    THEN BEGIN
                        RowNo += 1;
                        EnterCell(RowNo, 1, FORMAT('CHI'), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 2, FORMAT('VENDORPAY'), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 3, FORMAT(BkAccNo), TRUE, FALSE, FALSE, CellType::Text);
                        //EnterCell(RowNo,3,FORMAT(TODAY,10,'<Day,2>/<Month,2>/<Year4>'),TRUE,FALSE,FALSE);
                        EnterCell(RowNo, 4, FORMAT(PostingDate), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 5, FORMAT(PostingDate) + FORMAT("Document No."), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 6, FORMAT(PostingDate) + FORMAT("Document No.") + FORMAT(RowNo), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 7, FORMAT(ROUND(AmountLvar, 0.01, '=')), TRUE, FALSE, FALSE, CellType::Number);
                        EnterCell(RowNo, 8, FORMAT('NGN'), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 9, FORMAT("Source No."), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 10, FORMAT(VendorName), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 11, FORMAT(VendorRtBkCode), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 12, FORMAT(BankRoutingRec."Bank Name"), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 13, FORMAT(VendorAccNo), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 14, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 15, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 16, FORMAT("G/L Account Name"), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 17, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 18, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 19, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 20, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 21, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 22, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 23, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 24, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 25, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 26, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 27, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 28, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 29, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 30, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 31, FORMAT("Document No.") + '_' + FORMAT("Description 2"), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 32, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 33, FORMAT("Document No.") + '_' + FORMAT(Narration + ' Trf from CHI Ltd'), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 34, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 35, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 36, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 37, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 38, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 39, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 40, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 41, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                    END;

                    IF (PrintExcel) AND (BankNames = BankNames::UBA) AND ("Source Type" = "Source Type"::Customer)
                    THEN BEGIN
                        RowNo += 1;
                        EnterCell(RowNo, 1, FORMAT('CHI'), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 2, FORMAT('STAFFPAY'), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 3, FORMAT(BkAccNo), TRUE, FALSE, FALSE, CellType::Text);
                        //EnterCell(RowNo,3,FORMAT(TODAY,10,'<Day,2>/<Month,2>/<Year4>'),TRUE,FALSE,FALSE);
                        EnterCell(RowNo, 4, FORMAT(PostingDate), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 5, FORMAT(PostingDate) + FORMAT("Document No."), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 6, FORMAT(PostingDate) + FORMAT("Document No.") + FORMAT(RowNo), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 7, FORMAT(ROUND(AmountLvar, 0.01, '=')), TRUE, FALSE, FALSE, CellType::Number);
                        EnterCell(RowNo, 8, FORMAT('NGN'), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 9, FORMAT(EmpRec."No."), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 10, FORMAT(EmpRec."Search Name"), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 11, FORMAT(RoutingCode), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 12, FORMAT(EmpRec."Bank Name"), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 13, FORMAT(EmpRec."Bank Account No."), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 14, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 15, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 16, FORMAT("G/L Account Name"), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 17, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 18, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 19, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 20, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 21, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 22, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 23, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 24, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 25, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 26, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 27, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 28, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 29, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 30, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 31, FORMAT("Document No.") + '_' + FORMAT("Description 2"), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 32, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 33, FORMAT("Document No.") + '_' + FORMAT(Narration + ' Trf from CHI Ltd'), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 34, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 35, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 36, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 37, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 38, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 39, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 40, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 41, FORMAT(''), TRUE, FALSE, FALSE, CellType::Text);
                    END;
                    //CRF:2019-0068 NYO 14-08-19 >>

                    IF (PrintExcel) AND (BankNames = BankNames::CITI) AND ("Source Type" = "Source Type"::Vendor)
                    THEN BEGIN

                        NetPayments := '';
                        DateText := '';
                        DateText := FORMAT(TODAY, 10, '<YEAR4><MONTH,2><DAY,2>');
                        DateText := DELCHR(DateText, '=', ' ');
                        NetPayments := FORMAT(AmountLvar);
                        NetPayments := DELCHR(NetPayments, '=', ',');
                        IF VendorRtBkCode = 'C57' THEN BEGIN
                            BankRoutingRec.RESET;
                            BankRoutingRec.SETRANGE("Bank Code", BankRoutingRec."Bank Code"::ZB);
                            IF BankRoutingRec.FINDFIRST THEN
                                VendorRtBkCode := BankRoutingRec."Routing Code";
                        END;

                        /*IF (VendorAccNo <> '') AND (VendorRtBkCode <> '') THEN BEGIN
                            TXTLINES := KT + 'NG' + KT + 'DFT' + KT + DateText + KT + KT + KT + KT + KT + 'NGN' + KT + FORMAT(NetPayments) + KT + KT +
                          '**********' + KT + KT + KT + KT + KT + KT + KT + KT + KT + KT + KT + KT + KT + "Source No." + KT + KT + KT + KT + KT + KT + KT + KT + KT + KT + KT + KT + KT + KT + KT + KT + KT + KT + KT +
                                    VendorAccNo + KT + VendorName + KT + KT + KT + KT + KT + KT + VendorRtBkCode + KT + KT + KT + KT + KT + KT +
                                    KT + KT + KT + KT + KT + KT + KT + KT + KT + KT + KT + KT + KT + KT + KT + 'PAYMENT' + KT + KT + KT + KT + KT + KT + KT + KT + KT + KT + KT + KT + KT;
                        END;
                        IF FilePath = '' THEN BEGIN

                            FilePath := '\\B2BSRV-282\SharedPath\' + FORMAT("Document No.") + '.txt';
                            filetoexport.CREATE(FilePath);
                            filetoexport.WRITEMODE(TRUE);
                            filetoexport.OPEN(FilePath);
                        END;
                        filetoexport.WRITE(TXTLINES);

                    END;*/
                        //CRF:2019-0068 NYO 14-08-19 <<
                        DateDay := DATE2DMY("Posting Date", 1);
                        DateMonth := COPYSTR(DELCHR(FORMAT("Posting Date", 15, '<Month Text>'), '=', ' '), 1, 3);
                        DateYear := DATE2DMY("Posting Date", 3);
                        PostingDate := FORMAT(DateDay) + '/' + FORMAT(DateMonth) + '/' + FORMAT(DateYear);
                        DocNo := "Document No.";
                        /*IF FilePath = '' THEN
                          FilePath := 'C:\Zenith Bank Transfer\'+FORMAT(DocNo)+'.CSV';
                         */

                    end;
                END;
            end;

            trigger OnPreDataItem();
            begin
                LastFieldNo := FIELDNO("Source No.");
                if PrintGL then
                    SetCurrentKey("ShortCut Dimension code 7");
                IF PrintExcel THEN BEGIN
                    TempExcelBuffer.DELETEALL;
                    CLEAR(TempExcelBuffer);
                    IF BankNames = BankNames::ZB THEN BEGIN
                        RowNo := 1;
                        EnterCell(RowNo, 1, 'Transaction Reference', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 2, 'Beneficiary Name', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 3, 'Amount', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 4, 'Payment Due Date', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 5, 'Beneficiary Code', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 6, 'Beneficiary Account No.', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 7, 'Rounting Bank Code', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 8, 'Customer Debit Acct. No.', TRUE, FALSE, FALSE, CellType::Text);
                    END;
                    IF BankNames = BankNames::FBN THEN BEGIN
                        RowNo := 1;
                        EnterCell(RowNo, 1, 'OrgtranParticular', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 2, 'Orgtranrefno', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 3, 'Amount', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 4, 'DebitAccountNo', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 5, 'DebitBankCode', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 6, 'CreditAccountNo', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 7, 'CreditBankCode', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 8, 'BeneficiaryName', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 9, 'PaymentDate', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 10, 'Naration', TRUE, FALSE, FALSE, CellType::Text);
                    END;
                    //END;

                    IF BankNames = BankNames::GTB THEN BEGIN
                        RowNo := 1;
                        EnterCell(RowNo, 1, 'PaymentAmount', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 2, 'PaymentDate', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 3, 'Reference', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 4, 'Remark', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 5, 'VendorCode', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 6, 'VendorName', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 7, 'VendorAcctNumber', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 8, 'VendotBankSortCode', TRUE, FALSE, FALSE, CellType::Text);
                    END;

                    IF BankNames = BankNames::STANBIC THEN BEGIN
                        RowNo := 1;
                        EnterCell(RowNo, 1, 'Record_Identifier', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 2, 'Payment_Value_Date', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 3, 'Payment_Product_Code', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 4, 'Customer_Code', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 5, 'Debit_Account_No', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 6, 'Beneficiary_Code', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 7, 'Beneficiary_Name', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 8, 'Payment_Amount', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 9, 'Payment_Currency_Code', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 10, 'Credit_Account_No', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 11, 'Credit_Branch_Code', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 12, 'Debit_Narration', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 13, 'Credit_Narration', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 14, 'Notification_Email', TRUE, FALSE, FALSE, CellType::Text);
                    END;

                    IF BankNames = BankNames::UBA THEN BEGIN
                        RowNo := 1;
                        EnterCell(RowNo, 1, 'Corporate ID', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 2, 'My Product Code', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 3, 'Debit Account', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 4, 'Payment Date', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 5, 'Batch Reference', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 6, 'Instrument Reference Number', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 7, 'Txn Amount', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 8, 'Txn Currency', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 9, 'Beneficiary Code', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 10, 'Name of Beneficiary', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 11, 'Sort Code', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 12, 'Beneficiary Bank', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 13, 'Beneficiary Account Number', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 14, 'IBAN', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 15, 'Payment Product', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 16, 'Remarks', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 17, 'Instrument Reference', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 18, 'BIC', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 19, 'Beneficiary Branch', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 20, 'Benificiary Account Currncy', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 21, 'Beneficiary Address', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 22, 'Beneficiary City', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 23, 'Beneficiary State', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 24, 'Beneficiary Country', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 25, 'Beneficiary Post Code', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 26, 'Beneficiary Mobile Number', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 27, 'Beneficiary E-mail ID', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 28, 'Beneficiary Telephone Number', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 29, 'Beneficiary Fax', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 30, 'Debit Reference', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 31, 'Debit Details', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 32, 'Credit Reference', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 33, 'Credit Details', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 34, 'Charge To', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 35, 'Receiver Charge Currency', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 36, 'Receiver Charge Amount', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 37, 'My Product Enrichment 1', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 38, 'My Product Enrichment 2', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 39, 'My Product Enrichment 3', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 40, 'My Product Enrichment 4', TRUE, FALSE, FALSE, CellType::Text);
                        EnterCell(RowNo, 41, 'My Product Enrichment 5', TRUE, FALSE, FALSE, CellType::Text);
                    END;
                END;
            end;
        }
    }

    requestpage
    {

        layout
        {
            area(Content)
            {

                group(GroupName)
                {
                    field(PaymentProduct; PaymentProduct)
                    {

                    }
                    field(BankNames; BankNames)
                    {

                    }
                    field(PrintGL; PrintGL)
                    {

                    }
                }
            }
        }

        actions
        {

        }
    }

    labels
    {
    }

    trigger OnInitReport();
    begin
        PrintExcel := TRUE;
    end;

    trigger OnPostReport();
    begin
        IF PrintExcel THEN BEGIN
            //TempExcelBuffer.CreateNewBook('Bank Confirmation File');
            //TempExcelBuffer.WriteSheet('Bank Confirmation File', CompanyName(), UserId());
            //TempExcelBuffer.SaveWorkBook(FilePath);
            //TempExcelBuffer.GiveUserControl;
            //TempExcelBuffer.CloseBook();
            //TempExcelBuffer.OpenExcel();
            TempExcelBuffer.CreateBookAndOpenExcel('', Format(BankNames), 'Report', CompanyName, UserId);
        END;
    end;

    trigger OnPreReport();
    begin


        IF BankNames = BankNames::" " THEN
            ERROR('You Must Select a Bank Name');

        /*IF ISCLEAR(FileAttach) THEN
         CREATE(FileAttach);
        
        IF NOT FileAttach.FolderExists('C:\Zenith Bank Transfer\') THEN
         FileAttach.CreateFolder('C:\Zenith Bank Transfer\');
         */
        //CRF:2019-0068 NYO 14-08-19 >>
        //B2BSRA1.0 Commented
        /*
        IF BankNames = BankNames::CITI THEN BEGIN
          IF ISCLEAR(FileAttach) THEN
           CREATE(FileAttach);
        
          IF NOT FileAttach.FolderExists('C:\Vendor Payment\') THEN
           FileAttach.CreateFolder('C:\Vendor Payment\');
        END;
        */
        //irisClient_CHING\read\AUTOUPLOAD'
        //nyong{accounts.kenny}

        filetoexport.TEXTMODE(TRUE);
        //CRF:2019-0068 NYO 14-08-19 <<

    end;

    var
        LastFieldNo: Integer;
        WHTLedgerEntry: Record "WHT Buffer";
        FooterPrinted: Boolean;
        TotalFor: Label '"Total for "';
        PrintExcel: Boolean;
        RowNo: Integer;
        ColumnNo: Integer;
        TempExcelBuffer: Record "Excel Buffer C" temporary;
        VendorRec: Record Vendor;
        VendorName: Text[150];
        VendorAccNo: Code[20];
        VendorRtBkCode: Code[20];
        BkAccNo: Code[20];
        glentry: Record "G/L Entry";
        BankRec: Record "Bank Account";
        DocNo: Code[20];
        FilePath: Text[150];
        QT: Label '''';
        EmpRec: Record Employee;
        BankRoutingRec: Record "Bank Routing Code";
        RoutingCode: Code[20];
        PrintGL: Boolean;
        text001: Label 'The paying bank for the document selected was %1';
        //BankNames: Option " ",ZB,GTB,CITI,STANDARDCHART,WEMA,DIAMOND,SKY,STERLING,UBA,FBN,ACCESS,ECO,ETB,STANBIC,MAINSTREET,FIDELITY,KEYSTONE,ENTERPRISE,UNION,UNITY,CBN,FCMB,HERITAGE;//PKONDE16;
        BankNames: Option " ",ZB,GTB,CITI,STANDARDCHART,WEMA,DIAMOND,SKY,STERLING,UBA,FBN,ACCESS,ECO,ETB,STANBIC,MAINSTREET,FIDELITY,KEYSTONE,ENTERPRISE,UNION,UNITY,CBN,FCMB,HERITAGE,CORONATION,Globus,Nova,Optimus,Parallex,"PremiumTrust",Providus,Signature,SunTrust,"TitanTrust",Alternative,Jaiz,Lotus,TAJBank;//PKONDE16; //G2S 110324 CAS-01407-S2M2T7
        DateDay: Integer;
        DateMonth: Text[30];
        DateYear: Integer;
        PostingDate: Text[30];
        PaymentProduct: Option " ",NEFT,NEFT_BULK,NIP,NIP_BULK;
        UserSetup: Record "User Setup";
        filetoexport: File;
        TXTLINES: Text[1024];
        q: Text[30];
        DateText: Text[30];
        NetPayments: Text[30];
        KT: Label '#';
        PrevSourceNo: Code[20];
        PrevSourceType: Enum "Gen. Journal Source Type";
        DimMgt: Codeunit DimensionManagement;
        ShortCutDimCode: array[8] of Code[20];
        EmployeeDim: Code[20];
        PrevEmployee: Code[20];
        CellType: Option Number,Text,Date,Time;
        GLSetup: Record "General Ledger Setup";
        CreateFile: File;
        FileOutstream: OutStream;


    procedure EnterCell(RowNo: Integer; ColumnNo: Integer; Cellvalue: Text[250]; Bold: Boolean; Italic: Boolean; Underline: Boolean; CellType: Option Number,Text,Date,Time);
    begin
        TempExcelBuffer.INIT;
        TempExcelBuffer.VALIDATE("Row No.", RowNo);
        TempExcelBuffer.VALIDATE("Column No.", ColumnNo);
        TempExcelBuffer."Cell Value as Text" := Cellvalue;
        TempExcelBuffer.Formula := '';
        TempExcelBuffer.Bold := Bold;
        TempExcelBuffer.Italic := Italic;
        TempExcelBuffer.Underline := Underline;
        TempExcelBuffer."Cell Type" := CellType;
        TempExcelBuffer.INSERT;
    end;
}

