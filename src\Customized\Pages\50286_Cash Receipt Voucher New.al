page 50286 "Cash Receipt Voucher New"
{
    // version CHI6.0

    // PROJECT : CHI 6.0
    // **********************************************************************************
    // SIGN
    // **********************************************************************************
    // CHI     :  CHI 6.0 Developments
    // UNL     :  Univision Nigeria Limited
    // SAA     :  SAHEED ADIO ADEOSUN
    // HO      :  Henry <PERSON>
    // **********************************************************************************
    // VER      SIGN         DATE    DESCRIPTION
    // **********************************************************************************
    // 1.0      UNL      06-Dec-11   -> Form Created to display Cash Rcpt Document Details.
    // 
    // 3.0      SAA      08-Mar-12   -> Added codes to "Form-OnOpenForm" & "Form-OnNewRecord" to
    //                                  filter out Responsibility Centres in Vouchers.
    //                               -> Added the "Responsibility Centre" field to the form
    // 
    // 1.0      HO       7-Sep-12    -> Code added to "Form-OnDelereRecord()" to allow Archive of Cash Receipt Voucher No. when deleted.

    DeleteAllowed = false;
    PageType = Document;
    SourceTable = "Voucher Header";
    SourceTableView = SORTING("Voucher Type", "Document No.")
                      WHERE("Voucher Type" = CONST(CRV),
                            Status = FILTER(<> Released));
    layout
    {
        area(content)
        {
            group(General)
            {
                Caption = 'General';
                field("Document No."; "Document No.")
                {
                    ApplicationArea = all;
                    Editable = false;

                    trigger OnAssistEdit();
                    begin
                        if AssistEdit(xRec) then
                            CurrPage.UPDATE;
                    end;
                }
                field("Posting Date"; "Posting Date")
                {
                    ApplicationArea = all;
                    trigger OnValidate();
                    begin
                        // SAA 3.0 >>
                        PostingDateValidate;
                        // SAA 3.0 <<
                    end;
                }
                field("Account Type"; "Account Type")
                {
                    ApplicationArea = all;
                }
                field("Account No."; "Account No.")
                {
                    ApplicationArea = all;
                    Caption = 'Debit Account No.';
                }
                field("Account Name"; "Account Name")
                {
                    ApplicationArea = all;
                }
                field("WHT Applicable"; "WHT Applicable")
                {
                    ApplicationArea = all;
                    trigger OnValidate()
                    var
                        genjoulrec: Record "Gen. Journal Line";
                    begin
                        IF "WHT Applicable" then begin
                            genjoulrec.Reset();
                            genjoulrec.Setrange("Journal Template Name", 'CRV');
                            genjoulrec.Setrange("Journal Batch Name", 'CRV');
                            genjoulrec.Setrange("Voucher Type", genjoulrec."Voucher Type"::CRV);
                            genjoulrec.Setrange("Document No.", "Document No.");
                            genjoulrec.Setrange("Document Type", genjoulrec."Document Type"::Payment);
                            IF genjoulrec.FindFirst() then
                                Error('You cannot insert more than one line if WHT is Applicable')
                        end Else Begin
                            genjoulrec.Reset();
                            genjoulrec.Setrange("Journal Template Name", 'CRV');
                            genjoulrec.Setrange("Journal Batch Name", 'CRV');
                            genjoulrec.Setrange("Voucher Type", genjoulrec."Voucher Type"::CRV);
                            genjoulrec.Setrange("Document No.", "Document No.");
                            IF genjoulrec.FindFirst() then
                                repeat
                                    Clear(genjoulrec."WHT %");
                                    Clear(genjoulrec."WHT Account");
                                    Clear(genjoulrec."WHT Amount");
                                    Clear(genjoulrec."WHT Amount(LCY)");
                                    Clear(genjoulrec."WHT Group");
                                    genjoulrec.Modify();
                                until genjoulrec.Next = 0;
                        End;
                    end;

                }
                field(Status; Status)
                {
                    ApplicationArea = all;
                }
                field(Narration; Narration)
                {
                    ApplicationArea = all;
                }
                field("Currency Code"; "Currency Code")
                {
                    ApplicationArea = all;
                    Editable = false;

                    trigger OnAssistEdit();
                    begin
                        ChangeExchangeRate.SetParameter("Currency Code", "Currency Factor", "Posting Date");
                        if ChangeExchangeRate.RUNMODAL = ACTION::OK then begin
                            VALIDATE("Currency Factor", ChangeExchangeRate.GetParameter);
                        end;
                        CLEAR(ChangeExchangeRate);
                    end;
                }
                field("Amount (LCY)"; "Amount (LCY)")
                {
                    ApplicationArea = all;
                }

                field("Shortcut Dimension 1 Code"; "Shortcut Dimension 1 Code")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Shortcut Dimension 2 Code"; "Shortcut Dimension 2 Code")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Cash Account Type"; "Cash Account Type")
                {
                    ApplicationArea = all;
                }
            }
            part(VoucherLines; "Cash Receipt Voucher Subform")
            {
                ApplicationArea = all;
                SubPageLink = "Journal Template Name" = FIELD("Journal Template Code"),
                              "Journal Batch Name" = FIELD("Journal Batch Name"),
                              "Document No." = FIELD("Document No.");
            }
            group(Usertrail)
            {
                Caption = 'Usertrail';
                field("Created By"; "Created By")
                {
                    ApplicationArea = all;
                }
                field("Created By Name"; "Created By Name")
                {
                    ApplicationArea = all;
                }
                field("Created Date"; "Created Date")
                {
                    ApplicationArea = all;
                }

                field("Created Time"; "Created Time")
                {
                    ApplicationArea = all;
                }
                field("Modified By"; "Modified By")
                {
                    ApplicationArea = all;
                }
                field("Modified By Name"; "Modified By Name")
                {
                    ApplicationArea = all;
                }
                field("Modified Date"; "Modified Date")
                {
                    ApplicationArea = all;
                }
                field("Modified Time"; "Modified Time")
                {
                    ApplicationArea = all;
                }
            }
            group("Receipt Details")
            {
                Caption = 'Receipt Details';
                field("Responsibility Center"; "Responsibility Center")
                {
                    ApplicationArea = all;
                }
                field("Receiving Type"; "Receiving Type")
                {
                    ApplicationArea = all;
                    trigger OnValidate();
                    begin
                        if "Responsibility Center" = '' then
                            ERROR('Please Select Responcibility center');
                    end;
                }
                field("Receiving Code"; "Receiving Code")
                {
                    ApplicationArea = all;
                }
                field("Received From"; "Received From")
                {
                    ApplicationArea = all;
                }
            }
        }
        area(FactBoxes)
        {
            part("Attached Documents"; "Document Attachment Factbox")
            {
                ApplicationArea = All;
                Caption = 'Attachments';
                SubPageLink = "Table ID" = CONST(50117),
                                "No." = FIELD("Document No.");
                // Type = FIELD("Voucher Type");
            }
            systempart(PyamentTermsLinks; Links)
            {
                ApplicationArea = RecordLinks;
            }
            systempart(PyamentTermsNotes; Notes)
            {
                ApplicationArea = Notes;
            }
        }
        //g2s29Dev23
    }

    actions
    {
        area(navigation)
        {
            group("&Voucher")
            {
                Caption = '&Voucher';
                separator(Separator1000000067)
                {
                }
                action(Dimensions)
                {
                    ApplicationArea = all;
                    Caption = 'Dimensions';

                    trigger OnAction();
                    begin
                        ShowDocDim;
                    end;
                }
                separator(Separator1000000069)
                {
                }
                action(Approvals)
                {
                    ApplicationArea = all;
                    Caption = 'Approvals';

                    trigger OnAction();
                    begin
                        approvalmngmt.ApproveRecordApprovalRequest(RecordId());
                    end;
                }
                /*action(InsertLine)
                {
                    ApplicationArea = all;
                    Caption = 'Insert Line';
                    Promoted = true;
                    PromotedCategory = Process;
                    ShortcutKey = 'Ctrl+N';
                    trigger OnAction();
                    var
                        genjoulrec: Record "Gen. Journal Line";
                        linen: Integer;
                    begin
                        genjoulrec.RESET();
                        genjoulrec.SetRange("Journal Template Name", 'CRV');
                        genjoulrec.SetRange("Journal Batch Name", 'CRV');
                        //genjoulrec.SetRange("Document No.", "Document No.");
                        IF genjoulrec.FINDLAST THEN
                            linen := 10000 + genjoulrec."Line No."
                        else
                            linen := 10000;
                        IF "WHT Applicable" then begin
                            genjoulrec.Reset();
                            genjoulrec.Setrange("Journal Template Name", 'CRV');
                            genjoulrec.Setrange("Journal Batch Name", 'CRV');
                            genjoulrec.Setrange("Voucher Type", genjoulrec."Voucher Type"::CRV);
                            genjoulrec.Setrange("Document No.", "Document No.");
                            IF genjoulrec.FindFirst() then
                                Error('You cannot insert more than one line if WHT is Applicable')
                        end;
                        clear(genjoulrec);
                        genjoulrec.init();
                        genjoulrec."Journal Template Name" := 'CRV';
                        genjoulrec."Journal Batch Name" := 'CRV';
                        genjoulrec."Voucher Type" := genjoulrec."Voucher Type"::CRV;
                        genjoulrec."Document No." := "Document No.";
                        genjoulrec."Line No." := linen;
                        genjoulrec."Posting Date" := Today; //PJ                        
                        genjoulrec.insert(True);
                        Message('Line Inserted %1..%2', genjoulrec."Journal Template Name", genjoulrec."Journal Batch Name");
                        //insert(true);
                        CurrPage.Update();
                    end;
                }*/
            }
            group("F&unctions")
            {
                Caption = 'F&unctions';
                action("Send Approval Request")
                {
                    ApplicationArea = All;
                    Image = SendApprovalRequest;
                    Visible = Not OpenApprEntrEsists and CanrequestApprovForFlow;
                    Promoted = true;
                    PromotedIsBig = true;
                    PromotedCategory = Process;
                    PromotedOnly = true;
                    trigger OnAction()
                    var
                        GenLineLRec: Record "Gen. Journal Line";
                    begin
                        GenLineLRec.reset;
                        GenLineLRec.SetRange("Journal Template Name", "Journal Template Code");
                        GenLineLRec.SetRange("Journal Batch Name", "Journal Batch Name");
                        GenLineLRec.SetRange("Document No.", "Document No.");
                        GenLineLRec.SetRange(Quantity, 0);
                        IF GenLineLRec.findset then
                            repeat
                                error('quantity is zero in Gen Journal for Line No. %1', GenLineLRec."Line No.");
                            until GenLineLRec.next = 0;
                        CheckDimensions();
                        ClearValues();
                        TestField("Posting Date");
                        IF allinoneCU.CheckJournalVoucherApprovalsWorkflowEnabled(Rec) then
                            allinoneCU.OnSendJournalVoucherForApproval(Rec);
                    end;
                }
                action("Cancel Approval Request")
                {
                    ApplicationArea = All;
                    Image = CancelApprovalRequest;
                    Visible = CanCancelapprovalforrecord or CanCancelapprovalforflow;
                    Promoted = true;
                    PromotedIsBig = true;
                    PromotedCategory = Process;
                    PromotedOnly = true;
                    trigger OnAction()
                    begin
                        allinoneCU.OnCancelJournalVoucherForApproval(Rec);
                    end;
                }
                separator(Separator1000000049)
                {
                }
                action("Re&lease")
                {
                    ApplicationArea = all;
                    Caption = 'Re&lease';
                    ShortCutKey = 'Ctrl+F11';
                    Image = ReleaseDoc;
                    trigger OnAction()
                    begin
                        CheckDimensions();
                        IF WorkflowManagement.CanExecuteWorkflow(Rec, allinoneCU.RunworkflowOnSendJournalVoucherforApprovalCode()) then
                            error('Workflow is enabled. You can not release manually.');
                        TestField("Posting Date");
                        IF Status <> Status::Released then BEGIN
                            Status := Status::Released;
                            Modify();
                            Message('Document has been Released.');
                        end;
                    end;
                }
                action("Re&open")
                {
                    ApplicationArea = all;
                    Caption = 'Re&open';
                    Image = ReOpen;
                    trigger OnAction();
                    begin
                        RecordRest.Reset();
                        RecordRest.SetRange(ID, 50117);
                        RecordRest.SetRange("Record ID", Rec.RecordId());
                        IF RecordRest.FindFirst() THEN
                            error('This record is under in workflow process. Please cancel approval request if not required.');
                        IF Status <> Status::Open then BEGIN
                            Status := Status::Open;
                            Modify();
                            Message('Document has been Reopened.');
                        end;
                    end;
                }
                separator("----")
                {
                    Caption = '----';
                }
                action(Comments)
                {
                    ApplicationArea = all;
                    Caption = 'Comments';
                    RunObject = Page "Approval Comments";
                    RunPageLink = "Document Type" = FILTER('CRV'),
                                  "Document No." = FIELD("Document No.");
                }
            }
        }
        area(processing)
        {
            action("&Print")
            {
                ApplicationArea = all;
                Caption = '&Print';
                Ellipsis = true;
                Promoted = true;
                PromotedCategory = Process;

                trigger OnAction();
                begin
                    VoucherHeader.SETRANGE("Voucher Type", "Voucher Type");
                    VoucherHeader.SETRANGE("Document No.", "Document No.");
                    if VoucherHeader.FINDFIRST then
                        //REPORT.RUN(50193, true, false, VoucherHeader);
                        Report.Run(50081, true, false, VoucherHeader);
                end;
            }
            action(Preview)
            {
                ApplicationArea = all;
                Caption = 'Preview';
                ShortCutKey = 'Shift+F2';

                trigger OnAction();
                var
                    GeneralJrnlBtchLRec: Record "Gen. Journal Batch";
                    DocumentNo: Code[20];
                    GLRegGRec: Record "G/L Register";
                    genJounlin: Record "Gen. Journal Line 2";
                    VoucherPreview: Codeunit "Voucher Preview Posting";
                begin
                    DocumentNo := "Document No.";
                    ClearValues();
                    VoucherPreview.RUN(Rec);
                end;
            }
            action("Cash Receipt Voucher Test Report")
            {
                trigger OnAction()
                var
                    VouHeader: Record "Voucher Header";
                BEGIN
                    VouHeader.RESET;
                    VouHeader.SETRANGE("Voucher Type", "Voucher Type");
                    VouHeader.SETRANGE("Document No.", "Document No.");
                    if VouHeader.FINDFIRST then
                        REPORT.RUN(50563, true, false, VouHeader);
                END;
            }
            //Balu 05282021>>
            action("Open Excel")
            {
                ApplicationArea = all;
                Caption = 'Open Excel';
                Image = Open;
                trigger OnAction()
                var
                    GlLine2: Record "Gen. Journal Line 2";
                begin
                    GlLine2.CreateExcel(Rec);
                end;
            }
            //Balu 05282021<<
        }
    }

    trigger OnDeleteRecord(): Boolean;
    begin
        DelDocNoArchive.ArchiveNo("Document No.", 19, TODAY, TIME, USERID, DATABASE::"Voucher Header"); //HO1.0
        // B2BMS08022021>>
        TestField(Status, Status::Open);
        // B2BMS08022021<<
    end;

    trigger OnNewRecord(BelowxRec: Boolean);
    begin
        "Dim. Document Type" := "Dim. Document Type"::CRV;
        // SAA 3.0 >>
        "Responsibility Center" := UserMg.GetVoucherFilter();
        // SAA 3.0 <<
    end;

    trigger OnOpenPage();
    begin
        // SAA 3.0 >>
        /*IF UserMgt.GetVoucherFilter() <> '' THEN BEGIN
          FILTERGROUP(2);
          SETRANGE("Responsibility Center",UserMgt.GetVoucherFilter());
          FILTERGROUP(0);
        END;*/
        // SAA 3.0 >>
        USERSETUP.GET(USERID);
        /*IF USERSETUP.FilterResponsibilityCenter <> '' THEN BEGIN
          FILTERGROUP(2);
          SETFILTER("Responsibility Center",USERSETUP.FilterResponsibilityCenter);
          FILTERGROUP(0); END; */
        // SAA 3.0 <<

    end;

    trigger OnModifyRecord(): Boolean
    BEGIN
        TestField(Status, Status::Open);
    END;

    trigger OnAfterGetRecord()
    BEGIN
        OpenAppEntrExistsForCurrUser := approvalmngmt.HasOpenApprovalEntriesForCurrentUser(RecordId());
        OpenApprEntrEsists := approvalmngmt.HasOpenApprovalEntries(RecordId());
        CanCancelapprovalforrecord := approvalmngmt.CanCancelApprovalForRecord(RecordId());
        workflowwebhookmangt.GetCanRequestAndCanCancel(RecordId(), CanrequestApprovForFlow, CanCancelapprovalforflow);
    END;

    var
        VoucherHeader: Record "Voucher Header";
        ApprovalMgt: Codeunit 1535;
        ApprovalEntry: Record "Approval Entry";
        ReleaseVoucher: Codeunit "Voucher Release";
        ApprovalEntries: Page 658;
        ChangeExchangeRate: Page "Change Exchange Rate";
        GenJrnlLine: Record "Gen. Journal Line";
        ReportPrint: Codeunit "Test Report-Print";
        VoucherPost: Codeunit "Voucher Post";
        UserMgt: Codeunit "User Setup Management";
        UserMg: Codeunit "User Setup Management Ext";
        DelDocNoArchive: Codeunit "Deleted Doc. No. Archive";
        Text50000: Label 'Do wish to the Cancel the Approval Request of this Document?';
        USERSETUP: Record "User Setup";
        BuildFilter: Text[250];
        RespCentFilter: Codeunit "Responsibility Center Filter";
        VAmt: Decimal;
        WorkflowManagement: Codeunit "Workflow Management";
        allinoneCU: codeunit IJLSubEvents;
        RecordRest: record "Restricted Record";
        approvalmngmt: Codeunit "Approvals Mgmt.";
        OpenApprEntrEsists: Boolean;
        CanrequestApprovForFlow: Boolean;
        CanCancelapprovalforrecord: Boolean;
        CanCancelapprovalforflow: Boolean;
        OpenAppEntrExistsForCurrUser: Boolean;
        workflowwebhookmangt: Codeunit "Workflow Webhook Management";

    procedure CheckHeaderLines(VoucherHeaderRec: Record "Voucher Header");
    var
        GenJnlLine: Record "Gen. Journal Line 2";//PK-GJ2
        VendorLedgerEntry: Record "Vendor Ledger Entry";
    begin
        with VoucherHeaderRec do begin
            TESTFIELD("Account No.");
            TESTFIELD("Shortcut Dimension 2 Code");
            TESTFIELD("Shortcut Dimension 1 Code");
            TESTFIELD(Narration);
            //TESTFIELD("Received From");
            TESTFIELD("Receiving Type");
            if "Receiving Type" <> "Receiving Type"::Others then begin
                TESTFIELD("Receiving Code");
                TESTFIELD("Received From");
            end;

            GenJnlLine.SETRANGE(GenJnlLine."Journal Template Name", "Journal Template Code");
            GenJnlLine.SETRANGE(GenJnlLine."Journal Batch Name", "Journal Batch Name");
            GenJnlLine.SETRANGE(GenJnlLine."Document No.", "Document No.");
            if GenJnlLine.FIND('-') then begin
                repeat
                    GenJnlLine.TESTFIELD("Account No.");
                    GenJnlLine.TESTFIELD("Posting Date");
                    GenJnlLine.TESTFIELD(Amount);
                    GenJnlLine.TESTFIELD("Shortcut Dimension 1 Code");
                    GenJnlLine.TESTFIELD("Shortcut Dimension 2 Code");
                    //GenJnlLine.TESTFIELD("Description 2");
                    GenJnlLine.TESTFIELD(Cleared, true);

                    //Nyo
                    if GenJrnlLine."Account Type" = GenJrnlLine."Account Type"::"Fixed Asset" then
                        GenJnlLine.TESTFIELD("FA Posting Type");
                    if (GenJnlLine."Account Type" = GenJnlLine."Account Type"::Customer)
                      or (GenJnlLine."Bal. Account Type" = GenJnlLine."Bal. Account Type"::Customer) then
                        GenJnlLine.TESTFIELD("Responsibility Center");
                    /*if GenJnlLine."FA Posting Type" = GenJnlLine."FA Posting Type" ::"capital work in progress" then begin
                      //TESTFIELD("Shortcut Dimension 4 Code");
                      GenJnlLine.TESTFIELD("Capex No.");
                      GenJnlLine.TESTFIELD("Capex Line No.");
                    end;*///CHI 9.0
                    if GenJnlLine."FA Posting Type" = GenJnlLine."FA Posting Type"::"Acquisition Cost" then begin
                        GenJnlLine.TESTFIELD("Capex No.");
                        GenJnlLine.TESTFIELD("Capex Line No.");
                    end;
                //Nyo

                //GenJnlLine."Posting Date" := TODAY;

                //GenJnlLine.MODIFY;
                until GenJnlLine.NEXT = 0;
            end;
        end;
    end;

    procedure PostingDateValidate();
    var
        Text50201: Label 'Posting Date must not be a future date';
    begin
        if "Posting Date" > TODAY then
            ERROR(Text50201);
    end;

    local procedure CheckDimensions()
    var
        GenJnlLine: Record "Gen. Journal Line 2"; //PKON22AP19
    begin
        TestField("Shortcut Dimension 1 Code");
        TestField("Shortcut Dimension 2 Code");
        GenJnlLine.Reset();
        GenJnlLine.SetRange("Journal Batch Name", "Journal Template Code");
        GenJnlLine.SetRange("Journal Batch Name", "Journal Batch Name");
        GenJnlLine.SetRange("Document No.", "Document No.");
        GenJnlLine.SetRange("Voucher type", GenJnlLine."Voucher type"::CRV);
        if GenJnlLine.FindSet() then
            repeat
                GenJnlLine.TestField("Shortcut Dimension 1 Code");
                GenJnlLine.TestField("Shortcut Dimension 2 Code");
                GenJnlLine.TestField(Amount);
            until GenJnlLine.Next() = 0;
    end;
}

