page 50358 "Rebate Records Page"
{
    Caption = 'Variable Discounts Page';
    PageType = List;
    Editable = false;
    //DeleteAllowed = true;
    SourceTable = "Rebate Records";
    UsageCategory = Tasks;
    ApplicationArea = All;

    layout
    {
        area(content)
        {
            repeater(General)
            {
                field("No."; Rec."No.")
                {
                    ApplicationArea = All;
                    Visible = false;
                    ToolTip = 'Specifies the value of the No. field.';
                }
                field("Customer No."; Rec."Customer No.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Customer No. field.';
                }
                field("Customer Name"; Rec."Customer Name")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Customer Name field.';
                }
                field("Rebate Period"; Rec."Rebate Period")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Rebate Period field.';
                }
                field("Rebate Amount"; Rec."Rebate Amount")
                {
                    ApplicationArea = All;
                    Visible = true;
                    ToolTip = 'Specifies the value of the Variable Discount Amount field.';
                }
                field("Rebate Total Amount"; Rec."Rebate Total Amount")
                {
                    ApplicationArea = All;
                    Visible = true;
                    ToolTip = 'Specifies the value of the Variable Discount Amount field.';
                }
                field("Invoice Records"; Rec."Invoice Record.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Invoice Amount field.';
                    Enabled = false;
                    AssistEdit = true;
                    Visible = false;
                    trigger OnAssistEdit()
                    var
                        myInt: Integer;
                        GLEntryPg: Page "General Ledger Entries";
                        GLEntry: Record "G/L Entry";
                    begin
                        GLEntryPg.LookupMode := true;
                    end;
                }
                field("Posting Date"; Rec."Posting Date")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Posting Date field.';
                }
                field("Posted by"; Rec."Posted by")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Posted by field.';
                }
                field("Balance Rebate Amount"; Rec."Balance Rebate Amount")
                {
                    ApplicationArea = All;
                    // Caption = 'Remaining Rebate Amount';
                    ToolTip = 'Specifies the value of the Total variable Discount Amount field.';
                }
                field("No. of Transaction in a Month"; "No. of Transaction in a Month")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Total variable Discount Amount field.';
                }
                field("Rebate Discount Status"; "Rebate Discount Status")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Total variable Discount Amount field.';
                }
                field("Rebate Start Date"; "Rebate Start Date")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Total variable Discount Amount field.';
                }
                field("Rebate End Date"; "Rebate End Date")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the value of the Total variable Discount Amount field.';
                }
            }
        }
    }

    actions
    {
        area(Processing)
        {
            action("Rebate Records")
            {
                ApplicationArea = All;
                Caption = 'Rebate Records';
                Image = List;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                PromotedOnly = true;
                trigger OnAction()
                var
                    CU: Codeunit "PModifier-CU";
                begin
                    CU.UpdateRebateTable();
                end;
            }
        }
    }

    trigger OnOpenPage()
    begin
        SetCurrentKey("Rebate Period", "Customer No.");
        SetAscending("Rebate Period", false);

    end;
}
