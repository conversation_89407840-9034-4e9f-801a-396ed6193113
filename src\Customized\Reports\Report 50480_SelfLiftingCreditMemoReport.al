report 50480 "Self Lifting Credit Memo"
{
    DefaultLayout = RDLC;
    RDLCLayout = './PostedLoadingSlip.rdl';
    PreviewMode = PrintLayout;
    UsageCategory = ReportsAndAnalysis;
    ApplicationArea = all;
    Caption = 'Self Lifting Credit Memo Report_50480';
    Permissions = tabledata "Posted Loading SLip Header" = rm, tabledata "sales header" = irm, tabledata "sales line" = irm;

    dataset
    {
        dataitem("Posted Loading SLip Header"; "Posted Loading SLip Header")
        {
            RequestFilterFields = "No.", "Created Date", "Responsibility Center", "Party No.", "Vehicle By";//PKONJU()
            DataItemTableView = sorting("Party No.") where("Vehicle By" = filter("Customer(Self Lifting)"), cancel = filter(false));//PKONJU9

            column(ShwDetails; ShwDetails)
            {

            }
            column(CompanyInfoPicture; CompanyInfo.Picture)
            {

            }
            column(CompanyInfoName; CompanyInfo.Name)
            {

            }
            column(CurrReport_PAGENO; CurrReport.PAGENO)
            {
            }
            column(USERID; USERID)
            {
            }
            column(No_; "No.")
            {

            }
            column(Vehicle_No_; "Own Vehicle No.")
            {

            }
            column(Vehicle_Capacity_in_Tons; "Vehicle Capacity in Tons")
            {

            }
            column(Party_No_; "Party No.")
            {

            }
            column(CustName; CustName)
            {

            }
            column(Loaded_Qty; "Loaded Qty")
            {

            }
            column(Posted_date; "Created Date")
            {

            }
            column(Self_Lifting_Crdt_Memo; "Self Lifting Crdt Memo")
            {

            }
            column(Created_Date; "Created Date")
            {

            }
            column(Own_Vehicle_No_; "Own Vehicle No.")
            {

            }
            dataitem("Posted Loading Slip Line"; "Posted Loading Slip Line")
            {
                DataItemLink = "Document No." = FIELD("No.");
                //DataItemTableView = SORTING("Customer No.") where(cancelled = filter(False));//PKONJU9
                DataItemTableView = SORTING("Customer No.") where(cancelled = filter(False), "Item No." = filter(<> ''));//PKONOC2
                column(Item_No_; "Item No.")
                {

                }
                column(Line_No_; "Line No.")
                {

                }
                column(Description; Description)
                {

                }
                column(Quantity; Quantity)
                {

                }
                column(Qty__Loading; "Qty. Loading")
                {

                }
                column(From_Location; "From Location")
                {

                }
                column(To_Location; "To Location")
                {

                }
                column(TotalValue; TotalValue)
                {

                }
                column(Customer_No_; "Customer No.")
                {

                }
                column(Customer_Name; "Customer Name")
                {

                }
                column(SelfLiftNRate; rate) { }
                trigger OnAfterGetRecord()
                var
                    Resp: Record "Responsibility Center";
                    Item: Record Item;
                begin
                    Clear(TotalValue);
                    clear(rate);
                    Resp.get("Posted Loading SLip Header"."Responsibility Center");
                    if Resp."Item Self Lift rate" then begin
                        Item.get("Item No.");
                        rate := Item."Customer's Transportation Rate";
                        TotalValue := "Qty. Loading" * Item."Customer's Transportation Rate";
                    end else begin
                        BranchItemTransportRates.RESET;
                        BranchItemTransportRates.setrange("From Location", "Posted Loading SLip Header"."Transport From Location");
                        BranchItemTransportRates.SetRange("Dispatch Area", "Posted Loading SLip Header"."Transport To Location");
                        BranchItemTransportRates.SetRange("Item No.", "Item No.");
                        IF BranchItemTransportRates.FINDFIRST THEN begin
                            TotalValue := (BranchItemTransportRates."Self Lift Customer Rate" * "Qty. Loading");
                            rate := BranchItemTransportRates."Self Lift Customer Rate";
                        end;
                    end;

                end;
            }
            trigger OnAfterGetRecord()
            var
                CustomerLRec: Record Customer;
                Resp: Record "Responsibility Center";
                Item: Record Item;
            begin
                HeaderCreated := false;
                if PrevPartyNo <> "Party No." then begin
                    postedLoadingSlip.Reset();
                    postedLoadingSlip.CopyFilters("Posted Loading SLip Header");
                    postedLoadingSlip.SetRange("Party No.", "Party No.");
                    if postedLoadingSlip.FindSet() then
                        repeat
                            if CustomerLRec.Get("Party No.") then
                                CustName := CustomerLRec.Name;
                            //PKONJU9
                            IF ExcluBlCust then
                                IF CustomerLRec.Blocked <> CustomerLRec.Blocked::" " then
                                    CurrReport.skip;
                            //PKONJU9
                            if (CreateCreditMemo) AND (postedLoadingSlip."Self Lifting Crdt Memo" = '') then begin //PKONJU9 need to uncomment once done
                                if not HeaderCreated then begin
                                    SalesHeader.Init();
                                    SalesHeader."Document Type" := SalesHeader."Document Type"::"Credit Memo";
                                    SalesHeader."No." := '';
                                    SalesHeader.Insert(true);

                                    SalesHeader.Validate("Sell-to Customer No.", postedLoadingSlip."Party No."); //------- check
                                    SalesHeader.Validate("Cr. Memo Reason Type", SalesHeader."Cr. Memo Reason Type"::Others);
                                    //PKONJU9>>
                                    SalesHeader.Validate("Responsibility Center", "Responsibility Center");
                                    //<<<<<< G2S 8988-CAS-01417-Y0D5F1
                                    Resp.Get("Responsibility Center");
                                    SalesHeader.Validate("Shortcut Dimension 1 Code", Resp."Global Dimension 1 Code");
                                    SalesHeader.Validate("Shortcut Dimension 2 Code", Resp."Global Dimension 2 Code");
                                    // >>>>>> G2S 8988-CAS-01417-Y0D5F1
                                    SalesHeader."Printable Comment 1" := 'Distributors Self Lifting allowance' + rebperd;
                                    SalesHeader."Rebate Period Code" := rebperd;
                                    SalesHeader."VAT Bus. Posting Group" := 'NOTVATABLE';
                                    SalesHeader."Reason Code" := 'SELFLIFT';  //RKD RFC#18 (17)
                                    //PKONJU9<<
                                    SalesHeader.Modify();
                                    HeaderCreated := true;
                                end;
                                postedLoadingSlip."Self Lifting Crdt Memo" := SalesHeader."No.";
                                postedLoadingSlip.Modify();
                                PstdLdngSlipLineGRec.Reset();
                                PstdLdngSlipLineGRec.SetRange("Document No.", postedLoadingSlip."No.");
                                PstdLdngSlipLineGRec.Setfilter("Item no.", '<>%1', '');//PKONOC2
                                if PstdLdngSlipLineGRec.FindSet() then
                                    repeat
                                        SalesLine.Init();
                                        SalesLine.Type := SalesLine.Type::"G/L Account"; //<<<<<<>>>>> G2S 8988-CAS-01417-Y0D5F1
                                        SalesLine."Document Type" := SalesHeader."Document Type";
                                        SalesLine."Document No." := SalesHeader."No.";
                                        SalesLine."Line No." := LineNoLVar;
                                        SalesLine.Insert();
                                        SalesrcvbGRec.Get();
                                        SalesrcvbGRec.TestField("Self Lifting Credit Memo");
                                        SalesLine.Validate("No.", SalesrcvbGRec."Self Lifting Credit Memo");
                                        Resp.get("Posted Loading SLip Header"."Responsibility Center");
                                        if Resp."Item Self Lift rate" then begin
                                            Item.get(PstdLdngSlipLineGRec."Item No.");
                                            GetDims(item, SalesLine); //<<<<<<>>>>> G2S 8988-CAS-01417-Y0D5F1
                                            Item.TestField("Customer's Transportation Rate");
                                            SalesLine.Validate(Quantity, 1);
                                            SalesLine.Validate("Unit Price", PstdLdngSlipLineGRec."Qty. Loading" * Item."Customer's Transportation Rate");
                                        end else begin
                                            BranchItemTransportRates2.RESET;
                                            BranchItemTransportRates2.setrange("From Location", postedLoadingSlip."Transport From Location");//PKONJ29
                                            BranchItemTransportRates2.SetRange("Dispatch Area", postedLoadingSlip."Transport To Location");//PKONJ29 whole report
                                            BranchItemTransportRates2.SetRange("Item No.", PstdLdngSlipLineGRec."Item No.");
                                            IF not BranchItemTransportRates2.FINDFIRST THEN
                                                Error('Self lifting cost setup is not existing for from location %1, To location %2 and Item no %3', PstdLdngSlipLineGRec."From Location", PstdLdngSlipLineGRec."To Location"
                                            , PstdLdngSlipLineGRec."Item No.");
                                            IF BranchItemTransportRates2.FINDFIRST THEN;
                                            SalesLine.Validate("Unit Price", "Posted Loading Slip Line"."Qty. Loading" * BranchItemTransportRates2."Self Lift Customer Rate");
                                            SalesLine.Validate(Quantity, 1);
                                        end;
                                        //PKONJU9>>
                                        SalesLine."Description 2" := 'Distributors Self Lifting allowance' + rebperd;
                                        SalesLine.VALIDATE("VAT Bus. Posting Group", 'NOTVATABLE');
                                        SalesLine.VALIDATE("VAT Prod. Posting Group", 'NOTVATABLE');

                                        //<<<<<< G2S 8988-CAS-01417-Y0D5F1
                                        SalesLine."Shortcut Dimension 1 Code" := salesheader."Shortcut Dimension 1 Code";
                                        SalesLine."Shortcut Dimension 2 Code" := salesheader."Shortcut Dimension 2 Code";
                                        // >>>>>> G2S 8988-CAS-01417-Y0D5F1

                                        //PKONJU9<<
                                        SalesLine.Modify();
                                        LineNoLVar += 10000;
                                    until PstdLdngSlipLineGRec.Next() = 0;
                            end;
                        until postedLoadingSlip.Next() = 0;
                    PrevPartyNo := "Party No.";
                end;
            end;
        }
    }

    requestpage
    {
        // SaveValues = true;
        layout
        {
            area(Content)
            {
                group(GroupName)
                {
                    field(rebperd; rebperd)
                    {
                        TableRelation = "Rebate Period Codes";//PKONJU9
                        Caption = 'Rebate Period Code';

                    }
                    field(CreateCreditMemo; CreateCreditMemo)
                    {
                        trigger OnValidate()
                        begin
                            IF rebperd = '' then
                                error('Please select the Rebate Period Code.'); //PKONJU()
                        end;

                    }
                    field(ShwDetails; ShwDetails)
                    {
                        Caption = 'Show Details.';//PKONJU9
                    }
                    field(ExcluBlCust; ExcluBlCust)
                    {
                        Caption = 'Exclude Blocked Customers.';//PKONJU9
                    }
                }
            }
        }

        actions
        {
            area(processing)
            {
                action(ActionName)
                {
                    ApplicationArea = All;

                }
            }
        }
    }
    trigger OnInitReport()
    begin
        CompanyInfo.Get;
        CompanyInfo.CalcFields(Picture);
    end;

    trigger OnPreReport()
    begin
        // deletecreditmemo();
    end;

    var
        myInt: Integer;
        CompanyInfo: Record "Company Information";
        CustName: Text[50];
        SelfLiftingRate: Decimal;
        TotalValue: Decimal;
        BranchItemTransportRates: Record "Branch Item Transport Rates";
        CreateCreditMemo: Boolean;
        SalesHeader: Record "Sales Header";
        SalesLine: Record "Sales Line";
        LineNoLVar: Integer;
        PstdLdngSlipLineGRec: Record "Posted Loading Slip Line";
        BranchItemTransportRates2: Record "Branch Item Transport Rates";
        SalesrcvbGRec: Record "Sales & Receivables Setup";
        PrevPartyNo: Code[20];
        postedLoadingSlip: Record "Posted Loading SLip Header";
        HeaderCreated: Boolean;
        Rate: Decimal;
        ShwDetails: Boolean;
        rebperd: code[20];
        ExcluBlCust: Boolean;

    // <<<<<< G2S 8988-CAS-01417-Y0D5F1
    procedure GetDims(Itemnrec: record Item; var Salin: Record "Sales Line");
    var
        DefaltDim: record "Default Dimension";
        DimMgmt: Codeunit DimensionManagement;
    begin
        TempDimSetEntry.DELETEALL();
        DefaltDim.Reset();
        DefaltDim.SetRange("Table ID", 27);
        DefaltDim.SetRange("No.", Itemnrec."No.");
        DefaltDim.SetFilter("Dimension Value Code", '<>%1', '');
        IF DefaltDim.FindSet() then
            repeat
                TempDimSetEntry.INIT;
                TempDimSetEntry.VALIDATE("Dimension Set ID", 0);
                TempDimSetEntry.VALIDATE("Dimension Code", DefaltDim."Dimension Code");
                TempDimSetEntry.VALIDATE("Dimension Value Code", DefaltDim."Dimension Value Code");
                TempDimSetEntry.INSERT(true);
            until DefaltDim.next = 0;
        Salin."Dimension Set ID" := DimMgmt.GetDimensionSetID(TempDimSetEntry);
        DimMgmt.UpdateGlobalDimFromDimSetID(Salin."Dimension Set ID", Salin."Shortcut Dimension 1 Code",
        Salin."Shortcut Dimension 2 Code");
    end;
    // >>>>>> G2S 8988-CAS-01417-Y0D5F1 

    var
        TempDimSetEntry: Record "Dimension Set Entry" temporary;    // <<<<<<>>>>> G2S 8988-CAS-01417-Y0D5F1

}