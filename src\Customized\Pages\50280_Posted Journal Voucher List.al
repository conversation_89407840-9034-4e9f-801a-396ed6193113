page 50280 "Posted Journal Voucher List"
{
    // version CHI6.0

    // PROJECT : CHI 6.0
    // **********************************************************************************
    // SIGN
    // **********************************************************************************
    // CHI     :  CHI 6.0 Developments
    // UNL     :  Univision Nigeria Limited
    // SAA     :  SAHEED ADIO ADEOSUN
    // **********************************************************************************
    // VER      SIGN         DATE          DESCRIPTION
    // **********************************************************************************
    // 1.0      UNL      06-Dec-11      -> Form Created to display Posted voucher documents list.

    Editable = false;
    PageType = List;
    SourceTable = "Posted Voucher Header";
    CardPageId = "Posted Journal Voucher";
    ApplicationArea = all;
    UsageCategory = lists;
    SourceTableView = WHERE("Voucher Type" = FILTER(JV));
    layout
    {
        area(content)
        {
            repeater(Control1000000000)
            {
                field("Posting Date"; "Posting Date")
                {
                    ApplicationArea = all;
                }
                field("Document No."; "Document No.")
                {
                    ApplicationArea = all;
                }
                field("Voucher Type"; "Voucher Type")
                {
                    ApplicationArea = all;
                }
                field("Receiving Code"; "Receiving Code")
                {
                    ApplicationArea = all;
                }
                field("Account Type"; "Account Type")
                {
                    ApplicationArea = all;
                }
                field("Created By Name"; "Created By Name")
                {
                    ApplicationArea = all;
                }
                field("Teller / Cheque No."; "Teller / Cheque No.")
                {
                    ApplicationArea = all;
                }
                field("Import File No."; "Import File No.")
                {
                    ApplicationArea = all;
                }
                field("Created Time"; "Created Time")
                {
                    ApplicationArea = all;
                }
                field("Posted By"; "Posted By")
                {
                    ApplicationArea = all;
                }
                field("Posted By Name"; "Posted By Name")
                {
                    ApplicationArea = all;
                }
                field("Posted Date"; "Posted Date")
                {
                    ApplicationArea = all;
                }
                field("Posted Time"; "Posted Time")
                {
                    ApplicationArea = all;
                }
                field("Created Date"; "Created Date")
                {
                    ApplicationArea = all;
                }
                field("Created By"; "Created By")
                {
                    ApplicationArea = all;
                }
                field("Account No."; "Account No.")
                {
                    ApplicationArea = all;
                }
                field("Account Name"; "Account Name")
                {
                    ApplicationArea = all;
                }
                field(Narration; Narration)
                {
                    ApplicationArea = all;
                }
                field(Amount; Amount)
                {
                    ApplicationArea = all;
                    Visible = false;
                }
                field("Amount (LCY)"; "Amount (LCY)")
                {
                    ApplicationArea = all;
                    Visible = false;
                }
                field("Voucher No."; "Voucher No.")
                {
                    ApplicationArea = all;
                }
                field("Approved By1"; "Approved By1")
                {
                    Visible = false;
                }
                field("Approved By2"; "Approved By2")
                {
                    ApplicationArea = all;
                    Visible = false;
                }
                field("Approved By3"; "Approved By3")
                {
                    ApplicationArea = all;
                    Visible = false;
                }
                field("Approved By4"; "Approved By4")
                {
                    ApplicationArea = all;
                    Visible = false;
                }
            }
        }
        //attachmentAdded G2S
        area(FactBoxes)
        {
            part("Attached Documents"; "Document Attachment Factbox")
            {
                ApplicationArea = All;
                Caption = 'Attachments';
                SubPageLink = "Table ID" = CONST(50118),
                                "No." = FIELD("Document No.");
                // Type = FIELD("Voucher Type");
            }
            systempart(PyamentTermsLinks; Links)
            {
                ApplicationArea = RecordLinks;
            }
            systempart(PyamentTermsNotes; Notes)
            {
                ApplicationArea = Notes;
            }
        }

    }

    actions
    {
    }
}

