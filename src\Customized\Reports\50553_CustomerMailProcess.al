Report 50553 CustomerMailProcess
{
    Caption = 'Customer Mail Process_50553';
    UsageCategory = Administration;
    Permissions = tabledata "E-mail Log" = irm;
    ApplicationArea = All;
    ProcessingOnly = true;
    dataset
    {
        dataitem(Customer; Customer)
        {
            RequestFilterFields = "No.";
            //DataItemTableView = where(Blocked = const(" "));//FIX11Jun2021//FIX30Jun2021
            trigger OnPreDataItem()
            BEGIN
                //SetRange("No.", CustCodeGVar);
                Window.Open('Customer Name:   #1##########\');
            END;

            trigger OnAfterGetRecord()
            begin
                Window.Update(1, Name);
                SendMail();
            END;

            trigger OnPostDataItem()
            begin
                Window.Close();
            END;

        }
    }



    requestpage
    {
        SaveValues = true; // G2S CAS-01379-X2R2Z9 
        layout
        {
            area(Content)
            {
                group(Select)
                {
                    field(StartDate; StartDate)
                    {
                        ApplicationArea = ALL;
                    }
                    field(EndDate; EndDate)
                    {
                        ApplicationArea = ALL;
                    }
                    field(SendMailtoCustomer; SendMailtoCustomer)
                    {
                        ApplicationArea = all;
                    }
                    field(RequesterMail; RequesterMail)
                    {
                        ApplicationArea = all;
                    }

                }
            }
        }

        actions
        {
        }
    }
    trigger OnInitReport()
    begin
        //  Clear(CustCodeGVar);

    end;

    local procedure SendMail()
    var
        SMTPMailSetup: Record "SMTP Mail Setup";
        SMTPMail: Codeunit "SMTP Mail";
        SenderAddr: Text;
        RecepientAddr: List of [Text];
        SubjectTxt: text;
        CustomerLedEntry: Record "Cust. Ledger Entry";
        Salerec: Record "Sales & Receivables Setup";
        Mailmng: Codeunit "Mail Management";//PKmailCheck
    begin
        //PKONJ22 function need to Move
        IF Customer.Blocked <> Customer.Blocked::" " THEN BEGIN
            InsertLog('Customer Is Blocked.', Customer."No." + ' ' + Customer.Name);
            CurrReport.Skip;
        END;
        SMTPMailSetup.get();
        Salerec.GET();
        IF Mailmng.CheckValidEmailAddress(SMTPMailSetup."User ID") THEN
            SenderAddr := SMTPMailSetup."User ID"
        else BEGIN
            InsertLog('SMTP Email ID Issue.', Customer."No." + ' ' + Customer.Name);
            CurrReport.Skip;
        end;
        IF SendMailtoCustomer then begin
            IF Customer."E-Mail" <> '' THEN BEGIN
                IF Mailmng.CheckValidEmailAddress(Customer."E-Mail") THEN
                    RecepientAddr.Add(Customer."E-Mail")
                else BEGIN
                    InsertLog('Wrong cust Email ID.', Customer."No." + ' ' + Customer.Name);
                    CurrReport.Skip;
                end;
            end Else BEGIN
                InsertLog('No email ID.', Customer."No." + ' ' + Customer.Name);
                CurrReport.Skip;
            END;
            IF Salerec."Customer Statement Mail CC1" <> '' then
                IF Mailmng.CheckValidEmailAddress(Salerec."Customer Statement Mail CC1") THEN
                    RecepientAddr.Add(Salerec."Customer Statement Mail CC1")
                else BEGIN
                    InsertLog('CC Email ID 1 ' + Salerec."Customer Statement Mail CC1" + ' Is Wrong', Customer."No." + ' ' + Customer.Name);
                    CurrReport.Skip;
                end;
            IF Salerec."Customer Statement Mail CC2" <> '' then
                IF Mailmng.CheckValidEmailAddress(Salerec."Customer Statement Mail CC2") THEN
                    RecepientAddr.Add(Salerec."Customer Statement Mail CC2")
                else BEGIN
                    InsertLog('CC Email ID 2 ' + Salerec."Customer Statement Mail CC2" + ' Is Wrong', Customer."No." + ' ' + Customer.Name);
                    CurrReport.Skip;
                end;
        end else
            IF Mailmng.CheckValidEmailAddress(RequesterMail) THEN
                RecepientAddr.Add(RequesterMail)
            else BEGIN
                InsertLog('Requester self Email ' + RequesterMail + ' Is Wrong', Customer."No." + ' ' + Customer.Name);
                CurrReport.Skip;
            end;
        //SubjectTxt := 'Customer Statement from ' + FORMAT(StartDate) + ' to ' + Format(EndDate);
        SubjectTxt := FORMAT(EndDate, 0, '<Month Text>') + ' ' + FORMAT(Date2DMY(EndDate, 3)) + ' ' + Customer."No." + ' Statement Of Account from CHI Limited.';

        SMTPMAil.CreateMessage('Mail By ERP Team', SenderAddr, RecepientAddr, SubjectTxt, '', true);
        /*IF Customer."Alternate E-Mail" <> '' then
            SMTPMail.AddCC(RecepientAddr);*/
        /*SmtpMail.AppendBody('CHI Limited');
        SmtpMail.AppendBody('<br>');
        SmtpMail.AppendBody('<br>');*/
        SmtpMail.AppendBody('Dear ' + Customer.Name);
        SMTPMail.AppendBody('<br>');
        SmtpMail.AppendBody('<br>');
        SMTPMail.AppendBody('Below is the statement of your account as at  ' + Format(EndDate));
        SMTPMail.AppendBody('<br>');
        SMTPMail.AppendBody('<br>');
        SMTPMail.AppendBody('Details as below:');
        SMTPMail.AppendBody(CreateEmailBody());
        SMTPMail.AppendBody('<br>');
        SMTPMail.AppendBody('<br>');
        SMTPMail.AppendBody('<br>');
        SMTPMail.AppendBody('Having considered your above statement, please notify CHI Limited of your area of concerns');
        SMTPMail.AppendBody('<br>');
        SMTPMail.AppendBody('(if any) immedidately by forwarding such concerns (with supporting documents) to us in a letter');
        SMTPMail.AppendBody('<br>');
        SMTPMail.AppendBody('duly signed by your authorsed personnel(s) and delivered to our offices. A scanned copy of the letter and the supporting documents should be sent simultaneously by email to :');
        SMTPMail.AppendBody('<br>');
        SMTPMail.AppendBody('<br>');
        SMTPMail.AppendBody('<br>');
        SMTPMail.AppendBody('NOTE: Where no reponse is received from you after Two(2) weeks of your receipt of this balance confirmation letter, you shall be deemed to have agreed with your balance and contents');
        SMTPMail.AppendBody('<br>');
        SMTPMail.AppendBody('of your customer account statement with us as at the date stated above. Same shall be conclusive and consequently binding on you.');
        SMTPMail.AppendBody('<br>');
        SMTPMail.AppendBody('<br>');
        SMTPMail.AppendBody('Any indication of Goods returns, either implied or expressed does not constitute a guarantee undertaken by Chi Limited');
        SMTPMail.AppendBody('<br>');
        SMTPMail.AppendBody('In addition: please be reminded not to remit cash to any sales representative or staff of Chi Limited as per policy and agreement with you');
        SMTPMail.AppendBody('<br>');
        SMTPMail.AppendBody('<br>');
        SMTPMail.AppendBody('Thanks for your patronage');
        SMTPMail.AppendBody('<br>');
        SMTPMail.AppendBody('<br>');
        SMTPMail.AppendBody('Regards');
        SMTPMail.AppendBody('<br>');
        SMTPMail.AppendBody('CHI Limited');
        SMTPMail.AppendBody('<br>');
        SMTPMail.AppendBody('<br>');
        SMTPMail.AppendBody('<br>');
        //SMTPMail.AppendBody('Further enquiry, contact Ogunleye Bosede on 09063057358 and Mojisola Osunbanjo on 08025900600, E-mail: <EMAIL> ; <EMAIL> ');//PKON22FE2
        SMTPMail.AppendBody('Further enquiry, contact confidence ekeogu on 09139353223 and Sodiq Orogbo on 08067707570, E-mail: <EMAIL> ; <EMAIL>');//PKON22FE3
        IF SMTPMAil.send THEN
            InsertLog('Email Sent Succesfully', Customer."No." + ' ' + Customer.Name)
        else
            InsertLog('Faliure In Sending Mail', Customer."No." + ' ' + Customer.Name);
    end;

    Local procedure CreateEmailBody() EmailBodyText: Text
    var
        CustLedEntry: Record "Cust. Ledger Entry";
        CheckDate: Date;
        CustLedEntrBal: Record "Cust. Ledger Entry";
        PstInHeader: Record "Sales Invoice Header";
        DefaultQty, InvPercent : Decimal;
        RecCount: Integer; // G2S CAS-01379-X2R2Z9 
        Bal: Decimal;
    begin
        GrossAmount := 0;
        RecCount := 0;
        EmailBodyText += '<table border="1">';
        EmailBodyText += '<tr>';
        EmailBodyText += StrSubstNo('<td>%1</td>', 'Posting Date');
        EmailBodyText += StrSubstNo('<td>%1</td>', 'Document Type');
        EmailBodyText += StrSubstNo('<td>%1</td>', 'Document No.');
        EmailBodyText += StrSubstNo('<td>%1</td>', 'Description');
        //EmailBodyText += StrSubstNo('<td>%1</td>', 'Opening Balance');
        EmailBodyText += StrSubstNo('<td>%1</td>', 'Gross Amount'); // G2S CAS-01379-X2R2Z9 
        EmailBodyText += StrSubstNo('<td>%1</td>', 'Fixed Discount Amount');
        EmailBodyText += StrSubstNo('<td>%1</td>', 'Variable Discount Amount'); // G2S CAS-01379-X2R2Z9 
        EmailBodyText += StrSubstNo('<td>%1</td>', 'Debit Amount');
        EmailBodyText += StrSubstNo('<td>%1</td>', 'Credit Amount');
        EmailBodyText += StrSubstNo('<td>%1</td>', 'Balance');
        EmailBodyText += '</tr>';
        //message('%1', Customer."No.");
        CustLedEntry.Reset();
        CustLedEntry.SetCurrentKey("Entry No.");
        CustLedEntry.SetRange("Customer No.", Customer."No.");
        CustLedEntry.SetRange("Posting Date", StartDate, EndDate);
        IF CustLedEntry.FindSet() then BEGIN
            EmailBodyText += '<tr>';
            EmailBodyText += StrSubstNo('<td>%1</td>', 'Opening Balance');
            //EmailBodyText += StrSubstNo('<td>%1</td>', '');
            EmailBodyText += StrSubstNo('<td>%1</td>', '');
            EmailBodyText += StrSubstNo('<td>%1</td>', '');
            EmailBodyText += StrSubstNo('<td>%1</td>', '');
            EmailBodyText += StrSubstNo('<td>%1</td>', '');
            EmailBodyText += StrSubstNo('<td>%1</td>', '');
            // G2S CAS-01379-X2R2Z9 
            EmailBodyText += StrSubstNo('<td>%1</td>', '');
            EmailBodyText += StrSubstNo('<td>%1</td>', '');
            EmailBodyText += StrSubstNo('<td>%1</td>', '');
            // G2S CAS-01379-X2R2Z9 

            //Customer.Opening Balance
            Clear(Bal);
            CustLedEntrBal.RESET;
            CustLedEntrBal.SetRange("Customer No.", Customer."No.");
            CustLedEntrBal.SetFilter("Posting Date", '<%1', StartDate);
            IF CustLedEntrBal.FindSet() then
                repeat
                    CustLedEntrBal.CalcFields(Amount);
                    Bal += CustLedEntrBal.Amount;
                until CustLedEntrBal.next = 0;

            EmailBodyText += StrSubstNo('<td>%1</td>', Bal);
            EmailBodyText += '</tr>';
            repeat
                // G2S CAS-01379-X2R2Z9 
                PartialRecCount := 0;
                IsCompleteLineCount := 0;
                GrossAmount := 0;
                VariableDiscAmt2 := 0;
                totalLineAmount := 0;
                BaseLineAmt := 0;
                DiscLineAmt := 0;
                BaseLineAmt2 := 0;
                FixedDiscAmt2 := 0;
                VATAmt2 := 0;
                RecCount := 0;
                LineAmtBeforeVat2 := 0;
                PartialLineAmtBeforeVat2 := 0;
                LineAmtAfterVAT2 := 0;
                TotalDiscAmt2 := 0;
                // G2S CAS-01379-X2R2Z9 

                Clear(Bal);
                CustLedEntry.CalcFields(Amount);
                EmailBodyText += '<tr>';
                EmailBodyText += StrSubstNo('<td>%1</td>', CustLedEntry."Posting Date");

                EmailBodyText += StrSubstNo('<td>%1</td>', CustLedEntry."Document Type");
                EmailBodyText += StrSubstNo('<td>%1</td>', CustLedEntry."Document No.");
                if CustLedEntry."Description 2" <> '' then //RFC#2024_16
                    EmailBodyText += StrSubstNo('<td>%1</td>', CustLedEntry."Description 2") else //RFC#2024_16
                    EmailBodyText += StrSubstNo('<td>%1</td>', CustLedEntry.Description);

                // G2S CAS-01379-X2R2Z9 
                if CustLedEntry."Document Type" = CustLedEntry."Document Type"::Invoice then begin
                    PstSalesInvLine.SetFilter("Document No.", '%1', CustLedEntry."Document No.");
                    PstSalesInvLine.SetFilter("Line Discount %", '<>%1', 0);
                    if PstSalesInvLine.FindSet() then begin
                        RecCount := PstSalesInvLine.Count();
                        repeat
                            VariableDiscAmt := 0;
                            FixedDiscAmt := 0;
                            BaseLineAmt := 0;
                            DiscLineAmt := 0;
                            VATAmt := 0;
                            IsFixedDiscount := false;
                            IsVariableDiscount := false;
                            IsLineDiscountPercent := true;
                            PartialLineAmtBeforeVat := 0;
                            LineAmtBeforeVat := 0;
                            IsPartialTrxn := false;
                            // DefaultQty := 0;
                            // InvPercent := 0;

                            if PstSalesInvLine.Quantity = 0 then IsZeroQty := true else IsZeroQty := false;
                            if PstSalesInvLine."Fixed Rebate" <> 0 then IsFixedDiscount := true;
                            if PstSalesInvLine."Line Discount %" > PstSalesInvLine."Fixed Rebate" then IsVariableDiscount := true;

                            if not ((PstSalesInvLine."Line Discount %" <> 0) and (PstSalesInvLine."Line Discount %" > PstSalesInvLine."Fixed Rebate")) then
                                IsLineDiscountPercent := false;

                            if not IsZeroQty then begin

                                if (PstSalesInvLine."Fixed Rebate Amount to Inv." <> PstSalesInvLine."Fixed Rebate Amount") or
                                (PstSalesInvLine."Fixed Rebate Amount to Inv." < PstSalesInvLine."Fixed Rebate Amount") then begin
                                    IsPartialTrxn := true;
                                    IsLinePartial += 1;
                                end else
                                    IsCompleteLineCount += 1;

                                if IsPartialTrxn then begin
                                    PstInHeader.SetRange("No.", PstSalesInvLine."Document No.");
                                    if PstInHeader.FindFirst() then
                                        DefaultQty := GetDefaultQty(PstInHeader."Order No.", PstSalesInvLine."No.");

                                    InvPercent := GetLineAmtPercentage(PstSalesInvLine.Quantity, DefaultQty);
                                end;

                                // Calculate discount 
                                //Line Discount % | Variable Discount Amount
                                if IsLineDiscountPercent then
                                    if not IsZeroQty then begin
                                        VariableDiscAmt := PstSalesInvLine."Rebate Discount";
                                        VariableDiscAmt2 += PstSalesInvLine."Rebate Discount";
                                    end;

                                //Fixed Discount % | Fixed Discount Amount
                                if IsPartialTrxn then begin
                                    FixedDiscAmt := PstSalesInvLine."Fixed Rebate Amount to Inv.";
                                    FixedDiscAmt2 += FixedDiscAmt;
                                end else begin
                                    FixedDiscAmt := PstSalesInvLine."Fixed Rebate Amount";
                                    FixedDiscAmt2 += FixedDiscAmt;
                                end;

                                TotalDiscAmt := FixedDiscAmt + VariableDiscAmt;
                                TotalDiscAmt2 += TotalDiscAmt;

                                BaseLineAmt := PstSalesInvLine.Quantity * PstSalesInvLine."Unit Price";
                                BaseLineAmt2 += BaseLineAmt;

                                if not IsPartialTrxn then begin
                                    if IsFixedDiscount and not IsVariableDiscount then begin
                                        LineAmtBeforeVat := (PstSalesInvLine.Quantity * PstSalesInvLine."Unit Price") - FixedDiscAmt;
                                        LineAmtBeforeVat2 += LineAmtBeforeVat;
                                    end;

                                    if IsVariableDiscount and IsFixedDiscount then begin
                                        LineAmtBeforeVat := (PstSalesInvLine.Quantity * PstSalesInvLine."Unit Price") - TotalDiscAmt;
                                        LineAmtBeforeVat2 += LineAmtBeforeVat;
                                    end;

                                    if not IsVariableDiscount and not IsFixedDiscount then begin
                                        LineAmtBeforeVat := PstSalesInvLine.Quantity * PstSalesInvLine."Unit Price";
                                        LineAmtBeforeVat2 += LineAmtBeforeVat;
                                    end;
                                end else begin
                                    PartialLineAmtBeforeVat := (PstSalesInvLine.Quantity * PstSalesInvLine."Unit Price") - PstSalesInvLine."Fixed Rebate Amount to Inv."; //((PstSalesInvLine.Quantity * PstSalesInvLine."Unit Price") * (PstSalesInvLine."Fixed Rebate" / 100));
                                    PartialLineAmtBeforeVat2 += PartialLineAmtBeforeVat;
                                end;

                                // Vat Calculation
                                if PstSalesInvLine."VAT %" > 0 then begin
                                    VATAmt := (LineAmtBeforeVat + PartialLineAmtBeforeVat) * (PstSalesInvLine."VAT %" / 100);
                                    VATAmt2 += VATAmt;
                                    LineAmtAfterVAT := LineAmtBeforeVat + PartialLineAmtBeforeVat + VATAmt;
                                    GrossAmountVat += LineAmtAfterVAT;

                                    LineAmtAfterVAT2 += LineAmtAfterVAT;
                                end else begin
                                    LineAmtAfterVAT2 += LineAmtBeforeVat + PartialLineAmtBeforeVat;
                                    GrossAmountNOVat += LineAmtAfterVAT2;
                                end;

                            end;
                        until PstSalesInvLine.Next() = 0;
                    end;

                    // PartialRecCount := getPartlineRec(CustLedger);
                    PartialRecCount := IsLinePartial;
                    totalLineAmount := LineAmtBeforeVat2 + PartialLineAmtBeforeVat2;

                    if PartialRecCount = 0 then
                        GrossAmount := BaseLineAmt2 + VATAmt2;

                    if PartialRecCount > 0 then begin
                        if PartialRecCount = RecCount then
                            GrossAmount := PartialLineAmtBeforeVat2 + TotalDiscAmt2 + VATAmt2
                        else
                            GrossAmount := totalLineAmount + VATAmt2 + TotalDiscAmt2;
                    end;

                    GrossAmount := Round(GrossAmount, 0.01, '>')
                end else
                    GrossAmount := 0;

                EmailBodyText += StrSubstNo('<td>%1</td>', GrossAmount);
                EmailBodyText += StrSubstNo('<td>%1</td>', FixedDiscAmt2);
                EmailBodyText += StrSubstNo('<td>%1</td>', VariableDiscAmt2);
                // G2S CAS-01379-X2R2Z9 

                // EmailBodyText += StrSubstNo('<td>%1</td>', Bal);
                CustLedEntry.CalcFields(Amount);
                IF CustLedEntry.Amount > 0 then
                    EmailBodyText += StrSubstNo('<td>%1</td>', CustLedEntry.Amount)
                else
                    EmailBodyText += StrSubstNo('<td>%1</td>', 0);

                IF CustLedEntry.Amount < 0 then
                    EmailBodyText += StrSubstNo('<td>%1</td>', -1 * CustLedEntry.Amount)
                else
                    EmailBodyText += StrSubstNo('<td>%1</td>', 0);

                Clear(Bal);
                CustLedEntrBal.RESET;
                CustLedEntrBal.SetRange("Customer No.", Customer."No.");
                CustLedEntrBal.SetFilter("Entry No.", '<=%1', CustLedEntry."Entry No.");
                IF CustLedEntrBal.FindSet() then
                    repeat
                        CustLedEntrBal.CalcFields(Amount);
                        Bal += CustLedEntrBal.Amount;
                    until CustLedEntrBal.next = 0;
                //EmailBodyText += StrSubstNo('<td>%1</td>', Customer."Credit Amount");
                EmailBodyText += StrSubstNo('<td>%1</td>', Bal);
                EmailBodyText += '</tr>';
            until CustLedEntry.Next = 0;
            EmailBodyText += '<tr>';
            EmailBodyText += StrSubstNo('<td>%1</td>', 'Closing Balance');
            //EmailBodyText += StrSubstNo('<td>%1</td>', '');
            EmailBodyText += StrSubstNo('<td>%1</td>', '');
            EmailBodyText += StrSubstNo('<td>%1</td>', '');
            EmailBodyText += StrSubstNo('<td>%1</td>', '');
            EmailBodyText += StrSubstNo('<td>%1</td>', '');
            EmailBodyText += StrSubstNo('<td>%1</td>', '');
            // G2S CAS-01379-X2R2Z9 
            EmailBodyText += StrSubstNo('<td>%1</td>', '');
            EmailBodyText += StrSubstNo('<td>%1</td>', '');
            EmailBodyText += StrSubstNo('<td>%1</td>', '');
            // G2S CAS-01379-X2R2Z9 

            //Customer.Opening Balance
            Clear(Bal);
            CustLedEntrBal.RESET;
            CustLedEntrBal.SetRange("Customer No.", Customer."No.");
            CustLedEntrBal.SetFilter("Posting Date", '<=%1', EndDate);
            IF CustLedEntrBal.FindSet() then
                repeat
                    CustLedEntrBal.CalcFields(Amount);
                    Bal += CustLedEntrBal.Amount;
                until CustLedEntrBal.next = 0;
            EmailBodyText += StrSubstNo('<td>%1</td>', Bal);
            EmailBodyText += '</tr>';
        end else BEGIN
            InsertLog('No Statement Available For ', Customer."No." + ' ' + Customer.Name + 'during the Period is ' + FORMAT(StartDate) + '..' + FORMAT(EndDate));//PKONJU13.2
            CurrReport.Skip();
        end;
        EmailBodyText += '</table>';
        exit(EmailBodyText);
    end;

    trigger OnPreReport()
    BEGIN
        IF (StartDate = 0D) OR (EndDate = 0D) THEN
            Error('Please Select Both Start date and End date.');
        /*if NOT SendMailtoCustomer = false then
            RequesterMail := '';*/
        if NOT SendMailtoCustomer then
            IF RequesterMail = '' then
                Error('Please define requester Email ID.');
    END;

    trigger onpostreport()
    BEGIN
        message('Process Completed.');//PKONJ18.2
    END;


    procedure SetValues(CustCode: Code[20])
    begin
        CustCodeGVar := CustCode;
    end;



    Procedure InsertLog(Res: Text[1024]; Detail: text[1024])
    var
        maillog: record "E-mail Log";
        EntryNo: Integer;
    begin
        IF maillog.FINDLAST THEN
            EntryNo := maillog."Entry No" + 1
        ELSE
            EntryNo := 1;

        maillog.INIT;
        maillog."Entry No" := EntryNo;
        maillog."Document Type" := maillog."Document Type"::"Customer Statement";//Entry Type Is fixed //PKONJU5
        maillog.Result := Res;
        maillog.Details := Detail;
        Res := '';
        Detail := '';
        maillog."User Id" := USERID;
        maillog."Date Time" := CURRENTDATETIME;
        maillog.Insert();
    end;

    local procedure getPartlineRec(CustLedger: Record "Cust. Ledger Entry"): Integer;
    var
        recCount: Integer;
    begin
        recCount := 0;
        IsLinePartial := 0;
        PstSalesInvLine2.Reset();
        PstSalesInvLine2.SetFilter("Document No.", '%1', CustLedger."Document No.");
        PstSalesInvLine2.SetFilter("Line Discount %", '<>%1', 0);
        if PstSalesInvLine2.FindSet() then begin
            repeat
                if (PstSalesInvLine2.Quantity <> 0) and (PstSalesInvLine2."Fixed Rebate Amount to Inv." < PstSalesInvLine2."Fixed Rebate Amount") then
                    IsLinePartial += 1;
            until PstSalesInvLine2.Next() = 0;
        end;
        recCount := IsLinePartial;
        exit(recCount);
    end;

    local procedure GetLineAmtPercentage(var InvQty: Decimal; DefaultQty: Decimal) InvPercentVal: Decimal
    begin
        exit((InvQty / DefaultQty) * 100)
    end;

    local procedure GetDefaultQty(var OrderNo: Code[20]; ItemNo: code[20]) DefaultQty: Decimal
    begin
        "Sales Line Archive".SetRange("Document No.", OrderNo);
        "Sales Line Archive".SetFilter("No.", '%1', ItemNo);
        if "Sales Line Archive".FindFirst() then
            exit("Sales Line Archive".Quantity);
    end;

    var
        // G2S CAS-01379-X2R2Z9 
        "Sales Header Archive": Record "Sales Header Archive";
        "Sales Line Archive": Record "Sales Line Archive";
        PartialRecCount, RecCount, IsLinePartial, IsCompleteLineCount : Integer;
        IsZeroQty, IsLineDiscountPercent, IsPartialTrxn, IsFixedDiscount, IsVariableDiscount : Boolean;
        totalLineAmount, GrossAmount, GrossAmountVat, GrossAmountNOVat, VATAmt, VATAmt2, LineAmtAfterVAT, LineAmtAfterVAT2, PartialLineAmtBeforeVat, PartialLineAmtBeforeVat2, LineAmtBeforeVat, LineAmtBeforeVat2, FixedDiscAmt, VariableDiscAmt, FixedDiscAmt2, VariableDiscAmt2, TotalDiscAmt, TotalDiscAmt2, BaseLineAmt, BaseLineAmt2, DiscLineAmt : Decimal;
        PstSalesInvLine, PstSalesInvLine2 : Record "Sales Invoice Line";
        // G2S CAS-01379-X2R2Z9 
        myInt: Integer;
        CustCodeGVar: code[20];
        SMTPSetup: Record "SMTP Mail Setup";
        Mail: Codeunit "SMTP Mail";
        Bodymsg: Text[1024];
        StartDate: Date;
        EndDate: date;
        SendMailtoCustomer: Boolean;
        RequesterMail: Text[50];
        Window: Dialog;

}