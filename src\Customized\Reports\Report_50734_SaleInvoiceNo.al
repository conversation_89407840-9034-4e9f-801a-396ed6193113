report 50734 "Sales Invoice Nos_Copy"
{
    DefaultLayout = RDLC;
    RDLCLayout = './SalesInvoiceNos.rdl';
    Caption = 'Sales Invoice Nos_50734';
    ObsoleteState = Pending;
    ObsoleteReason = 'Infrequently used report.';
    ObsoleteTag = '18.0';
    UsageCategory = ReportsAndAnalysis;
    ApplicationArea = all;

    dataset
    {
        dataitem("Sales Invoice Header"; "Sales Invoice Header")
        {

            RequestFilterFields = "No.";
            RequestFilterHeading = 'Posted Sales Invoice';
            CalcFields = Amount;
            DataItemTableView = SORTING("No.") where(Amount = filter(<> 0));//FIXb2b3108
            column(COMPANYNAME; COMPANYPROPERTY.DisplayName)
            {
            }
            column(STRSUBSTNO_Text004_SalesInvHeaderFilter_; StrSubstNo(Text004, SalesInvHeaderFilter))
            {
            }
            column(SalesInvHeaderFilter; SalesInvHeaderFilter)
            {
            }
            column(PageGroupNo; PageGroupNo)
            {
            }
            column(Sales_Invoice_Header_No_; "No.")
            {
            }
            column(Sales_Invoice_Nos_Caption; Sales_Invoice_Nos_CaptionLbl)
            {
            }
            column(CurrReport_PAGENOCaption; CurrReport_PAGENOCaptionLbl)
            {
            }
            column(SalesInvHeader__No__Caption; SalesInvHeader.FieldCaption("No."))
            {
            }
            column(SalesInvHeader__Source_Code_Caption; SalesInvHeader.FieldCaption("Source Code"))
            {
            }
            column(SalesInvHeader__User_ID_Caption; SalesInvHeader.FieldCaption("User ID"))
            {
            }
            column(SalesInvHeader__Bill_to_Name_Caption; SalesInvHeader.FieldCaption("Bill-to Name"))
            {
            }
            column(SalesInvHeader__Bill_to_Customer_No__Caption; SalesInvHeader.FieldCaption("Bill-to Customer No."))
            {
            }
            column(SalesInvHeader__AmountIncluingVat; SalesInvHeader.FieldCaption("Amount Including VAT"))
            {
            }
            column(SalesInvHeader__appliestodocno__Caption; SalesInvHeader.FieldCaption("Applies-to Doc. No."))
            {
            }
            column(SalesInvHeader__ExternialDocumentNoCaption; SalesInvHeader.FieldCaption("External Document No."))
            {
            }
            column(SalesInvHeader__PosTranstionTypeCaption; SalesInvHeader.FieldCaption("POS Transaction Type"))
            {
            }
            column(SalesInvHeader__PosBankNameCaption; SalesInvHeader.FieldCaption("Pos Bank Name"))
            {
            }
            column(SalesInvHeader__PosCardAmountCaption; SalesInvHeader.FieldCaption("POS Card Amount"))
            {
            }
            column(SalesInvHeader__PosCashAmountCaption; SalesInvHeader.FieldCaption("POS Cash Amount"))
            {
            }
            column(SourceCode_DescriptionCaption; SourceCode_DescriptionCaptionLbl)
            {
            }
            column(SalesInvHeader__Posting_Date_Caption; SalesInvHeader__Posting_Date_CaptionLbl)
            {
            }
            dataitem(ErrorLoop; "Integer")
            {
                DataItemTableView = SORTING(Number);
                column(ErrorText_Number_; ErrorText[Number])
                {
                }
                column(NewPage; NewPage)
                {
                }
                column(ErrorText_Number__Control15; ErrorText[Number])
                {
                }
                column(ErrorText_Number__Control15Caption; ErrorText_Number__Control15CaptionLbl)
                {
                }

                trigger OnPostDataItem()
                begin
                    ErrorCounter := 0;
                end;

                trigger OnPreDataItem()
                begin
                    SetRange(Number, 1, ErrorCounter);
                end;
            }
            dataitem(SalesInvHeader; "Sales Invoice Header")
            {
                DataItemLink = "No." = FIELD("No.");
                DataItemTableView = SORTING("No.");
                column(SalesInvHeader__User_ID_; "User ID")
                {
                }
                column(SourceCode_Description; SourceCode.Description)
                {
                }
                column(SalesInvHeader__Source_Code_; "Source Code")
                {
                }
                column(SalesInvHeader__Bill_to_Name_; "Bill-to Name")
                {
                }
                column(SalesInvHeader__Bill_to_Customer_No__; "Bill-to Customer No.")
                {
                }
                column(SalesInvHeader__No__; DocumentNo)
                {
                }
                column(SalesInvHeader__Posting_Date_; Format("Posting Date"))
                {
                }
                column(POS_User_ID; "POS User ID")
                {
                }
                column(Amount_Including_VAT; "Amount Including VAT")
                {
                }
                column(Applies_to_Doc__No_; "Applies-to Doc. No.")
                {
                }
                column(External_Document_No_; "External Document No.")
                {
                }
                column(POS_Transaction_No_; "POS Transaction No.")
                {
                }
                column(POS_Transaction_Type; "POS Transaction Type")
                {
                }
                column(Pos_Bank_Name; "Pos Bank Name")
                {

                }
                column(POS_Bank_Names; "POS Bank Names")
                {

                }
                column(POS_Cash_Amount; "POS Cash Amount")
                {
                }
                column(POS_Card_Amount; "POS Card Amount")
                {
                }
                column(isClearwox; isClearwox)
                {

                }


                trigger OnAfterGetRecord()
                begin
                    Clear(DocumentNo);
                    DocumentNo := "No.";
                    if ("POS Transaction No." <> '') and ("External Document No." = '') then
                        "External Document No." := "POS Transaction No.";
                    if PrintExcel then begin
                        RowNo += 1;
                        EnterCell(RowNo, 1, "No.", FALSE, FALSE, FALSE);
                        EnterCell(RowNo, 2, Format("Posting Date"), FALSE, FALSE, FALSE);
                        EnterCell(RowNo, 3, "Bill-to Customer No.", FALSE, FALSE, FALSE);
                        EnterCell(RowNo, 4, "Bill-to Name", FALSE, FALSE, FALSE);
                        EnterCell(RowNo, 5, "Source Code", FALSE, FALSE, FALSE);
                        EnterCell(RowNo, 6, "User ID", FALSE, FALSE, FALSE);
                        EnterCell(RowNo, 7, Format("Amount Including VAT"), FALSE, FALSE, FALSE);
                        EnterCell(RowNo, 8, "Applies-to Doc. No.", FALSE, FALSE, FALSE);
                        EnterCell(RowNo, 9, "External Document No.", FALSE, FALSE, FALSE);
                        EnterCell(RowNo, 10, Format("Pos Transaction Type"), FALSE, FALSE, FALSE);
                        EnterCell(RowNo, 11, Format("Pos Bank Name"), FALSE, FALSE, FALSE);
                        EnterCell(RowNo, 12, Format("POS Bank Names"), FALSE, FALSE, FALSE);
                        EnterCell(RowNo, 13, Format("Pos Card Amount"), FALSE, FALSE, FALSE);
                        EnterCell(RowNo, 14, Format("Pos Cash Amount"), FALSE, FALSE, FALSE);
                    end;
                end;
            }

            trigger OnAfterGetRecord()
            begin
                if "Source Code" <> SourceCode.Code then
                    if not SourceCode.Get("Source Code") then
                        SourceCode.Init();
                if "No. Series" <> NoSeries.Code then
                    if not NoSeries.Get("No. Series") then
                        NoSeries.Init();

                if ("No. Series" <> LastNoSeriesCode) or FirstRecord then begin
                    if "No. Series" = '' then
                        AddError(Text000)
                    else
                        AddError(
                          StrSubstNo(
                            Text001,
                            "No. Series", NoSeries.Description));
                    if not FirstRecord then
                        PageGroupNo := PageGroupNo + 1;
                    NewPage := true;
                end else begin
                    if LastNo <> '' then
                        if not ("No." in [LastNo, IncStr(LastNo)]) then
                            AddError(Text002)
                        else
                            if "Posting Date" < LastPostingDate then
                                AddError(Text003);
                    NewPage := false;
                end;

                LastNo := "No.";
                LastPostingDate := "Posting Date";
                LastNoSeriesCode := "No. Series";
                FirstRecord := false;

                PosPayment.Reset();
                PosPayment.SetRange(TransactionID, "Sales Invoice Header"."External Document No.");
                if PosPayment.FindFirst() then
                    isClearwox := true
                else
                    isClearwox := false;

            end;

            trigger OnPreDataItem()
            begin
                FirstRecord := true;
                PageGroupNo := 1;
                IF PrintExcel THEN BEGIN
                    TempExcelBuffer.DELETEALL;
                    CLEAR(TempExcelBuffer);

                    RowNo := 1;
                    EnterCell(RowNo, 1, 'Sales Invoice Nos.', TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 15, FORMAT(TODAY, 0, 4), TRUE, FALSE, FALSE);
                    RowNo += 1;
                    EnterCell(RowNo, 1, FORMAT(UPPERCASE(COMPANYNAME)), TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 15, FORMAT(USERID), TRUE, FALSE, FALSE);
                    RowNo += 1;
                    EnterCell(RowNo, 1, FORMAT(SalesInvHeaderFilter), TRUE, FALSE, FALSE);
                    RowNo += 1;
                    EnterCell(RowNo, 1, 'No.', TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 2, 'Posting Date', TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 3, 'Bill-to Customer No.', TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 4, 'Bill-to Name', TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 5, 'Source Code', TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 6, 'User ID', TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 7, 'Amount Including VAT', TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 8, 'Applies-to Doc. No.', TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 9, 'External Document No.', TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 10, 'Pos Transaction Type', TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 11, 'Pos Bank Name', TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 12, 'Store App POS Bank Name', TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 13, 'Pos Card Amount', TRUE, FALSE, FALSE);
                    EnterCell(RowNo, 14, 'Pos Cash Amount', TRUE, FALSE, FALSE);

                END;


            end;
        }
    }


    requestpage
    {

        layout
        {
            area(content)
            {
                group(Options)
                {
                    Caption = 'Report As At';
                    field(PrintExcel; PrintExcel)
                    {
                        ApplicationArea = Basic, Suite;
                        Caption = 'Print Excel';
                    }
                }
            }
        }

        actions
        {
        }
    }


    labels
    {
    }

    trigger OnPreReport()
    begin
        SalesInvHeaderFilter := "Sales Invoice Header".GetFilters;
    end;

    trigger OnPostReport()
    begin
        IF PrintExcel THEN BEGIN
            TempExcelBuffer.CreateNewBook('Sales Invoice Nos.');
            TempExcelBuffer.WriteSheet('Sales Invoice Nos.', CompanyName(), UserId());
            TempExcelBuffer.CloseBook();
            TempExcelBuffer.OpenExcel();
        END
    end;

    var
        RowNo: Integer;
        PrintExcel: Boolean;
        TempExcelBuffer: Record "Excel Buffer" temporary;
        Text000: Label 'No number series has been used for the following entries:';
        Text001: Label 'The number series %1 %2 has been used for the following entries:';
        Text002: Label 'There is a gap in the number series.';
        Text003: Label 'The documents are not listed according to Posting Date because they were not entered in that order.';
        Text004: Label 'Posted Sales Invoice: %1';
        NoSeries: Record "No. Series";
        SourceCode: Record "Source Code";
        SalesInvHeaderFilter: Text;
        LastNo: Code[20];
        LastPostingDate: Date;
        LastNoSeriesCode: Code[20];
        FirstRecord: Boolean;
        NewPage: Boolean;
        ErrorText: array[10] of Text[250];
        ErrorCounter: Integer;
        PageGroupNo: Integer;
        Sales_Invoice_Nos_CaptionLbl: Label 'Sales Invoice Nos.';
        CurrReport_PAGENOCaptionLbl: Label 'Page';
        SourceCode_DescriptionCaptionLbl: Label 'Source Description';
        SalesInvHeader__Posting_Date_CaptionLbl: Label 'Posting Date';
        ErrorText_Number__Control15CaptionLbl: Label 'Warning!';
        DocumentNo: Code[20];
        // forStoreapp: Boolean;
        isClearwox: Boolean;
        PosPayment: record POSPaymentLog;


    local procedure AddError(Text: Text[250])
    begin
        ErrorCounter := ErrorCounter + 1;
        ErrorText[ErrorCounter] := Text;
    end;

    procedure EnterCell(RowNo: Integer; ColumnNo: Integer; CellValue: Text[250]; Bold: Boolean; Italic: Boolean; Underline: Boolean);
    begin
        TempExcelBuffer.INIT;
        TempExcelBuffer.VALIDATE("Row No.", RowNo);
        TempExcelBuffer.VALIDATE("Column No.", ColumnNo);
        TempExcelBuffer."Cell Value as Text" := CellValue;
        TempExcelBuffer.Formula := '';
        TempExcelBuffer.Bold := Bold;
        TempExcelBuffer.Italic := Italic;
        TempExcelBuffer.Underline := Underline;
        TempExcelBuffer.INSERT;
    end;
}
