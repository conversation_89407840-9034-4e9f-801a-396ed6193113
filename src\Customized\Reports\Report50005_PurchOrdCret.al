report 50005 "Purchase Order Creation"
{
    ProcessingOnly = true;
    UseRequestPage = false;
    ApplicationArea = all;
    UsageCategory = Lists;
    Caption = 'Purchase Order Creation_50005';

    dataset
    {
        dataitem("Quotation Comparison1";
        "Quotation Comparison")
        {
            DataItemTableView = SORTING("Line No.")
                                WHERE("Carry Out Action" = FILTER(true),
                                      Level = FILTER(1));


            trigger OnPreDataItem();
            begin
                SetRange("RFQ No.", RFQNum);
            end;

            trigger OnAfterGetRecord();
            var

                PurchaseHeaderOrder: Record "Purchase Header";
                PurchaseLineOrder: Record "Purchase Line";
                PPSetup: Record "Purchases & Payables Setup";
                PurchaseHeader: Record "Purchase Header";
                PurchaseLine: Record "Purchase Line";
                PurchaseLineOrder2: Record "Purchase Line";
                VendorL: Record Vendor;
                NoSeriesMgt: Codeunit NoSeriesManagement;
                LineNo: Integer;
                FixeAss: Record "Fixed Asset";
                FixedAssBool: Boolean;
                FixedAset: Record "Fixed Asset";
                FxdAset: Record "Fixed Asset";
                LneLVar: Integer;
                GLAcc: Record "G/L Account";
                GLAccBool: Boolean;
                FADepBook: Record "FA Depreciation Book";
            begin
                FixeAss.reset;
                FixeAss.SetRange("No.", "Item No.");
                IF FixeAss.findfirst then
                    FixedAssBool := true
                else
                    FixedAssBool := false;



                /*GLAcc.RESET;
                GLAcc.SetRange("No.", "Item No.");
                IF GLAcc.findfirst then
                    GLAccBool := True
                else
                    GLAccBool := false;*/


                clear(OrderNo);
                PurchaseHeaderOrder.RESET;
                PurchaseHeaderOrder.SETRANGE("Quotation No.", "Quotation Comparison1"."Parent Quote No.");
                PurchaseHeaderOrder.SETRANGE("Document Type", PurchaseHeaderOrder."Document Type"::order);
                IF PurchaseHeaderOrder.FindFirst() THEN BEGIN
                    PurchaseHeader.RESET;
                    PurchaseHeader.SETRANGE("Document Type", PurchaseHeader."Document Type"::Quote);
                    PurchaseHeader.SETRANGE("No.", "Parent Quote No.");
                    IF PurchaseHeader.FindFirst() THEN BEGIN
                        PurchaseLine.RESET;
                        PurchaseLine.SETRANGE("Document Type", PurchaseLine."Document Type"::Order);
                        PurchaseLine.SETRANGE("Document No.", PurchaseHeaderOrder."No.");
                        // PurchaseLine.SETRANGE(Type, PurchaseLine.Type::Item);
                        //PurchaseLine.SETRANGE("No.", "Item No.");
                        IF PurchaseLine.FINDLAST() THEN
                            LineNo := PurchaseLine."Line No." + 10000
                        ELSE
                            LineNo := 10000;
                        CLEAR(PurchaseLine);
                        PurchaseLine.RESET;
                        PurchaseLine.SETRANGE("Document Type", PurchaseLine."Document Type"::Quote);
                        PurchaseLine.SETRANGE("Document No.", "Parent Quote No.");
                        //PurchaseLine.SETRANGE(Type, PurchaseLine.Type::Item);
                        PurchaseLine.SETRANGE("No.", "Item No.");
                        PurchaseLine.SETRANGE("Line No.", "Purch. Req Line No");
                        IF PurchaseLine.FINDSET() THEN
                            REPEAT
                                //Multiple Lines 
                                IF (FixedAssBool and (PurchaseHeader."Purchase Type" = PurchaseHeader."Purchase Type"::Local)) AND (FixeAss."MRS No." <> '') then begin

                                    FxdAset.reset;
                                    FxdAset.SetRange("No.", PurchaseLine."No.");
                                    if FxdAset.findfirst then;

                                    FixedAset.reset;
                                    FixedAset.SetRange("MRS No.", FxdAset."MRS No.");
                                    FixedAset.SetRange("MRS Line No.", FxdAset."MRS Line No.");
                                    if FixedAset.findset then
                                        repeat
                                            //MESSAGE('%1...1..%2', "Item No.", "Line No.");//TEST
                                            PurchaseLineOrder.INIT();
                                            PurchaseLineOrder.RESET();
                                            PurchaseLineOrder."Document Type" := PurchaseHeaderOrder."Document Type";
                                            PurchaseLineOrder."Document No." := PurchaseHeaderOrder."No.";
                                            OrderNo := PurchaseHeaderOrder."No.";

                                            PurchaseLineOrder."Line No." := LineNo;
                                            PurchaseLineOrder."Buy-from Vendor No." := PurchaseHeaderOrder."Buy-from Vendor No.";
                                            PurchaseLineOrder.VALIDATE("Buy-from Vendor No.");
                                            /*IF FixedAssBool then
                                                PurchaseLineOrder.Type := PurchaseLineOrder.Type::"Fixed Asset"
                                            else
                                                PurchaseLineOrder.Type := PurchaseLineOrder.Type::Item;*/
                                            PurchaseLineOrder.Type := PurchaseLine.type;
                                            if itemgv.get("Item No.") then
                                                invposset.Get("Quotation Comparison1"."Location Code", itemgv."Inventory Posting Group");                             //pkonj7
                                            PurchaseLineOrder.VALIDATE("No.", "Item No.");
                                            PurchaseLineOrder."Description 2" := "Quotation Comparison1".Description2;
                                            //PurchaseLineOrder.VALIDATE(Quantity, "Quotation Comparison1".Quantity);
                                            PurchaseLineOrder.VALIDATE(Quantity, 1);
                                            PurchaseLineOrder."Direct Unit Cost" := "Quotation Comparison1".Rate;
                                            // PurchaseLineOrder.VALIDATE("Direct Unit Cost");//Moved after Capex due to validation  //G2S 280525 9505_CAS-01423-B0Z9D8
                                            PurchaseLineOrder."Sub Document Type" := PurchaseLineOrder."Sub Document Type"::"Purchase Req.";
                                            PurchaseLineOrder."Sub Document No." := "Quotation Comparison1"."Purc. Req No";
                                            PurchaseLineOrder."Sub Document Line No." := "Quotation Comparison1"."Purch. Req Line No";
                                            PurchaseLineOrder."Material req No.s" := "Quotation Comparison1"."Material req No.s";
                                            PurchaseLineOrder.VALIDATE("Variant Code", "Quotation Comparison1"."Variant Code");
                                            PurchaseLineOrder."Location Code" := "Quotation Comparison1"."Location Code";
                                            //PurchaseLineOrder.Quantity := "Quotation Comparison1".Quantity;
                                            PurchaseLineOrder.Quantity := 1;
                                            PurchaseLineOrder."Direct Unit Cost" := "Quotation Comparison1".Rate;
                                            PurchaseLineOrder.VALIDATE("Direct Unit Cost");
                                            PurchaseLineOrder.Amount := ("Quotation Comparison1"."Total Amount" / FixedAset.Count);
                                            PurchaseLineOrder."Capex No." := "Quotation Comparison1"."Capex No.";
                                            PurchaseLineOrder."Capex Line No." := "Quotation Comparison1"."Capex Line No.";
                                            PurchaseLineOrder.VALIDATE("Direct Unit Cost"); //G2S 280525 9505_CAS-01423-B0Z9D8
                                            //PhaniFeb112021 >>
                                            PurchaseLineOrder."CWIP No." := "Quotation Comparison1"."CWIP No.";
                                            //PhaniFeb112021 <<                                            
                                            //PhaniFeb102021 >>
                                            PurchaseLineOrder."VAT Bus. Posting Group" := "Quotation Comparison1"."VAT Bus. Posting Group";
                                            PurchaseLineOrder."VAT Prod. Posting Group" := "Quotation Comparison1"."VAT Prod. Posting Group";
                                            //PhaniFeb102021 <<
                                            PurchaseLineOrder."Budget Name" := "Quotation Comparison1"."Budget Name";
                                            //PurchaseLineOrder."FA Posting Group" := "Quotation Comparison1"."FA Posting Group";
                                            PurchaseLineOrder."Shortcut Dimension 1 Code" := "Quotation Comparison1"."Shortcut Dimension 1 Code";
                                            PurchaseLineOrder."Shortcut Dimension 2 Code" := "Quotation Comparison1"."Shortcut Dimension 2 Code";
                                            PurchaseLineOrder."Dimension Set ID" := "Quotation Comparison1"."Dimension Set ID";
                                            PurchaseLineOrder.INSERT();
                                            //FIX23Jun2021>>
                                            PurchaseLineOrder."Capex No." := "Quotation Comparison1"."Capex No.";
                                            PurchaseLineOrder."Capex Line No." := "Quotation Comparison1"."Capex Line No.";
                                            //PurchaseLineOrder."Posting Group" := "Quotation Comparison1"."FA Posting Group";
                                            PurchaseLineOrder."CWIP No." := "Quotation Comparison1"."CWIP No.";
                                            //Fix12Jul2021>>
                                            if PurchaseLineOrder.Type = PurchaseLineOrder.Type::"Fixed Asset" then begin
                                                PurchaseLineOrder."Posting Group" := "Quotation Comparison1"."FA Posting Group";
                                                FADepBook.Reset();
                                                FADepBook.SetRange("FA No.", PurchaseLineOrder."No.");
                                                if FADepBook.FindFirst() then
                                                    PurchaseLineOrder."Depreciation Book Code" := FADepBook."Depreciation Book Code"
                                                else
                                                    Error('Assign Depreciation Book For Fixed asset %1', PurchaseLineOrder."No.");
                                            end;
                                            //Fix12Jul2021<<
                                            //FIX23Jun2021<<
                                            PurchaseLineOrder."Service Code" := "Quotation Comparison1"."Service Code";//Service08Jul2021
                                            PurchaseLineOrder.Validate("Shortcut Dimension 1 Code");
                                            PurchaseLineOrder.validate("Shortcut Dimension 2 Code");
                                            PurchaseLineOrder.Validate("Dimension Set ID");
                                            PurchaseLineOrder.Modify();
                                            LineNo += 10000;
                                        until FixedAset.next = 0;
                                END ELSE begin
                                    //MESSAGE('%1...%2..2', "Item No.", "Line No.");//TEST
                                    ////another code >>
                                    PurchaseLineOrder.INIT();
                                    //PurchaseLineOrder.RESET();
                                    PurchaseLineOrder."Document Type" := PurchaseHeaderOrder."Document Type";
                                    PurchaseLineOrder."Document No." := PurchaseHeaderOrder."No.";
                                    OrderNo := PurchaseHeaderOrder."No.";

                                    PurchaseLineOrder."Line No." := LineNo;
                                    PurchaseLineOrder."Buy-from Vendor No." := PurchaseHeaderOrder."Buy-from Vendor No.";
                                    PurchaseLineOrder.VALIDATE("Buy-from Vendor No.");
                                    /*
                                    IF FixedAssBool then
                                        PurchaseLineOrder.Type := PurchaseLineOrder.Type::"Fixed Asset"
                                    else
                                        PurchaseLineOrder.Type := PurchaseLineOrder.Type::Item;
                                        */
                                    PurchaseLineOrder.Type := PurchaseLine.type;
                                    if itemgv.get("Item No.") then
                                        invposset.Get("Quotation Comparison1"."Location Code", itemgv."Inventory Posting Group");                             //pkonj7
                                    PurchaseLineOrder.VALIDATE("No.", "Item No.");
                                    PurchaseLineOrder."Description 2" := "Quotation Comparison1".Description2;
                                    PurchaseLineOrder.VALIDATE(Quantity, "Quotation Comparison1".Quantity);
                                    PurchaseLineOrder."Direct Unit Cost" := "Quotation Comparison1".Rate;
                                    PurchaseLineOrder.VALIDATE("Direct Unit Cost");
                                    PurchaseLineOrder."Sub Document Type" := PurchaseLineOrder."Sub Document Type"::"Purchase Req.";
                                    PurchaseLineOrder."Sub Document No." := "Quotation Comparison1"."Purc. Req No";
                                    PurchaseLineOrder."Material req No.s" := "Quotation Comparison1"."Material req No.s";
                                    PurchaseLineOrder."Sub Document Line No." := "Quotation Comparison1"."Purch. Req Line No";
                                    PurchaseLineOrder.VALIDATE("Variant Code", "Quotation Comparison1"."Variant Code");
                                    PurchaseLineOrder."Location Code" := "Quotation Comparison1"."Location Code";
                                    PurchaseLineOrder.Quantity := "Quotation Comparison1".Quantity;
                                    PurchaseLineOrder."Direct Unit Cost" := "Quotation Comparison1".Rate;
                                    PurchaseLineOrder.VALIDATE("Direct Unit Cost");
                                    PurchaseLineOrder.Amount := "Quotation Comparison1"."Total Amount";
                                    PurchaseLineOrder."Capex No." := "Quotation Comparison1"."Capex No.";
                                    PurchaseLineOrder."Capex Line No." := "Quotation Comparison1"."Capex Line No.";
                                    PurchaseLineOrder."CWIP No." := "Quotation Comparison1"."CWIP No.";
                                    PurchaseLineOrder."Budget Name" := "Quotation Comparison1"."Budget Name";
                                    PurchaseLineOrder."FA Posting Group" := "Quotation Comparison1"."FA Posting Group";
                                    PurchaseLineOrder."Shortcut Dimension 1 Code" := "Quotation Comparison1"."Shortcut Dimension 1 Code";
                                    PurchaseLineOrder."Shortcut Dimension 2 Code" := "Quotation Comparison1"."Shortcut Dimension 2 Code";
                                    PurchaseLineOrder."Dimension Set ID" := "Quotation Comparison1"."Dimension Set ID";
                                    PurchaseLineOrder.INSERT();
                                    //FIX23Jun2021>>
                                    PurchaseLineOrder."Capex No." := "Quotation Comparison1"."Capex No.";
                                    PurchaseLineOrder."Capex Line No." := "Quotation Comparison1"."Capex Line No.";
                                    //PurchaseLineOrder."Posting Group" := "Quotation Comparison1"."FA Posting Group";//PKONJU9
                                    PurchaseLineOrder."CWIP No." := "Quotation Comparison1"."CWIP No.";
                                    //FIX23Jun2021<<
                                    //Fix12Jul2021>>
                                    if PurchaseLineOrder.Type = PurchaseLineOrder.Type::"Fixed Asset" then begin
                                        PurchaseLineOrder."Posting Group" := "Quotation Comparison1"."FA Posting Group";
                                        FADepBook.Reset();
                                        FADepBook.SetRange("FA No.", PurchaseLineOrder."No.");
                                        if FADepBook.FindFirst() then
                                            PurchaseLineOrder."Depreciation Book Code" := FADepBook."Depreciation Book Code"
                                        else
                                            Error('Assign Depreciation Book For Fixed asset %1', PurchaseLineOrder."No.");
                                    end;
                                    //Fix12Jul2021<<
                                    PurchaseLineOrder."Service Code" := "Quotation Comparison1"."Service Code";//Service08Jul2021
                                    PurchaseLineOrder.Validate("Shortcut Dimension 1 Code");
                                    PurchaseLineOrder.validate("Shortcut Dimension 2 Code");
                                    PurchaseLineOrder.Validate("Dimension Set ID");
                                    PurchaseLineOrder.Modify();
                                end;
                                LineNo += 10000;
                            ////another code
                            UNTIL PurchaseLine.NEXT() = 0;
                        //message('enter 1');//TEST
                    END;
                END ELSE BEGIN
                    PurchaseHeader.RESET;
                    PurchaseHeader.SETRANGE("Document Type", PurchaseHeader."Document Type"::Quote);
                    PurchaseHeader.SETRANGE("No.", "Parent Quote No.");
                    IF PurchaseHeader.FindFirst() THEN BEGIN
                        PurchaseHeaderOrder."Document Type" := PurchaseHeaderOrder."Document Type"::Order;
                        PPSetup.GET();
                        VendorL.GET(PurchaseHeader."Buy-from Vendor No.");
                        IF VendorL."Vendor Type" = VendorL."Vendor Type"::Local then begin
                            PPSetup.TestField("Local Purchase Order");
                            PurchaseHeaderOrder."No." := NoSeriesMgt.GetNextNo(PPSetup."Local Purchase Order", WORKDATE(), TRUE);
                        END else
                            IF VendorL."Vendor Type" = VendorL."Vendor Type" then BEGIN
                                PPSetup.TestField("Import Purchase Order");
                                PurchaseHeaderOrder."No." := NoSeriesMgt.GetNextNo(PPSetup."Import Purchase Order", WORKDATE(), TRUE);
                            END;
                        IF PurchaseLineOrder2.GET(PurchaseHeaderOrder."Document Type"::Order, PurchaseHeaderOrder."No.") THEN
                            ERROR('Record already Existed.');
                        PurchaseHeaderOrder."Purchase Type" := VendorL."Vendor Type";//B2B FIX 19Apr2021
                        PurchaseHeaderOrder."Posting Date" := WORKDATE();
                        PurchaseHeaderOrder."Document Date" := WORKDATE();
                        PurchaseHeaderOrder.Validate("Order Date", WorkDate());
                        PurchaseHeaderOrder.VALIDATE("Buy-from Vendor No.", PurchaseHeader."Buy-from Vendor No.");
                        PurchaseHeaderOrder."Quotation No." := "Parent Quote No.";
                        PurchaseHeaderOrder."Quote No." := "Parent Quote No.";//Pk-Balu
                        PurchaseHeaderOrder."Expected Receipt Date" := "Quotation Comparison1"."Due Date";
                        PurchaseHeaderOrder."Purch Req. Ref. No." := PurchaseHeader."Purch Req. Ref. No.";
                        PurchaseHeaderOrder."Created Date" := CurrentDateTime;  //PKON22M25
                        PurchaseHeaderOrder."Created By" := USERID; //PKON22M25
                        PurchaseHeaderOrder.INSERT();
                        PurchaseLine.Reset();
                        PurchaseLine.SETRANGE("Document Type", PurchaseLine."Document Type"::Quote);
                        PurchaseLine.SETRANGE("Document No.", "Parent Quote No.");
                        //PurchaseLine.SETRANGE(Type, PurchaseLine.Type::Item);
                        PurchaseLine.SETRANGE("No.", "Item No.");

                        IF PurchaseLine.FindFirst() THEN BEGIN
                            //Multiple Lines 
                            IF (FixedAssBool and (PurchaseHeader."Purchase Type" = PurchaseHeader."Purchase Type"::Local)) AND (FixeAss."MRS No." <> '') then begin
                                clear(LneLVar);
                                LneLVar := PurchaseLine."Line No." + 10000;
                                FxdAset.reset;
                                FxdAset.SetRange("No.", PurchaseLine."No.");
                                if FxdAset.findfirst then;

                                FixedAset.reset;
                                FixedAset.SetRange("MRS No.", FxdAset."MRS No.");
                                FixedAset.SetRange("MRS Line No.", FxdAset."MRS Line No.");
                                if FixedAset.findset then
                                    repeat
                                        //MESSAGE('%1...3..%3', "Item No.", "Line No.");//TEST
                                        PurchaseLineOrder.INIT();
                                        PurchaseLineOrder.RESET();
                                        PurchaseLineOrder."Document Type" := PurchaseHeaderOrder."Document Type";
                                        PurchaseLineOrder."Document No." := PurchaseHeaderOrder."No.";

                                        PurchaseLineOrder."Line No." := LneLVar;
                                        PurchaseLineOrder."Buy-from Vendor No." := PurchaseHeaderOrder."Buy-from Vendor No.";
                                        PurchaseLineOrder.VALIDATE("Buy-from Vendor No.");

                                        /*IF FixedAssBool then
                                            PurchaseLineOrder.Type := PurchaseLineOrder.Type::"Fixed Asset"
                                        else
                                            PurchaseLineOrder.Type := PurchaseLineOrder.Type::Item;*/
                                        PurchaseLineOrder.Type := PurchaseLine.type;
                                        //PurchaseLineOrder.VALIDATE("No.", "Item No.");
                                        PurchaseLineOrder.VALIDATE("No.", FixedAset."No.");//B2B-PK-Ravi
                                        PurchaseLineOrder."Description 2" := "Quotation Comparison1".Description2;
                                        //PurchaseLineOrder.VALIDATE(Quantity, "Quotation Comparison1".Quantity);//TESTJan27
                                        PurchaseLineOrder.VALIDATE(Quantity, 1);
                                        PurchaseLineOrder."Direct Unit Cost" := "Quotation Comparison1".Rate;
                                        // PurchaseLineOrder.VALIDATE("Direct Unit Cost");//Moved after Capex due to validation  //G2S 280525 9505_CAS-01423-B0Z9D8
                                        PurchaseLineOrder."Sub Document Type" := PurchaseLineOrder."Sub Document Type"::"Purchase Req.";
                                        PurchaseLineOrder."Sub Document No." := "Quotation Comparison1"."Purc. Req No";
                                        PurchaseLineOrder."Material req No.s" := "Quotation Comparison1"."Material req No.s";
                                        PurchaseLineOrder."Sub Document Line No." := "Quotation Comparison1"."Purch. Req Line No";
                                        PurchaseLineOrder.VALIDATE("Variant Code", "Quotation Comparison1"."Variant Code");
                                        PurchaseLineOrder."Location Code" := "Quotation Comparison1"."Location Code";
                                        OrderNo := PurchaseLineOrder."Document No.";
                                        PurchaseLineOrder."Capex No." := "Quotation Comparison1"."Capex No.";
                                        PurchaseLineOrder."Capex Line No." := "Quotation Comparison1"."Capex Line No.";
                                        PurchaseLineOrder.VALIDATE("Direct Unit Cost");  //G2S 280525 9505_CAS-01423-B0Z9D8
                                        PurchaseLineOrder."CWIP No." := "Quotation Comparison1"."CWIP No.";
                                        PurchaseLineOrder."Budget Name" := "Quotation Comparison1"."Budget Name";
                                        PurchaseLineOrder."FA Posting Group" := "Quotation Comparison1"."FA Posting Group";
                                        PurchaseLineOrder."Shortcut Dimension 1 Code" := "Quotation Comparison1"."Shortcut Dimension 1 Code";
                                        PurchaseLineOrder."Shortcut Dimension 2 Code" := "Quotation Comparison1"."Shortcut Dimension 2 Code";
                                        PurchaseLineOrder."Dimension Set ID" := "Quotation Comparison1"."Dimension Set ID";
                                        PurchaseLineOrder."Shortcut Dimension 1 Code" := "Quotation Comparison1"."Shortcut Dimension 1 Code";
                                        PurchaseLineOrder."Shortcut Dimension 2 Code" := "Quotation Comparison1"."Shortcut Dimension 2 Code";
                                        PurchaseLineOrder."Dimension Set ID" := "Quotation Comparison1"."Dimension Set ID";
                                        PurchaseLineOrder.INSERT();
                                        PurchaseLineOrder."Service Code" := "Quotation Comparison1"."Service Code";//Service08Jul2021
                                        PurchaseLineOrder.Validate("Shortcut Dimension 1 Code");
                                        PurchaseLineOrder.validate("Shortcut Dimension 2 Code");
                                        PurchaseLineOrder.Validate("Dimension Set ID");
                                        //Fix12Jul2021>>
                                        if PurchaseLineOrder.Type = PurchaseLineOrder.Type::"Fixed Asset" then begin
                                            PurchaseLineOrder."Posting Group" := "Quotation Comparison1"."FA Posting Group";
                                            FADepBook.Reset();
                                            FADepBook.SetRange("FA No.", PurchaseLineOrder."No.");
                                            if FADepBook.FindFirst() then
                                                PurchaseLineOrder."Depreciation Book Code" := FADepBook."Depreciation Book Code"
                                            else
                                                Error('Assign Depreciation Book For Fixed asset %1', PurchaseLineOrder."No.");
                                        end;
                                        //Fix12Jul2021<<
                                        PurchaseLineOrder.Modify();
                                        LneLVar += 10000;
                                    until FixedAset.next = 0;
                            END else begin
                                //MESSAGE('%1...%2..%3 4', "Item No.", Quantity, "Line No.");//TEST
                                PurchaseLineOrder.INIT();
                                //PurchaseLineOrder.RESET();
                                PurchaseLineOrder."Document Type" := PurchaseHeaderOrder."Document Type";
                                PurchaseLineOrder."Document No." := PurchaseHeaderOrder."No.";
                                PurchaseLineOrder."Line No." := PurchaseLine."Line No." + 10000;
                                PurchaseLineOrder."Buy-from Vendor No." := PurchaseHeaderOrder."Buy-from Vendor No.";
                                PurchaseLineOrder.VALIDATE("Buy-from Vendor No.");

                                /*IF FixedAssBool then
                                    PurchaseLineOrder.Type := PurchaseLineOrder.Type::"Fixed Asset"
                                else
                                    PurchaseLineOrder.Type := PurchaseLineOrder.Type::Item;*/
                                PurchaseLineOrder.Type := PurchaseLine.type;
                                if itemgv.get("Item No.") then
                                    invposset.Get("Quotation Comparison1"."Location Code", itemgv."Inventory Posting Group");
                                PurchaseLineOrder.VALIDATE("No.", "Item No.");
                                PurchaseLineOrder."Description 2" := "Quotation Comparison1".Description2;
                                PurchaseLineOrder.VALIDATE(Quantity, "Quotation Comparison1".Quantity);
                                PurchaseLineOrder."Direct Unit Cost" := "Quotation Comparison1".Rate;
                                // PurchaseLineOrder.VALIDATE("Direct Unit Cost");//Moved after Capex due to validation
                                PurchaseLineOrder."Sub Document Type" := PurchaseLineOrder."Sub Document Type"::"Purchase Req.";
                                PurchaseLineOrder."Sub Document No." := "Quotation Comparison1"."Purc. Req No";
                                PurchaseLineOrder."Material req No.s" := "Quotation Comparison1"."Material req No.s";
                                PurchaseLineOrder."Sub Document Line No." := "Quotation Comparison1"."Purch. Req Line No";
                                PurchaseLineOrder.VALIDATE("Variant Code", "Quotation Comparison1"."Variant Code");
                                PurchaseLineOrder."Location Code" := "Quotation Comparison1"."Location Code";
                                OrderNo := PurchaseLineOrder."Document No.";
                                PurchaseLineOrder."Capex No." := "Quotation Comparison1"."Capex No.";
                                PurchaseLineOrder."Capex Line No." := "Quotation Comparison1"."Capex Line No.";
                                PurchaseLineOrder.VALIDATE("Direct Unit Cost");  //G2S 280525 9505_CAS-01423-B0Z9D8
                                //PhaniFeb112021 >>
                                PurchaseLineOrder."CWIP No." := "Quotation Comparison1"."CWIP No.";
                                //PhaniFeb112021 <<                                
                                PurchaseLineOrder."Budget Name" := "Quotation Comparison1"."Budget Name";
                                PurchaseLineOrder."FA Posting Group" := "Quotation Comparison1"."FA Posting Group";
                                PurchaseLineOrder."Shortcut Dimension 1 Code" := "Quotation Comparison1"."Shortcut Dimension 1 Code";
                                PurchaseLineOrder."Shortcut Dimension 2 Code" := "Quotation Comparison1"."Shortcut Dimension 2 Code";
                                PurchaseLineOrder."Dimension Set ID" := "Quotation Comparison1"."Dimension Set ID";
                                PurchaseLineOrder.INSERT();
                                PurchaseLineOrder."Service Code" := "Quotation Comparison1"."Service Code";//Service08Jul2021
                                PurchaseLineOrder.Validate("Shortcut Dimension 1 Code");
                                PurchaseLineOrder.validate("Shortcut Dimension 2 Code");
                                PurchaseLineOrder.Validate("Dimension Set ID");
                                //Fix12Jul2021>>
                                if PurchaseLineOrder.Type = PurchaseLineOrder.Type::"Fixed Asset" then begin
                                    PurchaseLineOrder."Posting Group" := "Quotation Comparison1"."FA Posting Group";
                                    FADepBook.Reset();
                                    FADepBook.SetRange("FA No.", PurchaseLineOrder."No.");
                                    if FADepBook.FindFirst() then
                                        PurchaseLineOrder."Depreciation Book Code" := FADepBook."Depreciation Book Code"
                                    else
                                        Error('Assign Depreciation Book For Fixed asset %1', PurchaseLineOrder."No.");
                                end;
                                //Fix12Jul2021<<
                                PurchaseLineOrder.Modify();
                            end;
                        end;

                    end;
                END;
                QutComLine.RESET;
                QutComLine.SetRange("Quot Comp No.", "Quot Comp No.");
                QutComLine.SetRange("Line No.", "Line No.");
                IF QutComLine.findfirst THEN BEGIN
                    QutComLine."Po No." := OrderNo;
                    QutComLine.modify;
                END;
                message('%1', OrderNo);

            END;

            trigger OnPostDataItem();
            var
                RFQNumbers: Record "RFQ Numbers";
            begin

                //MESSAGE('Order %1 Created', OrderNo);
                //error('stop');
                RFQNumbers.RESET;
                RFQNumbers.SETRANGE("RFQ No.", RFQNum);
                IF RFQNumbers.FIND('-') THEN BEGIN
                    RFQNumbers.Completed := TRUE;
                    RFQNumbers.MODIFY();
                END;
            end;
        }
    }

    requestpage
    {

        layout
        {
        }

        actions
        {
        }
    }

    labels
    {
    }

    var
        Noseries2: Code[20];
        RFQNum: Code[20];
        OrderNo: Code[20];
        QutComLine: Record "Quotation Comparison";
        invposset: Record "Inventory Posting Setup";
        itemgv: Record Item;


    procedure GetValues(RFQNUmbr: code[20]);
    begin
        RFQNum := RFQNUmbr;
    end;
}

