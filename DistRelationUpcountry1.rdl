﻿<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2016/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="DataSource">
      <ConnectionProperties>
        <DataProvider>SQL</DataProvider>
        <ConnectString />
      </ConnectionProperties>
      <rd:SecurityType>None</rd:SecurityType>
      <rd:DataSourceID>44c10963-d03b-4f39-afac-72de93751f39</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <ReportSections>
    <ReportSection>
      <Body>
        <ReportItems>
          <Tablix Name="Tablix1">
            <TablixBody>
              <TablixColumns>
                <TablixColumn>
                  <Width>18.76751cm</Width>
                </TablixColumn>
              </TablixColumns>
              <TablixRows>
                <TablixRow>
                  <Height>37.18028cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Rectangle Name="Rectangle2">
                          <ReportItems>
                            <Textbox Name="Textbox39">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>The Managing Director / Chief Finance Office</Value>
                                      <Style>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox39</rd:DefaultName>
                              <Top>0cm</Top>
                              <Left>0.0335cm</Left>
                              <Height>11pt</Height>
                              <Width>8.66336cm</Width>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="Textbox40">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!Name.Value</Value>
                                      <Style />
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox40</rd:DefaultName>
                              <Top>0.42334cm</Top>
                              <Left>0.0335cm</Left>
                              <Height>11pt</Height>
                              <Width>8.66336cm</Width>
                              <ZIndex>1</ZIndex>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="Textbox42">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!Address.Value</Value>
                                      <Style />
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox40</rd:DefaultName>
                              <Top>0.84667cm</Top>
                              <Left>0.0335cm</Left>
                              <Height>11pt</Height>
                              <Width>8.66336cm</Width>
                              <ZIndex>2</ZIndex>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="Textbox43">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!Address_2.Value</Value>
                                      <Style />
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox40</rd:DefaultName>
                              <Top>1.27cm</Top>
                              <Left>0.0335cm</Left>
                              <Height>11pt</Height>
                              <Width>8.66336cm</Width>
                              <ZIndex>3</ZIndex>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="Textbox45">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!AttentionCaptionLbl.Value+" : "+Fields!Contact.Value</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                        <TextDecoration>Underline</TextDecoration>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox45</rd:DefaultName>
                              <Top>2.19604cm</Top>
                              <Left>0.0335cm</Left>
                              <Height>11pt</Height>
                              <Width>4.37711cm</Width>
                              <ZIndex>4</ZIndex>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="Textbox48">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value xml:space="preserve"> </Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                        <TextDecoration>None</TextDecoration>
                                      </Style>
                                    </TextRun>
                                    <TextRun>
                                      <Value>CHI LIMITED'S RELATIONSHIP WITH ITS DISTRIBUTORS: RESTATEMENT OF POLICIES</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                        <TextDecoration>Underline</TextDecoration>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox45</rd:DefaultName>
                              <Top>2.68993cm</Top>
                              <Left>1.96904cm</Left>
                              <Height>11pt</Height>
                              <Width>16.76484cm</Width>
                              <ZIndex>5</ZIndex>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="Textbox49">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!DearSirCaption.Value</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Normal</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox40</rd:DefaultName>
                              <Top>3.43865cm</Top>
                              <Left>0.0335cm</Left>
                              <Height>11pt</Height>
                              <Width>4.37711cm</Width>
                              <ZIndex>6</ZIndex>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="Textbox50">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!SubjectCaption.Value</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Normal</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox40</rd:DefaultName>
                              <Top>3.86198cm</Top>
                              <Left>0.0335cm</Left>
                              <Height>11pt</Height>
                              <Width>6.4144cm</Width>
                              <ZIndex>7</ZIndex>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="Textbox51">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>We wish to use this opportunity to reiterate certain policies governing the existing relationship between CHI Limited ("CHI" or the "Company") and its distributors. The policies are as follows:</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Normal</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox51</rd:DefaultName>
                              <Top>4.42643cm</Top>
                              <Left>0.0335cm</Left>
                              <Height>1.235cm</Height>
                              <Width>18.70039cm</Width>
                              <ZIndex>8</ZIndex>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="Textbox52">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>A.   NO CASH HANDLING POLICY:</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox40</rd:DefaultName>
                              <Top>5.69671cm</Top>
                              <Left>0.31575cm</Left>
                              <Height>11pt</Height>
                              <Width>7.36686cm</Width>
                              <ZIndex>9</ZIndex>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="Textbox53">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>CHI Limited prohibits distributors from giving cash to, or extending any credit facility to the Company's sales staff, its employees or its representatives. All transactions carried out by the sales staff on behalf of the distributors are to be carried out strictly in compliance with this policy. The Company will not bear responsibility for any loss suffered by a distributor in defiance of this "no cash handling" and "no credit sales" rules and all cash given to, or credit sales made to our sales representatives will be at the sole risk of such distributor.</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Normal</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <ListLevel>1</ListLevel>
                                  <Style />
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox53</rd:DefaultName>
                              <Top>6.12004cm</Top>
                              <Left>0.31575cm</Left>
                              <Height>2.16986cm</Height>
                              <Width>18.41818cm</Width>
                              <ZIndex>10</ZIndex>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="Textbox54">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>B.   POLICY ON SALES SUPPORT BY SALES REPRESENTATIVES:</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                        <TextDecoration>Underline</TextDecoration>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox40</rd:DefaultName>
                              <Top>8.2899cm</Top>
                              <Left>0.3157cm</Left>
                              <Height>11pt</Height>
                              <Width>14.69586cm</Width>
                              <ZIndex>11</ZIndex>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="Textbox55">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>CHI Limited from time to time offers to its distributors, sales representatives to support the distributors' business with the Company in order to aid the distributors to achieve their monthly sales and coverage targets under the Company's redistribution scheme. CHI Limited wishes to reiterate that the services provided by the sales representatives are merely support services, and while performing these services, the sales representatives remain for all purposes, the agents of the distributors.</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Normal</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <ListLevel>1</ListLevel>
                                  <Style />
                                </Paragraph>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value />
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Normal</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <ListLevel>1</ListLevel>
                                  <Style />
                                </Paragraph>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Therefore, the distributors reserve the right to reject, suspend or terminate the utilization of the support services provided by the sales representatives. Following from the foregoing, the distributors remain fully responsible for the ownership of the products, collection of proceeds of sales from their customers, collection of unsold stock from sales representatives, and reconciliation of accounts. The Company shall not be held responsible for any misappropriated cash or stock, or any loss suffered by a distributor as a result of the distributor's breach of this policy. </Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Normal</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <ListLevel>1</ListLevel>
                                  <Style />
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox55</rd:DefaultName>
                              <Top>8.71324cm</Top>
                              <Left>0.3157cm</Left>
                              <Height>4.53982cm</Height>
                              <Width>18.41818cm</Width>
                              <ZIndex>12</ZIndex>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="Textbox56">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>C.  POLICY ON DELIVERY OF PRODUCT:</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                        <TextDecoration>Underline</TextDecoration>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox40</rd:DefaultName>
                              <Top>13.42944cm</Top>
                              <Left>0.31571cm</Left>
                              <Height>11pt</Height>
                              <Width>6.46732cm</Width>
                              <ZIndex>13</ZIndex>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="Textbox57">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>i . </Value>
                                      <Style>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                    <TextRun>
                                      <Value>       After the distributor has paid for products and the Company has confirmed receipt of the payment thereof, an invoice </Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Normal</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>            shall be raised in respect of the products, and the products shall be delivered to the distributor. At the point of delivery, </Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Normal</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>            the distributor or his representative shall acknowledge receipt of the products and shall duly endorse the copy of the </Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Normal</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>            invoice as proof of delivery. Where the distributor or his representative fails or refuses to endorse the copy of the invoice </Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Normal</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>            as acknowledgement of receipt of the products, the products shall not be delivered to the distributor, but shall be </Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Normal</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>            returned to the Company's warehouse.</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Normal</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value xml:space="preserve"> </Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Normal</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>ii.      </Value>
                                      <Style>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                    <TextRun>
                                      <Value>Not with standing the contents of the paragraph above, all products paid for by the distributors shall be deemed </Value>
                                      <Style>
                                        <FontWeight>Normal</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>         duly delivered in full to the distributors as per the invoice, and the Company shall not entertain any complaints </Value>
                                      <Style>
                                        <FontWeight>Normal</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>         from any distributor that the products were not delivered in full or at all to such distributor, UNLESS the </Value>
                                      <Style>
                                        <FontWeight>Normal</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>         complaint is in writing and is received by the Director, Sales within seven (7) calendar days of payment for</Value>
                                      <Style>
                                        <FontWeight>Normal</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>         the products by the distributor. The contact details of the Director, Sales are as follows:</Value>
                                      <Style>
                                        <FontWeight>Normal</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value />
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Normal</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox55</rd:DefaultName>
                              <Top>13.8175cm</Top>
                              <Left>0.31574cm</Left>
                              <Height>5.0712cm</Height>
                              <Width>18.41818cm</Width>
                              <ZIndex>14</ZIndex>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="Textbox58">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>           Products sold and/or delivered to distributors are made to the Company's set standard of quality and as such, products </Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Normal</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>           represented by the distributor as defective shall not form the subject of any claim by the distributor or of any incidental or </Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Normal</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>           consequential loss, damage or liability howsoever arising indirectly from such defects; but such products, if returned to </Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Normal</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>           the Company within seven (7) calendar days of its delivery and accepted by the Company as defective, will at the request </Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Normal</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>           of the distributor and if practicable, be replaced as originally ordered; provided however that defects in quality of products </Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Normal</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>           in any delivery shall not be a ground for cancellation of the remainder or the order or contract.</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Normal</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox55</rd:DefaultName>
                              <Top>19.84147cm</Top>
                              <Left>0.3157cm</Left>
                              <Height>2.62187cm</Height>
                              <Width>18.41818cm</Width>
                              <ZIndex>15</ZIndex>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="Textbox59">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>D.   POLICY ON DEFECTIVE PRODUCTS:</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                        <TextDecoration>Underline</TextDecoration>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox40</rd:DefaultName>
                              <Top>19.41814cm</Top>
                              <Left>0.3157cm</Left>
                              <Height>11pt</Height>
                              <Width>7.63149cm</Width>
                              <ZIndex>16</ZIndex>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="Textbox60">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>E.   POLICY ON CLAIM FOR DAMAGE, SHORTAGE OR LOSS:</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                        <TextDecoration>Underline</TextDecoration>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox40</rd:DefaultName>
                              <Top>22.49862cm</Top>
                              <Left>0.31575cm</Left>
                              <Height>11pt</Height>
                              <Width>10.64774cm</Width>
                              <ZIndex>17</ZIndex>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="Textbox61">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>           No claim in respect of damage in transit, shortage of delivery, or loss of products will be entertained by the Company, </Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Normal</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>           unless, in the case of damage in transit or shortage of delivery, followed by a complete claim in writing within seven (7) </Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Normal</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>           calendar days of receipt of products and in the case of loss of the products, notice in writing is given and a complete </Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Normal</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>           claim in writing made within seven (7) days of the date of the consignment.</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Normal</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value />
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Normal</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>           Please be assured that we value your business and seek protection of your interest in furtherance of the mutually                          beneficial relationship.</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Normal</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value />
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Normal</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>           In the event that you require any clarification on any of these policies, or any issue related to your business with us, kindly </Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Normal</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>           contact on by email or telephone as follows:</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Normal</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox55</rd:DefaultName>
                              <Top>22.92196cm</Top>
                              <Left>0.3157cm</Left>
                              <Height>4.28875cm</Height>
                              <Width>18.41818cm</Width>
                              <ZIndex>18</ZIndex>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="Textbox10">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!EmailName.Value</Value>
                                      <Style>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox10</rd:DefaultName>
                              <Top>27.24598cm</Top>
                              <Left>0.31583cm</Left>
                              <Height>0.49416cm</Height>
                              <Width>7.36687cm</Width>
                              <ZIndex>19</ZIndex>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="Textbox28">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!PhoneNumber.Value</Value>
                                      <Style>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox10</rd:DefaultName>
                              <Top>27.77542cm</Top>
                              <Left>0.31579cm</Left>
                              <Height>0.49416cm</Height>
                              <Width>7.36687cm</Width>
                              <ZIndex>20</ZIndex>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="Textbox31">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>:<EMAIL></Value>
                                      <Style />
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox31</rd:DefaultName>
                              <Top>18.8887cm</Top>
                              <Left>1.4985cm</Left>
                              <Height>0.49417cm</Height>
                              <Width>7.47417cm</Width>
                              <ZIndex>21</ZIndex>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="Textbox12">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Yours faithfully,</Value>
                                      <Style />
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox12</rd:DefaultName>
                              <Top>28.42833cm</Top>
                              <Left>0.34937cm</Left>
                              <Height>0.6cm</Height>
                              <Width>6.46729cm</Width>
                              <ZIndex>22</ZIndex>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="Textbox14">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>For:</Value>
                                      <Style />
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox12</rd:DefaultName>
                              <Top>29.02833cm</Top>
                              <Left>0.34933cm</Left>
                              <Height>0.6cm</Height>
                              <Width>1.11389cm</Width>
                              <ZIndex>23</ZIndex>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="Textbox15">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!CompanyInfo_Name.Value</Value>
                                      <Style />
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox15</rd:DefaultName>
                              <Top>29.02833cm</Top>
                              <Left>1.4985cm</Left>
                              <Height>0.6cm</Height>
                              <Width>5.31816cm</Width>
                              <ZIndex>24</ZIndex>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="Textbox16">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value />
                                      <Style />
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox16</rd:DefaultName>
                              <Top>29.62833cm</Top>
                              <Left>0.34937cm</Left>
                              <Height>0.86458cm</Height>
                              <Width>6.46729cm</Width>
                              <ZIndex>25</ZIndex>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="Textbox18">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!SobhanMukherjee.Value</Value>
                                      <Style>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox18</rd:DefaultName>
                              <Top>30.52819cm</Top>
                              <Left>0.34933cm</Left>
                              <Height>0.54709cm</Height>
                              <Width>6.46733cm</Width>
                              <ZIndex>26</ZIndex>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="Textbox19">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>UNDERTAKING</Value>
                                      <Style>
                                        <FontWeight>Bold</FontWeight>
                                        <TextDecoration>Underline</TextDecoration>
                                      </Style>
                                    </TextRun>
                                    <TextRun>
                                      <Value>:</Value>
                                      <Style />
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox19</rd:DefaultName>
                              <Top>31.87785cm</Top>
                              <Left>0.34933cm</Left>
                              <Height>0.6cm</Height>
                              <Width>4.09491cm</Width>
                              <ZIndex>27</ZIndex>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="Textbox6">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>="We, "+ Fields!Name.Value +"  hereby confirm that we have read and understood the contents of the above letter, and we agree to be bound and undertake to abide by all the terms set out in this letter."</Value>
                                      <Style />
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox6</rd:DefaultName>
                              <Top>32.51313cm</Top>
                              <Left>0.34933cm</Left>
                              <Height>1.26146cm</Height>
                              <Width>18.41818cm</Width>
                              <ZIndex>28</ZIndex>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="Textbox8">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>="Signed for and on behalf of :  "+Fields!Name.Value</Value>
                                      <Style />
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox8</rd:DefaultName>
                              <Top>33.80986cm</Top>
                              <Left>0.34937cm</Left>
                              <Height>0.75875cm</Height>
                              <Width>17.40527cm</Width>
                              <ZIndex>29</ZIndex>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="Textbox20">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Name:  </Value>
                                      <Style>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                    <TextRun>
                                      <Value>____________________________</Value>
                                      <Style />
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox20</rd:DefaultName>
                              <Top>34.70972cm</Top>
                              <Left>0.34933cm</Left>
                              <Height>0.6cm</Height>
                              <Width>8.74274cm</Width>
                              <ZIndex>30</ZIndex>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="Textbox21">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Name:  </Value>
                                      <Style>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                    <TextRun>
                                      <Value>____________________________</Value>
                                      <Style />
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox20</rd:DefaultName>
                              <Top>34.70972cm</Top>
                              <Left>9.12735cm</Left>
                              <Height>0.6cm</Height>
                              <Width>8.74274cm</Width>
                              <ZIndex>31</ZIndex>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="Textbox22">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Position:   </Value>
                                      <Style>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                    <TextRun>
                                      <Value>____________________________</Value>
                                      <Style />
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox20</rd:DefaultName>
                              <Top>35.30973cm</Top>
                              <Left>0.34933cm</Left>
                              <Height>0.6cm</Height>
                              <Width>8.74274cm</Width>
                              <ZIndex>32</ZIndex>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="Textbox23">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Position:  </Value>
                                      <Style>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                    <TextRun>
                                      <Value>____________________________</Value>
                                      <Style />
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox20</rd:DefaultName>
                              <Top>35.30973cm</Top>
                              <Left>9.12735cm</Left>
                              <Height>0.6cm</Height>
                              <Width>8.74274cm</Width>
                              <ZIndex>33</ZIndex>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="Textbox24">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Signature:   </Value>
                                      <Style>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                    <TextRun>
                                      <Value>____________________________</Value>
                                      <Style />
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox20</rd:DefaultName>
                              <Top>35.945cm</Top>
                              <Left>0.34933cm</Left>
                              <Height>0.6cm</Height>
                              <Width>8.74274cm</Width>
                              <ZIndex>34</ZIndex>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="Textbox25">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Signature:   </Value>
                                      <Style>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                    <TextRun>
                                      <Value>____________________________</Value>
                                      <Style />
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox20</rd:DefaultName>
                              <Top>35.90972cm</Top>
                              <Left>9.12735cm</Left>
                              <Height>0.63528cm</Height>
                              <Width>8.74274cm</Width>
                              <ZIndex>35</ZIndex>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="Textbox26">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Date:   </Value>
                                      <Style>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                    <TextRun>
                                      <Value>____________________________</Value>
                                      <Style />
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox20</rd:DefaultName>
                              <Top>36.58028cm</Top>
                              <Left>0.34933cm</Left>
                              <Height>0.6cm</Height>
                              <Width>8.74274cm</Width>
                              <ZIndex>36</ZIndex>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="Textbox27">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Date:  </Value>
                                      <Style>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                    <TextRun>
                                      <Value>____________________________</Value>
                                      <Style />
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox20</rd:DefaultName>
                              <Top>36.58028cm</Top>
                              <Left>9.12735cm</Left>
                              <Height>0.6cm</Height>
                              <Width>8.74274cm</Width>
                              <ZIndex>37</ZIndex>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="Textbox29">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!NationalSalesDirector.Value</Value>
                                      <Style>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox18</rd:DefaultName>
                              <Top>31.07528cm</Top>
                              <Left>0.34933cm</Left>
                              <Height>0.54709cm</Height>
                              <Width>6.46733cm</Width>
                              <ZIndex>38</ZIndex>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="Textbox32">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!EmailCaptionLbl.Value</Value>
                                      <Style />
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox31</rd:DefaultName>
                              <Top>18.88869cm</Top>
                              <Left>0.3157cm</Left>
                              <Height>0.49417cm</Height>
                              <Width>1.14752cm</Width>
                              <ZIndex>39</ZIndex>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                            <Textbox Name="Textbox46">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Today</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                        <Format>MMMM d, yyyy</Format>
                                        <TextDecoration>None</TextDecoration>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style />
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox45</rd:DefaultName>
                              <Top>0.00001cm</Top>
                              <Left>11.94863cm</Left>
                              <Height>11pt</Height>
                              <Width>6.72357cm</Width>
                              <ZIndex>40</ZIndex>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </ReportItems>
                          <KeepTogether>true</KeepTogether>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                          </Style>
                        </Rectangle>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
              </TablixRows>
            </TablixBody>
            <TablixColumnHierarchy>
              <TablixMembers>
                <TablixMember />
              </TablixMembers>
            </TablixColumnHierarchy>
            <TablixRowHierarchy>
              <TablixMembers>
                <TablixMember>
                  <Group Name="Customer_No">
                    <GroupExpressions>
                      <GroupExpression>=Fields!Customer_No.Value</GroupExpression>
                    </GroupExpressions>
                    <PageBreak>
                      <BreakLocation>Between</BreakLocation>
                    </PageBreak>
                  </Group>
                  <SortExpressions>
                    <SortExpression>
                      <Value>=Fields!Customer_No.Value</Value>
                    </SortExpression>
                  </SortExpressions>
                  <TablixMembers>
                    <TablixMember>
                      <Group Name="Details" />
                    </TablixMember>
                  </TablixMembers>
                </TablixMember>
              </TablixMembers>
            </TablixRowHierarchy>
            <DataSetName>DataSet_Result</DataSetName>
            <Top>0.07054cm</Top>
            <Left>0.15023cm</Left>
            <Height>37.18028cm</Height>
            <Width>18.76751cm</Width>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <TopBorder>
                <Color>Black</Color>
                <Width>1pt</Width>
              </TopBorder>
            </Style>
          </Tablix>
        </ReportItems>
        <Height>37.2861cm</Height>
        <Style />
      </Body>
      <Width>18.91774cm</Width>
      <Page>
        <PageHeader>
          <Height>5.70328cm</Height>
          <PrintOnFirstPage>true</PrintOnFirstPage>
          <PrintOnLastPage>true</PrintOnLastPage>
          <ReportItems>
            <Rectangle Name="Rectangle1">
              <ReportItems>
                <Textbox Name="Textbox2">
                  <CanGrow>true</CanGrow>
                  <KeepTogether>true</KeepTogether>
                  <Paragraphs>
                    <Paragraph>
                      <TextRuns>
                        <TextRun>
                          <Value>=Fields!CompanyInfo_Name.Value</Value>
                          <Style>
                            <FontSize>22pt</FontSize>
                          </Style>
                        </TextRun>
                      </TextRuns>
                      <Style />
                    </Paragraph>
                  </Paragraphs>
                  <rd:DefaultName>Textbox2</rd:DefaultName>
                  <Top>0cm</Top>
                  <Left>12.09886cm</Left>
                  <Height>1.03189cm</Height>
                  <Width>6.7236cm</Width>
                  <Style>
                    <Border>
                      <Style>None</Style>
                    </Border>
                    <PaddingLeft>2pt</PaddingLeft>
                    <PaddingRight>2pt</PaddingRight>
                    <PaddingTop>2pt</PaddingTop>
                    <PaddingBottom>2pt</PaddingBottom>
                  </Style>
                </Textbox>
                <Textbox Name="Textbox3">
                  <CanGrow>true</CanGrow>
                  <KeepTogether>true</KeepTogether>
                  <Paragraphs>
                    <Paragraph>
                      <TextRuns>
                        <TextRun>
                          <Value>=Fields!OfficeAddressCaptionLbl.Value</Value>
                          <Style>
                            <FontSize>9pt</FontSize>
                            <FontWeight>Bold</FontWeight>
                          </Style>
                        </TextRun>
                      </TextRuns>
                      <Style />
                    </Paragraph>
                  </Paragraphs>
                  <rd:DefaultName>Textbox3</rd:DefaultName>
                  <Top>1.57869cm</Top>
                  <Left>12.09887cm</Left>
                  <Height>0.51153cm</Height>
                  <Width>6.7236cm</Width>
                  <ZIndex>1</ZIndex>
                  <Style>
                    <Border>
                      <Style>None</Style>
                    </Border>
                    <PaddingLeft>2pt</PaddingLeft>
                    <PaddingRight>2pt</PaddingRight>
                    <PaddingTop>2pt</PaddingTop>
                    <PaddingBottom>2pt</PaddingBottom>
                  </Style>
                </Textbox>
                <Textbox Name="Textbox1">
                  <CanGrow>true</CanGrow>
                  <KeepTogether>true</KeepTogether>
                  <Paragraphs>
                    <Paragraph>
                      <TextRuns>
                        <TextRun>
                          <Value>=Fields!CompanyInfo_Address.Value</Value>
                          <Style>
                            <FontSize>9pt</FontSize>
                            <FontWeight>Bold</FontWeight>
                          </Style>
                        </TextRun>
                      </TextRuns>
                      <Style />
                    </Paragraph>
                  </Paragraphs>
                  <rd:DefaultName>Textbox1</rd:DefaultName>
                  <Top>2.1255cm</Top>
                  <Left>12.09887cm</Left>
                  <Height>0.51153cm</Height>
                  <Width>6.7236cm</Width>
                  <ZIndex>2</ZIndex>
                  <Style>
                    <Border>
                      <Style>None</Style>
                    </Border>
                    <PaddingLeft>2pt</PaddingLeft>
                    <PaddingRight>2pt</PaddingRight>
                    <PaddingTop>2pt</PaddingTop>
                    <PaddingBottom>2pt</PaddingBottom>
                  </Style>
                </Textbox>
                <Textbox Name="Textbox4">
                  <CanGrow>true</CanGrow>
                  <KeepTogether>true</KeepTogether>
                  <Paragraphs>
                    <Paragraph>
                      <TextRuns>
                        <TextRun>
                          <Value>=Fields!CompanyInfo_Addess2.Value</Value>
                          <Style>
                            <FontSize>9pt</FontSize>
                            <FontWeight>Bold</FontWeight>
                          </Style>
                        </TextRun>
                      </TextRuns>
                      <Style />
                    </Paragraph>
                  </Paragraphs>
                  <rd:DefaultName>Textbox4</rd:DefaultName>
                  <Top>2.63704cm</Top>
                  <Left>12.09887cm</Left>
                  <Height>0.51153cm</Height>
                  <Width>6.7236cm</Width>
                  <ZIndex>3</ZIndex>
                  <Style>
                    <Border>
                      <Style>None</Style>
                    </Border>
                    <PaddingLeft>2pt</PaddingLeft>
                    <PaddingRight>2pt</PaddingRight>
                    <PaddingTop>2pt</PaddingTop>
                    <PaddingBottom>2pt</PaddingBottom>
                  </Style>
                </Textbox>
                <Textbox Name="Textbox5">
                  <CanGrow>true</CanGrow>
                  <KeepTogether>true</KeepTogether>
                  <Paragraphs>
                    <Paragraph>
                      <TextRuns>
                        <TextRun>
                          <Value>TEL: +234-1-903-9000-29,774-0383</Value>
                          <Style>
                            <FontSize>9pt</FontSize>
                            <FontWeight>Bold</FontWeight>
                          </Style>
                        </TextRun>
                      </TextRuns>
                      <Style />
                    </Paragraph>
                  </Paragraphs>
                  <rd:DefaultName>Textbox5</rd:DefaultName>
                  <Top>3.18384cm</Top>
                  <Left>12.09887cm</Left>
                  <Height>0.51153cm</Height>
                  <Width>6.72359cm</Width>
                  <ZIndex>4</ZIndex>
                  <Style>
                    <Border>
                      <Style>None</Style>
                    </Border>
                    <PaddingLeft>2pt</PaddingLeft>
                    <PaddingRight>2pt</PaddingRight>
                    <PaddingTop>2pt</PaddingTop>
                    <PaddingBottom>2pt</PaddingBottom>
                  </Style>
                </Textbox>
                <Textbox Name="Textbox7">
                  <CanGrow>true</CanGrow>
                  <KeepTogether>true</KeepTogether>
                  <Paragraphs>
                    <Paragraph>
                      <TextRuns>
                        <TextRun>
                          <Value>=Fields!WebsiteCaptionLbl.Value+" : "+Fields!CompanyInfo_HomePage.Value</Value>
                          <Style>
                            <FontSize>9pt</FontSize>
                            <FontWeight>Bold</FontWeight>
                          </Style>
                        </TextRun>
                      </TextRuns>
                      <Style />
                    </Paragraph>
                  </Paragraphs>
                  <rd:DefaultName>Textbox5</rd:DefaultName>
                  <Top>4.82427cm</Top>
                  <Left>12.09887cm</Left>
                  <Height>0.51153cm</Height>
                  <Width>6.72359cm</Width>
                  <ZIndex>5</ZIndex>
                  <Style>
                    <Border>
                      <Style>None</Style>
                    </Border>
                    <PaddingLeft>2pt</PaddingLeft>
                    <PaddingRight>2pt</PaddingRight>
                    <PaddingTop>2pt</PaddingTop>
                    <PaddingBottom>2pt</PaddingBottom>
                  </Style>
                </Textbox>
                <Textbox Name="Textbox9">
                  <CanGrow>true</CanGrow>
                  <KeepTogether>true</KeepTogether>
                  <Paragraphs>
                    <Paragraph>
                      <TextRuns>
                        <TextRun>
                          <Value>=Fields!EmailCaptionLbl.Value+" : "+Fields!CompanyInfo_EMail.Value</Value>
                          <Style>
                            <FontSize>9pt</FontSize>
                            <FontWeight>Bold</FontWeight>
                          </Style>
                        </TextRun>
                      </TextRuns>
                      <Style />
                    </Paragraph>
                  </Paragraphs>
                  <rd:DefaultName>Textbox5</rd:DefaultName>
                  <Top>4.20691cm</Top>
                  <Left>12.09887cm</Left>
                  <Height>0.58207cm</Height>
                  <Width>6.72359cm</Width>
                  <ZIndex>6</ZIndex>
                  <Style>
                    <Border>
                      <Style>None</Style>
                    </Border>
                    <PaddingLeft>2pt</PaddingLeft>
                    <PaddingRight>2pt</PaddingRight>
                    <PaddingTop>2pt</PaddingTop>
                    <PaddingBottom>2pt</PaddingBottom>
                  </Style>
                </Textbox>
                <Textbox Name="Textbox11">
                  <CanGrow>true</CanGrow>
                  <KeepTogether>true</KeepTogether>
                  <Paragraphs>
                    <Paragraph>
                      <TextRuns>
                        <TextRun>
                          <Value> FAX: +234-1-271-9265, 01-271-9266</Value>
                          <Style>
                            <FontSize>9pt</FontSize>
                            <FontWeight>Bold</FontWeight>
                          </Style>
                        </TextRun>
                      </TextRuns>
                      <Style />
                    </Paragraph>
                  </Paragraphs>
                  <rd:DefaultName>Textbox5</rd:DefaultName>
                  <Top>3.69538cm</Top>
                  <Left>12.09888cm</Left>
                  <Height>0.51153cm</Height>
                  <Width>6.72358cm</Width>
                  <ZIndex>7</ZIndex>
                  <Style>
                    <Border>
                      <Style>None</Style>
                    </Border>
                    <PaddingLeft>2pt</PaddingLeft>
                    <PaddingRight>2pt</PaddingRight>
                    <PaddingTop>2pt</PaddingTop>
                    <PaddingBottom>2pt</PaddingBottom>
                  </Style>
                </Textbox>
                <Textbox Name="Textbox13">
                  <CanGrow>true</CanGrow>
                  <KeepTogether>true</KeepTogether>
                  <Paragraphs>
                    <Paragraph>
                      <TextRuns>
                        <TextRun>
                          <Value />
                          <Style />
                        </TextRun>
                      </TextRuns>
                      <Style />
                    </Paragraph>
                  </Paragraphs>
                  <rd:DefaultName>Textbox13</rd:DefaultName>
                  <Top>0.51153cm</Top>
                  <Left>0.49953cm</Left>
                  <Height>3.21029cm</Height>
                  <Width>3.16146cm</Width>
                  <ZIndex>8</ZIndex>
                  <Style>
                    <Border>
                      <Style>None</Style>
                    </Border>
                    <PaddingLeft>2pt</PaddingLeft>
                    <PaddingRight>2pt</PaddingRight>
                    <PaddingTop>2pt</PaddingTop>
                    <PaddingBottom>2pt</PaddingBottom>
                  </Style>
                </Textbox>
                <Image Name="Image1">
                  <Source>Database</Source>
                  <Value>=First(Fields!CompanyInfo_Picture.Value, "DataSet_Result")</Value>
                  <MIMEType>image/jpeg</MIMEType>
                  <Sizing>FitProportional</Sizing>
                  <Top>0.51153cm</Top>
                  <Left>0.49953cm</Left>
                  <Height>3.21029cm</Height>
                  <Width>3.16146cm</Width>
                  <ZIndex>9</ZIndex>
                  <Style>
                    <Border>
                      <Style>None</Style>
                    </Border>
                  </Style>
                </Image>
                <Textbox Name="Textbox17">
                  <CanGrow>true</CanGrow>
                  <KeepTogether>true</KeepTogether>
                  <Paragraphs>
                    <Paragraph>
                      <TextRuns>
                        <TextRun>
                          <Value>=Fields!CompanyInfo_RegistrationNo.Value</Value>
                          <Style>
                            <FontSize>8pt</FontSize>
                            <FontWeight>Bold</FontWeight>
                          </Style>
                        </TextRun>
                      </TextRuns>
                      <Style>
                        <TextAlign>Right</TextAlign>
                      </Style>
                    </Paragraph>
                  </Paragraphs>
                  <rd:DefaultName>Textbox2</rd:DefaultName>
                  <Top>1.03189cm</Top>
                  <Left>12.09888cm</Left>
                  <Height>0.51153cm</Height>
                  <Width>6.7236cm</Width>
                  <ZIndex>10</ZIndex>
                  <Style>
                    <Border>
                      <Style>None</Style>
                    </Border>
                    <PaddingLeft>2pt</PaddingLeft>
                    <PaddingRight>2pt</PaddingRight>
                    <PaddingTop>2pt</PaddingTop>
                    <PaddingBottom>2pt</PaddingBottom>
                  </Style>
                </Textbox>
                <Line Name="Line1">
                  <Top>5.63271cm</Top>
                  <Left>0cm</Left>
                  <Height>0cm</Height>
                  <Width>18.82243cm</Width>
                  <ZIndex>11</ZIndex>
                  <Style>
                    <Border>
                      <Style>Solid</Style>
                    </Border>
                  </Style>
                </Line>
              </ReportItems>
              <KeepTogether>true</KeepTogether>
              <Height>2.24539in</Height>
              <Width>7.44793in</Width>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
              </Style>
            </Rectangle>
          </ReportItems>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
          </Style>
        </PageHeader>
        <PageHeight>29.7cm</PageHeight>
        <PageWidth>21cm</PageWidth>
        <LeftMargin>1cm</LeftMargin>
        <RightMargin>0.5cm</RightMargin>
        <TopMargin>0.5cm</TopMargin>
        <BottomMargin>0.5cm</BottomMargin>
        <ColumnSpacing>1.27cm</ColumnSpacing>
        <Style />
      </Page>
    </ReportSection>
  </ReportSections>
  <Code>Public Function BlankZero(ByVal Value As Decimal)
    if Value = 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankPos(ByVal Value As Decimal)
    if Value &gt; 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankZeroAndPos(ByVal Value As Decimal)
    if Value &gt;= 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankNeg(ByVal Value As Decimal)
    if Value &lt; 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankNegAndZero(ByVal Value As Decimal)
    if Value &lt;= 0 then
        Return ""
    end if
    Return Value
End Function
</Code>
  <Language>=User!Language</Language>
  <ConsumeContainerWhitespace>true</ConsumeContainerWhitespace>
  <rd:ReportUnitType>Cm</rd:ReportUnitType>
  <rd:ReportID>0eeb6585-38ae-40f1-885b-8d50088d51b4</rd:ReportID>
  <DataSets>
    <DataSet Name="DataSet_Result">
      <Fields>
        <Field Name="CompanyInfo_Picture">
          <DataField>CompanyInfo_Picture</DataField>
        </Field>
        <Field Name="CompanyInfo_Name">
          <DataField>CompanyInfo_Name</DataField>
        </Field>
        <Field Name="CompanyInfo_Address">
          <DataField>CompanyInfo_Address</DataField>
        </Field>
        <Field Name="CompanyInfo_Addess2">
          <DataField>CompanyInfo_Addess2</DataField>
        </Field>
        <Field Name="CompanyInfo_PhoneNo">
          <DataField>CompanyInfo_PhoneNo</DataField>
        </Field>
        <Field Name="CompanyInfo_PhoneNo2">
          <DataField>CompanyInfo_PhoneNo2</DataField>
        </Field>
        <Field Name="CompanyInfo_RegistrationNo">
          <DataField>CompanyInfo_RegistrationNo</DataField>
        </Field>
        <Field Name="CompanyInfo_FaxNo">
          <DataField>CompanyInfo_FaxNo</DataField>
        </Field>
        <Field Name="CompanyInfo_EMail">
          <DataField>CompanyInfo_EMail</DataField>
        </Field>
        <Field Name="CompanyInfo_HomePage">
          <DataField>CompanyInfo_HomePage</DataField>
        </Field>
        <Field Name="OfficeAddressCaptionLbl">
          <DataField>OfficeAddressCaptionLbl</DataField>
        </Field>
        <Field Name="FAXCaptionLbl">
          <DataField>FAXCaptionLbl</DataField>
        </Field>
        <Field Name="EmailCaptionLbl">
          <DataField>EmailCaptionLbl</DataField>
        </Field>
        <Field Name="TelCaptionLbl">
          <DataField>TelCaptionLbl</DataField>
        </Field>
        <Field Name="WebsiteCaptionLbl">
          <DataField>WebsiteCaptionLbl</DataField>
        </Field>
        <Field Name="AttentionCaptionLbl">
          <DataField>AttentionCaptionLbl</DataField>
        </Field>
        <Field Name="CHILtdCaption">
          <DataField>CHILtdCaption</DataField>
        </Field>
        <Field Name="DearSirCaption">
          <DataField>DearSirCaption</DataField>
        </Field>
        <Field Name="SubjectCaption">
          <DataField>SubjectCaption</DataField>
        </Field>
        <Field Name="EmailName">
          <DataField>EmailName</DataField>
        </Field>
        <Field Name="PhoneNumber">
          <DataField>PhoneNumber</DataField>
        </Field>
        <Field Name="SobhanMukherjee">
          <DataField>SobhanMukherjee</DataField>
        </Field>
        <Field Name="NationalSalesDirector">
          <DataField>NationalSalesDirector</DataField>
        </Field>
        <Field Name="Contact">
          <DataField>Contact</DataField>
        </Field>
        <Field Name="Name">
          <DataField>Name</DataField>
        </Field>
        <Field Name="Address">
          <DataField>Address</DataField>
        </Field>
        <Field Name="Address_2">
          <DataField>Address_2</DataField>
        </Field>
      </Fields>
      <Query>
        <DataSourceName>DataSource</DataSourceName>
        <CommandText />
      </Query>
    </DataSet>
  </DataSets>
</Report>