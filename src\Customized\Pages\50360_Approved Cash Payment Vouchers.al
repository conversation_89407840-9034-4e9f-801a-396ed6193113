page 50360 "Approved Cash Payment Vouchers"
{
    // version CHI6.0

    // PROJECT : CHI 6.0
    // **********************************************************************************
    // SIGN
    // **********************************************************************************
    // CHI     :  CHI 6.0 Developments
    // UNL     :  Univision Nigeria Limited
    // SAA     :  SAHEED ADIO ADEOSUN
    // **********************************************************************************
    // VER      SIGN         DATE   DESCRIPTION
    // **********************************************************************************
    // 1.0      UNL      06-Dec-11   -> Form Created to display Cash Payment Document Details.
    // 
    // 3.0      SAA      26-Dec-12   -> Added new Function "CheckHeaderLines" to the table.
    //                               -> Code added OnPush for Menu Item "Send Approval Request" to run function "CheckHeaderLines".
    // 
    //                   08-Mar-12   -> Added codes to "Form-OnOpenForm" to filter out Responsibility Centres in Vouchers.
    //                               ->  Added the "Responsibility Centre" field to the form

    Caption = 'Approved Cash Payment Vouchers';
    DeleteAllowed = false;
    Editable = false;
    PageType = Document;
    SourceTable = "Voucher Header";
    SourceTableView = SORTING("Voucher Type", "Document No.")
                      WHERE("Voucher Type" = CONST(CPV),
                            Status = FILTER(Released),
                            "Cash Paid" = FILTER(true));
    UsageCategory = Documents;
    ApplicationArea = all;

    layout
    {
        area(content)
        {
            group(General)
            {
                Caption = 'General';
                field("Document No."; "Document No.")
                {
                    ApplicationArea = all;
                    Editable = false;

                    trigger OnAssistEdit();
                    begin
                        if AssistEdit(xRec) then
                            CurrPage.UPDATE;
                    end;
                }
                field("Posting Date"; "Posting Date")
                {
                    ApplicationArea = all;
                    Editable = false;

                    trigger OnValidate();
                    begin
                        // SAA 3.0 >>
                        PostingDateValidate;
                        // SAA 3.0 <<
                    end;
                }
                field("Transaction Type"; "Transaction Type")
                {
                    ApplicationArea = all;
                    Editable = false;

                    trigger OnValidate();
                    begin
                        if "Transaction Type" <> "Transaction Type"::Import then
                            "Import File No." := '';
                        if "Transaction Type" <> "Transaction Type"::" " then
                            TESTFIELD("Account No.", '');
                    end;
                }
                field("Import File No."; "Import File No.")
                {
                    ApplicationArea = all;
                    Editable = false;

                    trigger OnValidate();
                    begin
                        if "Import File No." <> '' then
                            TESTFIELD("Transaction Type", "Transaction Type"::Import);
                    end;
                }
                field("Clearing File No."; "Clearing File No.")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Account No."; "Account No.")
                {
                    ApplicationArea = all;
                    Caption = 'Credit Account No.';
                    Editable = false;

                    trigger OnValidate();
                    begin
                        TestTransactionType;
                    end;
                }
                field("Account Name"; "Account Name")
                {
                    ApplicationArea = all;
                }
                field(Narration; Narration)
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Currency Code"; "Currency Code")
                {
                    ApplicationArea = all;
                    Editable = false;

                    trigger OnAssistEdit();
                    begin
                        ChangeExchangeRate.SetParameter("Currency Code", "Currency Factor", "Posting Date");
                        if ChangeExchangeRate.RUNMODAL = ACTION::OK then begin
                            VALIDATE("Currency Factor", ChangeExchangeRate.GetParameter);
                        end;
                        CLEAR(ChangeExchangeRate);
                    end;
                }
                field("Amount (LCY)"; "Amount (LCY)")
                {
                    ApplicationArea = all;
                }
                field("Shortcut Dimension 1 Code"; "Shortcut Dimension 1 Code")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Shortcut Dimension 2 Code"; "Shortcut Dimension 2 Code")
                {
                    ApplicationArea = all;
                }
                field("Cash Requisition Slip No."; "Cash Requisition Slip No.")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Responsibility Center"; "Responsibility Center")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field(Status; Status)
                {
                    ApplicationArea = all;
                }
            }
            part(VoucherLines; "Approvd Cash Pmt. Vch. Subform")
            {
                ApplicationArea = all;
                SubPageLink = "Journal Template Name" = FIELD("Journal Template Code"),
                              "Journal Batch Name" = FIELD("Journal Batch Name"),
                              "Document No." = FIELD("Document No.");
            }
            group(Usertrail)
            {
                Caption = 'Usertrail';
                field("Created By"; "Created By")
                {
                    ApplicationArea = all;
                }
                field("Created By Name"; "Created By Name")
                {
                    ApplicationArea = all;
                }
                field("Created Date"; "Created Date")
                {
                    ApplicationArea = all;
                }
                field("Created Time"; "Created Time")
                {
                    ApplicationArea = all;
                }
                field("Modified By"; "Modified By")
                {
                    ApplicationArea = all;
                }
                field("Modified By Name"; "Modified By Name")
                {
                    ApplicationArea = all;
                }
                field("Modified Date"; "Modified Date")
                {
                    ApplicationArea = all;
                }
                field("Modified Time"; "Modified Time")
                {
                    ApplicationArea = all;
                }
            }
            group("Payment Details")
            {
                Caption = 'Payment Details';
                field(ToBeCollectedBy; ToBeCollectedBy)
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field(PaymentSettlementOf; PaymentSettlementOf)
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Payable To"; "Payable To")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Payable Code"; "Payable Code")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Payable Name"; "Payable Name")
                {
                    ApplicationArea = all;
                }
                field("Cash Payments Options"; "Cash Payments Options")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Cash Paid"; "Cash Paid")
                {
                    ApplicationArea = all;
                    Editable = false;

                    trigger OnValidate();
                    begin

                        if Status = Status::Open then
                            ERROR(Text50200);

                        //GLsetup.GET;

                        if not "Cash Paid" then
                            ERROR(Text50201);
                        GLsetup.GET;
                        if (TODAY - "Posting Date") <= GLsetup."Voucher Half Life" then begin
                            //IF  "Cash Paid" THEN
                            //PermCodeUnit.CheckAutority(USERID,609);
                            if CONFIRM('Do You Want to Print this CPV Slip ?\', true) then begin
                                "Cash Paid By" := USERID;
                                "Cash Paid On" := CURRENTDATETIME;
                                SETRECFILTER;
                                REPORT.RUN(50290, true, true, Rec);
                            end else begin
                                "Cash Paid" := false;
                                "Cash Paid By" := '';
                                "Cash Paid On" := 0DT;
                            end

                        end else
                            ERROR(Text50208);
                    end;
                }
                field("Cash Paid By"; "Cash Paid By")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Cash Paid On"; "Cash Paid On")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("ABS(""Amount (LCY)"")"; ABS("Amount (LCY)"))
                {
                    ApplicationArea = all;
                    Caption = 'Amount Paid';
                }
                field("Reprint CPV Slip"; "Reprint CPV Slip")
                {
                    ApplicationArea = all;
                    Editable = false;

                    trigger OnValidate();
                    begin
                        //PermCodeUnit.CheckAutority(USERID,609);
                        if "Reprint CPV Slip" then begin
                            if "Cash Paid" then begin
                                if CONFIRM(Text50202, true) then begin
                                    "Reprinted By" := USERID;
                                    "Reprinted On" := CURRENTDATETIME;
                                    SETRECFILTER;
                                    REPORT.RUN(50290, false, false, Rec);
                                end else begin
                                    "Reprint CPV Slip" := false;
                                end;
                            end else
                                ERROR(Text50205);
                        end else
                            "Reprint CPV Slip" := false;
                    end;
                }
                field("Reprinted By"; "Reprinted By")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
                field("Reprinted On"; "Reprinted On")
                {
                    ApplicationArea = all;
                    Editable = false;
                }
            }
        }
        area(FactBoxes)
        {
            part("Attached Documents"; "Document Attachment Factbox")
            {
                ApplicationArea = All;
                Caption = 'Attachments';
                SubPageLink = "Table ID" = CONST(50117),
                                "No." = FIELD("Document No.");
                // Type = FIELD("Voucher Type");
            }
            systempart(PyamentTermsLinks; Links)
            {
                ApplicationArea = RecordLinks;
            }
            systempart(PyamentTermsNotes; Notes)
            {
                ApplicationArea = Notes;
            }
        }
        //g2s29Dev23
    }

    actions
    {
        area(navigation)
        {
            group("&Voucher")
            {
                Caption = '&Voucher';
                separator(Separator1000000067)
                {
                }
                action(Dimensions)
                {
                    ApplicationArea = all;
                    Caption = 'Dimensions';

                    trigger OnAction();
                    begin
                        ShowDocDim;
                    end;
                }
                separator(Separator1000000069)
                {
                }
                action(Approvals)
                {
                    ApplicationArea = all;
                    Caption = 'Approvals';

                    trigger OnAction();
                    begin
                        ApprovalEntries.Setfilters(DATABASE::"Voucher Header", 22, "Document No.");
                        ApprovalEntries.RUN;
                    end;
                }
            }
            group("F&unctions")
            {
                Caption = 'F&unctions';
                action("Send A&pproval Request")
                {
                    ApplicationArea = all;
                    Caption = 'Send A&pproval Request';
                    Visible = false;

                    trigger OnAction();
                    begin
                        CheckHeaderLines(Rec);
                        //IF ApprovalMgt.SendVoucherApprovalRequest(Rec) THEN;//CHI2018
                    end;
                }
                action("Cancel Approval Re&quest")
                {
                    ApplicationArea = all;
                    Caption = 'Cancel Approval Re&quest';
                    Visible = false;

                    trigger OnAction();
                    begin
                        //IF ApprovalMgt.CancelVoucherApprovalRequest(Rec,TRUE,TRUE) THEN;//CHI2018
                    end;
                }
                separator(Separator1000000049)
                {
                }
                action("Re&lease")
                {
                    ApplicationArea = all;
                    Caption = 'Re&lease';
                    ShortCutKey = 'Ctrl+F11';
                    Visible = false;

                    trigger OnAction();
                    begin
                        CheckHeaderLines(Rec);
                        PerformManualRelease;
                    end;
                }
                action("Re&open")
                {
                    ApplicationArea = all;
                    Caption = 'Re&open';

                    trigger OnAction();
                    begin
                        // SAA 3.0 >>
                        if "Cash Paid" then
                            ERROR(Text50207)
                        Else begin
                            RecordRest.Reset();
                            RecordRest.SetRange(ID, 50117);
                            RecordRest.SetRange("Record ID", Rec.RecordId());
                            IF RecordRest.FindFirst() THEN
                                error('This record is under in workflow process. Please cancel approval request if not required.');
                            IF Status <> Status::Open then BEGIN
                                Status := Status::Open;
                                Modify();
                                Message('Document has been Reopened.');
                            end;
                        end;
                        // SAA 3.0 <<

                    end;
                }
            }
            group("P&osting")
            {
                Caption = 'P&osting';
                action("P&ost")
                {
                    ApplicationArea = all;
                    Caption = 'P&ost';
                    ShortCutKey = 'F11';
                    Visible = false;

                    trigger OnAction();
                    var
                        GeneralJrnlBtchLRec: Record "Gen. Journal Batch";
                        DocAttmtExt: Codeunit "Document Attachment Ext";
                    begin
                        if not "Cash Paid" then
                            ERROR(Text50203);
                        GeneralJrnlBtchLRec.Reset();
                        GeneralJrnlBtchLRec.SetRange("Journal Template Name", 'CPV');
                        GeneralJrnlBtchLRec.SetRange(Name, 'CPV');
                        if GeneralJrnlBtchLRec.FindFirst() then begin
                            GeneralJrnlBtchLRec.TestField("No. Series", '');
                            GeneralJrnlBtchLRec.TestField("Posting No. Series", '');
                        end;
                        VoucherPost.RUN(Rec);
                        //voucherpost.pre
                        //g2s>>>>> TransferAttachment  150124 `
                        IF DocAttmtExt.IsAttachmentsEnabled() THEN BEGIN
                            MoveAttachmentPage.MoveAttachment(Rec);

                            MoveAttachmentPage.CopyLinksAndNotes(Rec);
                        END;
                        //g2s>>>>>>>>>>>>>>>>>>>> 150124
                    end;
                }
                action(Preview)
                {
                    ApplicationArea = all;
                    Caption = 'Preview';
                    ShortCutKey = 'Shift+F2';

                    trigger OnAction();
                    var
                        GeneralJrnlBtchLRec: Record "Gen. Journal Batch";
                        DocumentNo: Code[20];
                        GLRegGRec: Record "G/L Register";
                        genJounlin: Record "Gen. Journal Line 2";
                        VoucherPreview: Codeunit "Voucher Preview Posting";
                    begin
                        DocumentNo := "Document No.";
                        ClearValues();
                        VoucherPreview.RUN(Rec);
                    end;
                }
                action("Post and &Print")
                {
                    ApplicationArea = all;
                    Caption = 'Post and &Print';
                    ShortCutKey = 'Shift+F11';

                    trigger OnAction();
                    var
                        GeneralJrnlBtchLRec: Record "Gen. Journal Batch";
                        DocAttmtExt: Codeunit "Document Attachment Ext";
                    begin
                        //PermCodeUnit.CheckAutority(USERID,157);
                        if not "Cash Paid" then
                            ERROR(Text50203);
                        GeneralJrnlBtchLRec.Reset();
                        GeneralJrnlBtchLRec.SetRange("Journal Template Name", 'CPV');
                        GeneralJrnlBtchLRec.SetRange(Name, 'CPV');
                        if GeneralJrnlBtchLRec.FindFirst() then begin
                            GeneralJrnlBtchLRec.TestField("No. Series", '');
                            GeneralJrnlBtchLRec.TestField("Posting No. Series", '');
                        end;
                        REPORT.Run(50290, true, false, Rec);
                        VoucherPost.RUN(Rec);
                        //g2s>>>>> TransferAttachment  150124 `
                        IF DocAttmtExt.IsAttachmentsEnabled() THEN BEGIN
                            MoveAttachmentPage.MoveAttachment(Rec);

                            MoveAttachmentPage.CopyLinksAndNotes(Rec);
                        END;
                        //g2s>>>>>>>>>>>>>>>>>>>> 150124
                    end;
                }
                action("Cash Payment Voucher Test Report")
                {
                    trigger OnAction()
                    var
                        VouHeader: Record "Voucher Header";
                    BEGIN
                        VouHeader.RESET;
                        VouHeader.SETRANGE("Voucher Type", "Voucher Type");
                        VouHeader.SETRANGE("Document No.", "Document No.");
                        if VouHeader.FINDFIRST then
                            REPORT.RUN(50563, true, false, VouHeader);
                    END;
                }
                //Balu 05282021>>
                action("Open Excel")
                {
                    ApplicationArea = all;
                    Caption = 'Open Excel';
                    Image = Open;
                    trigger OnAction()
                    var
                        GlLine2: Record "Gen. Journal Line 2";
                    begin
                        GlLine2.CreateExcel(Rec);
                    end;
                }
                //Balu 05282021<<

                /*action(Preview)
                {
                    APplicationArea = All;
                    Caption = 'Preview';
                    trigger onaction()
                    var
                        GenJnlPost: Codeunit "Gen. Jnl.-Post2";
                        GenJoulne: Record "Gen. Journal Line";
                    BEGIN
                        GenJoulne.Reset();
                        GenJoulne.SetRange("Journal Template Name", "Journal Template Code");
                        GenJoulne.SetRange("Journal Batch Name", "Journal Batch Name");
                        GenJoulne.SetRange("Document No.", "Document No.");
                        IF GenJoulne.findset then;
                        GenJnlPost.Preview(GenJoulne);
                    END;
                }*/
            }
        }
        area(processing)
        {
            action("&Print")
            {
                ApplicationArea = all;
                Caption = '&Print';
                Ellipsis = true;
                Promoted = true;
                PromotedCategory = Process;
                trigger OnAction();
                begin
                    VoucherHeader.reset;
                    //VoucherHeader.SETRANGE("Voucher Type", "Voucher Type");
                    VoucherHeader.SETRANGE("Document No.", "Document No.");
                    if VoucherHeader.FINDFIRST then
                        //REPORT.RUN(50192,TRUE,FALSE,VoucherHeader);
                        REPORT.RUN(50495, true, false, VoucherHeader);
                end;
            }
        }
    }

    trigger OnNewRecord(BelowxRec: Boolean);
    begin
        "Dim. Document Type" := "Dim. Document Type"::CPV;
        // SAA 3.0 >>
        "Responsibility Center" := UserMg.GetVoucherFilter();
        // SAA 3.0 <<
    end;

    trigger OnOpenPage();
    begin
        // SAA 3.0 >>
        if UserMg.GetVoucherFilter() <> '' then begin
            FILTERGROUP(2);
            SETRANGE("Responsibility Center", UserMg.GetVoucherFilter());
            FILTERGROUP(0);
        end;
        // SAA 3.0 <<
    end;

    var
        VoucherHeader: Record "Voucher Header";
        ApprovalMgt: Codeunit 1535;
        ApprovalEntry: Record "Approval Entry";
        ReleaseVoucher: Codeunit "Voucher Release";
        ApprovalEntries: Page 658;
        ChangeExchangeRate: Page "Change Exchange Rate";
        GenJrnlLine: Record "Gen. Journal Line";
        ReportPrint: Codeunit "Test Report-Print";
        VoucherPost: Codeunit "Voucher Post";
        Vendor: Record Vendor;
        Customer: Record Customer;
        Employee: Record Employee;
        Bank: Record "Bank Account";
        Text50200: Label 'Status must be Released to pay Cash.';
        Text50201: Label 'You have already Printed the CPV Slip, you cannot Untick';
        Text50202: Label 'Do You Want to RePrint this CPV Slip ?\';
        Text50203: Label 'Cash Paid is False, hence you cannot Post.';
        Text50204: Label 'To be collected by must not be Blank';
        Text50205: Label 'You cannot Reprint while Cash Paid is False.';
        Text50206: Label 'Cash payment options must not be blank';
        Text50207: Label 'You cannot ReOpen Document while Cash Paid is True.';
        UserMgt: Codeunit "User Setup Management";
        UserMg: Codeunit "User Setup Management Ext";
        GLsetup: Record "General Ledger Setup";
        RecordRest: Record "Restricted Record";
        Text50208: Label 'This Voucher is long overdue and cannot be paid, Reopen the voucher for review.';
        Text50209: Label 'You you want to post the voucher with current date?';

    procedure CheckHeaderLines(VoucherHeaderRec: Record "Voucher Header");
    var
        GenJnlLine: Record "Gen. Journal Line 2";//PK-GJ2
        VendorLedgerEntry: Record "Vendor Ledger Entry";
    begin
        with VoucherHeaderRec do begin

            TESTFIELD(Narration);
            TESTFIELD("Responsibility Center");

            if ToBeCollectedBy = '' then
                ERROR(Text50204);

            if "Cash Payments Options" = 0 then
                ERROR(Text50206);

            TESTFIELD("Account No.");
            TESTFIELD("Shortcut Dimension 2 Code");
            TESTFIELD("Shortcut Dimension 1 Code");
            //TESTFIELD(Narration);
            TESTFIELD(PaymentSettlementOf);

            TESTFIELD("Payable To");
            if "Payable To" <> "Payable To"::Others then
                TESTFIELD("Payable Code");
            TESTFIELD("Posting Date");
            TESTFIELD("Cash Requisition Slip No.");

            GenJnlLine.SETRANGE(GenJnlLine."Journal Template Name", "Journal Template Code");
            GenJnlLine.SETRANGE(GenJnlLine."Journal Batch Name", "Journal Batch Name");
            GenJnlLine.SETRANGE(GenJnlLine."Document No.", "Document No.");
            if GenJnlLine.FIND('-') then begin
                repeat
                    GenJnlLine.TESTFIELD("Account No.");
                    GenJnlLine.TESTFIELD("Posting Date");
                    GenJnlLine.TESTFIELD(Amount);
                    GenJnlLine.TESTFIELD("Shortcut Dimension 1 Code");
                    GenJnlLine.TESTFIELD("Shortcut Dimension 2 Code");
                    GenJnlLine.TESTFIELD("Description 2");
                    if GenJnlLine."Account Type" = GenJnlLine."Account Type"::Vendor then begin
                        Vendor.GET(GenJnlLine."Account No.");
                        GenJnlLine.TESTFIELD("External Document No.");
                        if (Vendor."Service Group" = Vendor."Service Group"::Supplier) or
                          (Vendor."Service Group" = Vendor."Service Group"::Contractor) then begin
                            GenJnlLine.TESTFIELD("Vendor Payment Type");
                            if GenJnlLine."Vendor Payment Type" = GenJnlLine."Vendor Payment Type"::Advance then
                                GenJnlLine.TESTFIELD("LPO No.");
                            //GenJnlLine.TESTFIELD("Applies-to Doc. Type");
                            //GenJnlLine.TESTFIELD("Applies-to Doc. No.");

                            if GenJnlLine."Applies-to Doc. Type" = 0 then
                                ERROR('Applies-to Doc. Type must not be blank for this %1 Vendor', Vendor."Service Group");

                            if GenJnlLine."Applies-to Doc. No." = '' then
                                ERROR('Applies-to Doc. No. must not be blank for this %1 Vendor', Vendor."Service Group");

                        end;
                        VendorLedgerEntry.SETCURRENTKEY("Global Dimension 1 Code", "Vendor No.", "Vendor Posting Group");
                        VendorLedgerEntry.SETRANGE("Global Dimension 1 Code", GenJnlLine."Shortcut Dimension 1 Code");
                        VendorLedgerEntry.SETRANGE("Vendor No.", GenJnlLine."Account No.");
                        VendorLedgerEntry.SETRANGE("Vendor Posting Group", GenJnlLine."Posting Group");
                        VendorLedgerEntry.SETRANGE(VendorLedgerEntry.Open, true);
                        if VendorLedgerEntry.FIND('-') then
                            if not CONFIRM('YOU HAVE ADVANCES NOT ADJUSTED AGAINST THIS VENDOR. \DO YOU WANT TO PASS THE PAYMENT?', true) then
                                REPORT.RUNMODAL(/*"Unadjusted Vendor Debit Bal."*/50204, true, false, VendorLedgerEntry)
                            else
                                REPORT.RUNMODAL(50204, true, false, VendorLedgerEntry);
                    end;

                    GenJnlLine."Posting Date" := "Posting Date";

                    if GenJnlLine."Account Type" = GenJnlLine."Account Type"::"Fixed Asset" then
                        GenJnlLine.TESTFIELD("FA Posting Type");
                    if GenJnlLine."FA Posting Type" = GenJnlLine."FA Posting Type"::Maintenance then
                        GenJnlLine.TESTFIELD("Maintenance Code");

                    if GenJnlLine."IC Partner Code" <> '' then
                        GenJnlLine.TESTFIELD("IC Partner G/L Acc. No.");


                    GenJnlLine.MODIFY;
                until GenJnlLine.NEXT = 0;
            end;
        end;

    end;

    procedure PostingDateValidate();
    var
        Text50201: Label 'Posting Date must not be a future date';
    begin
        if "Posting Date" > TODAY then
            ERROR(Text50201);
    end;

    procedure TestTransactionType();
    begin
        if "Account No." <> '' then begin
            TESTFIELD("Transaction Type");
            if "Transaction Type" = "Transaction Type"::Import then begin
                TESTFIELD("Import File No.");
                TESTFIELD("Clearing File No.");
            end;
        end;
    end;

    var
        MoveAttachmentPage: Page "Approved General JVs";
}

